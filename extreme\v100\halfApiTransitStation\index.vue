<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import { fetchHalfApiProduct } from '@/apis/common-2'
import Header from '@/components/header/header-hyh.vue'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'

export default {
  name: 'halfApiTransitStation',
  components: { Declaration, Header },

  data() {
    return {
      form: {
        consumerId: '',
        firstFlag: '',
        templateVersion: 'v100'
      },
      fetchHalfApiProductLoading: true,
      fetchHalfApiProductProgress: 0,
      progressTimer: null
    }
  },

  onLoad({ param, consumerId, first }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (first) {
      this.form.firstFlag = first
    }

    if (consumerId) {
      this.form.consumerId = decryptByDES(decodeURIComponent(consumerId))
      this.$u.vuex('vuex_consumerId', this.form.consumerId)
      this.navigateToNextPage()
    } else {
      this.navigateToEnd()
    }
  },

  onUnload() {
    this.progressDone()
  },

  methods: {
    async fetchHalfApiProduct() {
      const res = await fetchHalfApiProduct(this.form)
      if (res.data) {
        return res.data
      }
    },

    async navigateToNextPage() {
      try {
        this.updateProgress(10 * 1000)

        const url = await this.fetchHalfApiProduct()
        if (url) {
          window.location.href = url
          return
        }

        const routeMap = {
          offline: '/extreme/v100/apply/index',
          wechat: '/extreme/v100/qw/index',
          overloan: '/extreme/v100/applyOther/index',
          end: '/extreme/v100/download/index'
        }

        const nextFlow = await nextFlowNode({
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        })

        if (nextFlow.flow === 'halfapi') {
          window.location.href = nextFlow.url
          return
        }

        const path = routeMap[nextFlow.flow]
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `${path}?param=${urlParamString}`
        })
      } catch (e) {
        this.navigateToEnd()
      } finally {
        this.progressDone()
      }
    },

    progressDone() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      this.fetchHalfApiProductProgress = 100
    },

    updateProgress(duration) {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }

      const startTime = Date.now()
      const startProgress = this.fetchHalfApiProductProgress
      const targetProgress = 100

      this.progressTimer = setInterval(() => {
        const currentTime = Date.now()
        const elapsedTime = currentTime - startTime

        if (elapsedTime >= duration) {
          this.fetchHalfApiProductProgress = targetProgress
          clearInterval(this.progressTimer)
        } else {
          const progress = this.easeInOutQuad(
            elapsedTime,
            startProgress,
            targetProgress - startProgress,
            duration
          )
          this.fetchHalfApiProductProgress = Math.round(progress)
        }
      }, 50) // 每50毫秒更新一次进度
    },

    // easeInOutQuad 缓动函数
    easeInOutQuad(t, b, c, d) {
      t /= d / 2
      if (t < 1) return (c / 2) * t * t + b
      t--
      return (-c / 2) * (t * (t - 2) - 1) + b
    },

    navigateToEnd() {
      uni.navigateTo({
        url: `/extreme/v100/download/index?param=${encodeURIComponent(encryptByDES(JSON.stringify(this.form)))}&result=2`
      })
    }
  }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="fetchHalfApiProductLoading" v-if="fetchHalfApiProductLoading">
      <view class="fetchHalfApiProductLoading-content">
        <view class="icon-loading">
          <image
            class="out-img"
            src="https://cdn.oss-unos.hmctec.cn/common/path/c46718c0c9e14ecc8f384b38bc548cb9.png"
          />
          <image
            class="inner-img"
            src="https://cdn.oss-unos.hmctec.cn/common/path/2f939835ffa34f1195ad696cb36e41b2.png"
          />
        </view>

        <view class="tips-1">尊敬的用户，您已完成资料填写</view>
        <view class="tips-2">正在为您评估额度和匹配服务机构，请耐心等待</view>
      </view>
    </view>

    <view class="footer">
      <view class="text">正在加载 ({{ fetchHalfApiProductProgress }}%)</view>
      <view class="process">
        <view class="inner" :style="{ width: `${fetchHalfApiProductProgress}%` }"></view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}
.page-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #e5ffff 0%, #ffffff 21%);
}

.fetchHalfApiProductLoading {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .fetchHalfApiProductLoading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon-loading {
      position: relative;
      width: 420rpx;
      height: 420rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .out-img {
        position: absolute;
        width: 100%;
        height: 100%;
        animation: rotate 1s linear infinite;
      }

      .inner-img {
        width: 200rpx;
        height: 200rpx;
      }
    }
  }
}

.tips-1 {
  margin-top: 20rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
  text-align: center;
}

.tips-2 {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 35rpx;
  text-align: center;
}

.footer {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .process {
    width: 300rpx;
    height: 18rpx;
    border-radius: 300rpx;
    //border: 1rpx solid #1ED1FF;
    display: flex;
    //padding: 4rpx;
    overflow: hidden;
    background-color: #f1f1f1;

    .inner {
      background-color: #1ed1ff;
      border-radius: 300rpx;
      transition: width 0.5s;
    }
  }

  .text {
    margin-bottom: 10rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #1ed1ff;
    line-height: 35rpx;
  }
}

// 旋转动画
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
