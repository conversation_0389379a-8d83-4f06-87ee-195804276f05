<template>
  <uni-popup ref="popup" type="center" :mask-click="false" mask-background-color="#FFFFFF">
    <view class="wait-loading-container">
      <view class="loading-container">
        <image
          class="loading-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/9ad73f91229349daaf374af23b0bb5e5.png"
          mode="aspectFit|aspectFill|widthFix"
          lazy-load="false"
          binderror=""
          bindload=""
        >
        </image>
      </view>

      <view class="loading-text">尊敬的用户，您已完成认证</view>
      <view class="loading-desc">正在为您匹配服务机构...</view>
      <view class="loading-buttons">
        <view
          v-for="(item, index) in checkSteps"
          :key="index"
          class="loading-button"
          :class="{
            'loading-button-primary': item.complete,
            'loading-button-secondary': !item.complete
          }"
        >
          <text>{{ item.text }}</text>
          <view class="status-indicator">
            <view v-if="!item.complete" class="loading-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
            <view v-else class="check-mark">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.98438 1.94336C5.50781 1.94336 1.87891 5.55664 1.87891 10.0137C1.87891 14.4707 5.50781 18.084 9.98438 18.084C14.4609 18.084 18.0898 14.4707 18.0898 10.0137C18.0898 5.55664 14.4609 1.94336 9.98438 1.94336ZM9.98438 16.9629C6.13086 16.9629 3.00586 13.8516 3.00586 10.0156C3.00586 6.17773 6.13086 3.06836 9.98438 3.06836C13.8379 3.06836 16.9629 6.17969 16.9629 10.0156C16.9609 13.8516 13.8379 16.9629 9.98438 16.9629Z"
                  fill="white"
                />
                <path
                  d="M5.73044 10.5743C5.57224 10.4297 5.55856 10.1817 5.7031 10.0235L5.88474 9.8223C6.02927 9.66409 6.27732 9.65042 6.43747 9.79495L8.5488 11.7129C8.707 11.8575 8.957 11.8457 9.09958 11.6856L13.4863 6.8555C13.6308 6.6973 13.8789 6.68363 14.0371 6.82816L14.2363 7.0098C14.3945 7.15433 14.4082 7.40238 14.2636 7.56253L9.1738 13.1739C9.02927 13.3321 8.78122 13.3457 8.62302 13.2012L5.73044 10.5743Z"
                  fill="white"
                />
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'WaitPopup',
  data() {
    return {
      duration: 10 * 1000, // 默认 10 秒
      autoClose: false,
      checkSteps: [
        { text: '身份信息检测', loading: false, complete: false },
        { text: '基本信息检测', loading: false, complete: false },
        { text: '信用信息检测', loading: false, complete: false }
      ]
    }
  },

  methods: {
    open(duration, options = {}) {
      this.duration = duration || this.duration
      this.autoClose = options.autoClose || this.autoClose
      this.resetSteps()
      this.$refs.popup.open()
      this.startStepsAnimation()
    },
    resetSteps() {
      this.checkSteps.forEach((step) => {
        step.loading = false
        step.complete = false
      })
    },
    startStepsAnimation() {
      const stepsCount = this.checkSteps.length
      const stepDuration = this.duration / stepsCount // 根据步骤数量平均分配时间

      const runStep = (index) => {
        if (index >= stepsCount) {
          if (this.autoClose) {
            this.close()
          }
          return
        }

        this.checkSteps[index].loading = true
        setTimeout(() => {
          this.checkSteps[index].complete = true
          runStep(index + 1)
        }, stepDuration)
      }

      // 开始执行第一步
      runStep(0)
    },
    close() {
      this.$refs.popup.close()
    }
  },

  beforeDestroy() {}
}
</script>

<style lang="scss" scoped>
.wait-loading-container {
  transform: translateY(-200rpx);
}

.loading-container {
  width: 582rpx;
  height: 514rpx;
  background: url('https://cdn.oss-unos.hmctec.cn/common/path/61fa0e7399ab4c2086bfda35eac73114.png')
    no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-image {
    width: 94.28rpx;
    height: 95.89rpx;
    animation: orbit 3s linear infinite;
    position: relative;
  }
}

@keyframes orbit {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(40rpx, -30rpx);
  }
  50% {
    transform: translate(80rpx, 0);
  }
  75% {
    transform: translate(40rpx, 30rpx);
  }
  100% {
    transform: translate(0, 0);
  }
}
.loading-text {
  margin-top: 22rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #ffc131;
  line-height: 38rpx;
  text-align: center;
}

.loading-desc {
  margin-bottom: 100rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 28rpx;
  text-align: center;
}

.loading-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;

  .loading-button {
    width: 400rpx;
    height: 72rpx;
    padding: 20rpx 30rpx;
    border-radius: 16rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 33rpx;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20rpx;
    transition: background-color 0.3s ease-in-out;

    .status-indicator {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .loading-dots {
      display: flex;
      gap: 6rpx;

      .dot {
        width: 8rpx;
        height: 8rpx;
        background-color: #ffffff;
        border-radius: 50%;
        animation: dotBounce 1s infinite;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }

    .check-mark {
      display: flex;
      justify-content: center;
      align-items: center;
      animation: checkAppear 0.3s ease-in-out;

      svg {
        width: 40rpx;
        height: 40rpx;
      }
    }

    &-primary {
      background: #ffc131;
    }
    &-secondary {
      background: #f4e3ad;
    }
  }
}

@keyframes dotBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6rpx);
  }
}

@keyframes checkAppear {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
