<template>
  <view class="description">
    <view class="description-title">查询说明：</view>
    <view class="description-content"
      >上述服务方可能根据业务实际发展情况调整。
      如您已经申请大额好借产品，请以平台的申请记录查询结果为准。</view
    >
  </view>
</template>

<script>
export default {};
</script>

<style lang="scss" scoped>
.description {
  margin: 50rpx 20rpx 0;
  background: #F8FCFF;
  border-radius: 8rpx;

  .description-title {
    padding: 35rpx 35rpx 20rpx;
    font-weight: 500;
    font-size: 28rpx;
    color: #333333;
    line-height: 41rpx;
  }

  .description-content {
    padding: 0 35rpx 40rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 42rpx;
  }
}
</style> 