<script>
export default {
  name: 'header-custom'
}
</script>

<template>
  <view class="header-container">
    <image class="logo" :src="vuex_templateConfig.logo" v-if="vuex_templateConfig.logo" />
    <view class="text-container">
      <view class="name" v-if="vuex_templateConfig.platformName">{{
        vuex_templateConfig.platformName
      }}</view>
      <view
        class="line"
        v-if="vuex_templateConfig.platformName && vuex_templateConfig.platformSlogan"
      ></view>
      <view class="desc" v-if="vuex_templateConfig.platformSlogan">{{
        vuex_templateConfig.platformSlogan
      }}</view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.header-container {
  width: fit-content;
  position: relative;
  padding: 30rpx;
  display: flex;
  gap: 10rpx;
  align-items: center;

  .logo {
    width: 62rpx;
    height: 62rpx;
  }

  .text-container {
    display: flex;
    align-items: center;
    gap: 10rpx;
  }

  .name {
    margin-bottom: 4rpx;
    font-family:
      <PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON>;
    font-weight: 700;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 34rpx;
  }

  .line {
    width: 1rpx;
    height: 24rpx;
    background: #ffffff;
  }

  .desc {
    font-weight: 400;
    font-size: 18rpx;
    color: #ffffff;
    line-height: 26rpx;
    letter-spacing: 1px;
  }
}
</style>
