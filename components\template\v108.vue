<template>
  <view class="page-container">
    <view class="header-container">
      <Header />
      <image
        class="header-img"
        src="https://cdn.oss-unos.hmctec.cn/common/path/e63bc9c88f51452da3a39443c3c09a4f.png"
      >
      </image>
    </view>

    <image
      src="https://cdn.oss-unos.hmctec.cn/common/path/d45c2fa352db49c7bd444a2b52287fcb.png"
      class="feature-img"
    />

    <view class="input-container">
      <input
        class="input-container-input"
        type="text"
        placeholder="请输入手机号"
        v-model="form.phone"
        maxlength="11"
      />
      <view class="input-container-btn" @click="clickSubmit">立即领取</view>
    </view>

    <image
      src="https://cdn.oss-unos.hmctec.cn/common/path/c964a713440a4582bd614975c4fbd9da.png"
      class="feature-img-bottom"
    />

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>
<script>
import { getOnlineLowProduct, verifySmsIdV7 } from '@/apis/common-2'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { fetchChannelAndTemplate, reportUV } from '@/apis/common'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import Header from '@/components/header/header-hrh.vue'

export default {
  name: 'v108',
  components: {
    FullScreenMatchLoading,
    Header
  },

  data() {
    return {
      form: {
        templateVersion: 'v108',
        consumerId: '',
        /**
         * directMatchFlag
         * 1 有用户资质信息，无需填写资料，直接匹配企微、半流程和贷超
         * 2 无用户资质信息，填写资料匹配企微和贷超
         */
        directMatchFlag: '',
        channelId: '',
        phone: ''
      }
    }
  },

  mounted() {
    // 从路由参数获取数据
    const query = this.$route.query

    this.$u.vuex('vuex_invite', query.i)
    this.$u.vuex('vuex_smsId', query.s)

    if (query.param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(query.param)))
    }

    // 如果有邀请码和短信ID，则获取渠道和模板信息
    if (this.vuex_invite && this.vuex_smsId) {
      this.fetchChannelAndTemplate()
    }
  },

  methods: {
    async fetchChannelAndTemplate() {
      const res = await fetchChannelAndTemplate({
        invite: this.vuex_invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        reportUV({
          channelId: this.form.channelId
        })

        this.fetchUser()
      }
    },

    async fetchUser() {
      const res = await verifySmsIdV7({
        smsId: this.vuex_smsId,
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        setFlowNodes(res.data.process.length ? res.data.process : [])
        this.form.consumerId = res.data.consumerId
        this.$u.vuex('vuex_consumerId', res.data.consumerId)
        this.form.directMatchFlag = res.data.directMatchFlag
      }
    },

    clickSubmit() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      this.$u.vuex('vuex_phone', this.form.phone)

      if (this.form.directMatchFlag == 1) {
        // 有用户资质信息，无需填写资料，直接匹配
        this.navigateToNextFlow()
      } else if (this.form.directMatchFlag == 2) {
        // 无用户资质信息，需要跳转到填写资料页面
        this.navigateToAuthPage()
      }
    },

    navigateToAuthPage() {
      // 跳转到填写资料页面
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v108/auth/index?param=${urlParamString}`
      })
    },

    async navigateToNextFlow() {
      const routeMap = {
        offline: '/extreme/v108/apply/index',
        wechat: '/extreme/v108/qw/index',
        overloan: '/extreme/v108/applyOther/index',
        end: '/extreme/v108/download/index'
      }

      const customFetch = {
        wechat: getOnlineLowProduct,
        overloan: getOnlineLowProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )

      if (nextFlow.flow === 'halfapi') {
        this.$refs.loading.close()
        window.location.href = nextFlow.url
        return
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #7546fc 46%, rgba(255, 255, 255, 0) 97%) no-repeat;
  background-size: 100% 600rpx;
}

::v-deep .header-container {
  .name,
  .desc {
    color: #ffffff !important;
  }
}

.header-container {
  display: flex;
  align-items: center;
  .header-img {
    width: 215rpx;
    height: 30rpx;
  }
}

.feature-img {
  display: block;
  margin: 0 auto 30rpx;
  width: 687rpx;
  height: 358rpx;
}

.input-container {
  margin: 0 auto 40rpx;
  width: 688rpx;
  height: 322rpx;
  padding: 55rpx 35rpx;
  background: #ffffff;
  border-radius: 16rpx;

  .input-container-input {
    margin-bottom: 35rpx;
    padding: 25rpx 40rpx;
    width: 620rpx;
    height: 88rpx;
    background: #ffffff;
    border-radius: 301rpx 301rpx 301rpx 301rpx;
    border: 1rpx solid #999999;
    font-weight: 400;
    font-size: 28rpx;
    line-height: 40rpx;
  }

  .input-container-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 624rpx;
    height: 88rpx;
    background: #7546fc;
    border-radius: 217rpx 217rpx 217rpx 217rpx;
    font-weight: 500;
    font-size: 34rpx;
    color: #ffffff;
    line-height: 49rpx;
  }
}

.feature-img-bottom {
  display: block;
  margin: 0 auto;
  width: 690rpx;
  height: 408rpx;
}
</style>
