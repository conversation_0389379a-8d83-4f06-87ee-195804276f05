<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { formatAmount, parseAmount } from '@/utils/amount'
import { applyFormProduct } from '@/apis/common-2'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-axfq-2.vue'
import Declaration from '@/components/footer/declaration-component/declaration-szhh-2.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getAgreements } from '@/utils/agreement'
import routeMap from '../packages/routeMap.js'

// 导入axfq UI组件
import MatchSuccessHeader from '@/components/axfq/apply/MatchSuccessHeader.vue'
import CountdownTimer from '@/components/axfq/apply/CountdownTimer.vue'
import LoanAmountCard from '@/components/axfq/apply/LoanAmountCard.vue'
import LoanDetailsCard from '@/components/axfq/apply/LoanDetailsCard.vue'
import InstitutionMatchCard from '@/components/axfq/apply/InstitutionMatchCard.vue'
import NoticeBar from '@/components/axfq/apply/NoticeBar.vue'
import BottomConfirm from '@/components/axfq/apply/BottomConfirm.vue'
import AgreementPopup from '@/components/axfq/apply/AgreementPopup.vue'
import ProductPopup from '@/components/axfq/apply/ProductPopup.vue'

export default {
  name: 'ApplyIndex',
  components: {
    Declaration,
    Header,
    FullScreenMatchLoading,
    MatchSuccessHeader,
    CountdownTimer,
    LoanAmountCard,
    LoanDetailsCard,
    InstitutionMatchCard,
    NoticeBar,
    BottomConfirm,
    AgreementPopup,
    ProductPopup
  },

  data() {
    return {
      form: {
        monthIndex: 0,
        demandAmount: '50000'
      },
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      activeForm: '',
      productList: [],
      monthlyPay: '',
      agreementList: [],
      productPopupData: {}
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.updateAmountUI()
    this.getFlowData()
    this.fetchProtocol()
  },

  methods: {
    async fetchProtocol() {
      // 知情告知书，个人信息共享授权书，通用授权书
      this.agreementList = await getAgreements('axfq-szhh-apply')
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickProductItem(product) {
      this.productPopupData = product
      this.$refs.productPopup.open()
    },

    // hyh组件事件处理方法
    handleAmountBlur(value) {
      const amount = parseInt(value)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = '200000'
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = amount
      this.updateAmountUI()
    },

    handleAmountFocus(value) {
      this.form.demandAmount = parseAmount(this.form.demandAmount)
    },

    handleMonthChange(monthIndex) {
      this.form.monthIndex = monthIndex
      this.updateAmountUI()
    },

    handleProductCheckboxChange({ product, checked }) {
      product.uiChecked = checked
    },

    clickApply() {
      throttle(this.applyHandler)
    },

    applyHandler() {
      const selectedProducts = this.productList.filter((item) => item.uiChecked)
      if (selectedProducts.length === 0) {
        uni.showToast({
          title: '请选择产品',
          icon: 'none'
        })
        return
      }

      this.applyProduct()
    },

    getApplyParams() {
      const params = {
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      }

      params.applyProduct = this.productList.filter((item) => item.uiChecked)
      return params
    },

    async applyProduct() {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const params = this.getApplyParams()
      await applyFormProduct(params)

      uni.hideLoading()

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))

      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    updateAmountUI() {
      this.form.demandAmount = formatAmount(this.form.demandAmount)
      this.computedMonthPay()
    },

    computedMonthPay() {
      let price = Number(parseAmount(this.form.demandAmount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.form.monthIndex].value

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },



    setActiveForm(formName) {
      this.activeForm = formName
    },

    async getFlowData() {
      const flowData = getFlowData('offline')
      if (flowData) {
        flowData.productList.forEach((product) => {
          product.uiChecked = true
          // 随机生成匹配率（95～99）
          product.matchingDegree = Math.floor(Math.random() * 5 + 95)
        })
        this.productList = flowData.productList
        this.form.appUserId = flowData.appUserId
        this.form.channelId = flowData.channelId
      }
    }
  }
}
</script>

<template>
  <view class="page-container">
    <Header @click.native="navigateToIndex" />

    <!-- 匹配成功提示 -->
    <MatchSuccessHeader />

    <!-- 倒计时 -->
    <CountdownTimer />

    <!-- 借款金额卡片 -->
    <LoanAmountCard
      :amount="form.demandAmount"
      @amount-blur="handleAmountBlur"
      @amount-focus="handleAmountFocus"
    />

    <!-- 借款详情 -->
    <LoanDetailsCard
      :month-index="form.monthIndex"
      :month-range="monthRange"
      :monthly-pay="monthlyPay"
      @month-change="handleMonthChange"
      @card-click="setActiveForm('borrowing')"
    />

    <!-- 机构匹配 -->
    <InstitutionMatchCard
      :product-list="productList"
      @product-click="clickProductItem"
      @checkbox-change="handleProductCheckboxChange"
      @card-click="setActiveForm('product')"
    />

    <!-- 通知栏 -->
    <NoticeBar />

    <!-- 底部声明 -->
    <Declaration />

    <!-- 底部确认 -->
    <BottomConfirm
      :agreement-list="agreementList"
      @agreement-click="clickAgreement"
      @confirm-click="clickApply"
    />

    <AgreementPopup
      ref="agreementPopup"
      :agreement-list="agreementList"
      @agree-click="clickApply"
    />

    <ProductPopup
      ref="productPopup"
      :product-data="productPopupData"
      @checkbox-change="handleProductCheckboxChange"
    />

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background: #f1f8ff;
  padding-bottom: 600rpx;
}

</style>
