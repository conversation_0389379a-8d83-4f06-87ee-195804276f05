<script>
import Header from '@/components/header/header-axfq.vue'
import Declaration from '@/components/footer/declaration-component/declaration-szhh.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: { Declaration, Header, Services }
}
</script>

<template>
  <view class="page-container">
    <Header class="header" />
    <Declaration class="declaration" />
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  background-color: #eaecff;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/960a4ac404e54eef8aa35d78cbf6a0db.png);
  background-size: 100%;
  padding-top: 50rpx;
}

.header {
  transform: scale(1.8);
  margin: 0 auto;
}

.declaration {
  margin-top: 1000rpx;
}
</style>
