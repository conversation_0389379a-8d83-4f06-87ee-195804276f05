<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <view class="page-title">
        <view class="title">您的额度可以去</view>
        <view class="title">
          <image
            class="icon-wechat-2"
            src="https://cdn.oss-unos.hmctec.cn/common/path/e6a104cfe31f4a08b2d80202da103a8f.png"
          />
          <view class="title">微信使用啦</view>
        </view>
      </view>

      <view class="card">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/6c4d792b0b4a48938c756330d6e68090.png"
        />
      </view>

      <view class="card">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/7f7cd289118f461094019efea19531ec.png"
        />
      </view>
    </view>
    <view class="page-footer">


      <template v-if="applyUrl">
        <a class="btn confirm-btn" :href="applyUrl" target="_blank">
          立即领取
          <text v-if="confirmCountdownNumber">（{{ confirmCountdownNumber }}S）</text>
        </a>
      </template>

      <view class="confirm-tips">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/55328190f6c14d1297e166abb133b20a.png"
          class="icon-wechat"
        />
        <text>使用您本人微信验证后才可领取额度</text>
      </view>
    </view>



    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyOnlineProduct, fetchOnlineProduct, fetchWechatAutoJump, fetchWechatAutoTime, checkProductSequenceEnd } from '@/apis/common-2'

import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getApplyOnlineProductLink } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'qwIndex',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      productList: [],
      confirmCountdownNumber: 0,
      confirmCountdownTimer: null,
      // 企微自动跳转: 1 为不自动跳转 2为自动跳转
      wechatAutoJump: '',
      // 跳转链接
      applyUrl: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (isPageNested()) {
        savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    }
  },

  async onShow() {
    // 1. 判断当前流程是否结束
    const isEnd = await this.checkProductSequenceEnd()

    if (isEnd) {
      this.navigateToNextFlow()
      return
    }

    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      // 2. 根据接口获取是否自动跳
      await this.fetchWechatAutoJump()

      this.applyUrl = getApplyOnlineProductLink({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId,
        redirectUrl: encodeURIComponent(window.location.href)
      })

      // 3. 如果自动跳，根据接口获取时间
      if (this.wechatAutoJump == 2) {
        // 获取倒计时时间
        await this.fetchWechatAutoTime()
        // 启动倒计时，倒计时结束后自动申请产品
        this.timerStart()
      }
    } else {
      this.navigateToNextFlow()
    }
  },

  onUnload() {
    this.timerStop()
  },



  methods: {
    async checkProductSequenceEnd() {
      const res = await checkProductSequenceEnd({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchLowWechatFlag: 1
      })

      return res.data
    },

    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchLowWechatFlag: 1
      })

      // 返回 data 1 不自动跳转 2自动跳转
      this.wechatAutoJump = res.data || 1
    },

    async fetchWechatAutoTime() {
      const res = await fetchWechatAutoTime({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchLowWechatFlag: 1
      })

      // 返回 data 0 立即跳转 5 5秒后跳转 10 10秒后跳转
      this.confirmCountdownNumber = res.data || 0
    },

    timerStart() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
      }

      // 如果倒计时时间为0，立即跳转
      if (this.confirmCountdownNumber === 0) {
        this.clickConfirm()
        return
      }

      this.confirmCountdownTimer = setInterval(() => {
        this.confirmCountdownNumber--
        if (this.confirmCountdownNumber === 0) {
          this.clickConfirm()
        }
      }, 1000)
    },

    timerStop() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
        this.confirmCountdownTimer = null
        this.confirmCountdownNumber = 0
      }
    },



    async getFlowData() {
      const flowData = getFlowData('wechat_low')
      if (flowData) {
        this.productList = flowData.productList

        if (!this.form.consumerId) {
          this.form.consumerId = this.productList[0].consumerId
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1,
        matchLowWechatFlag: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      if (nextFlow.flow === 'end') {
        if (this.vuex_halfJumpUrl) {
          window.location.href = this.vuex_halfJumpUrl
          return
        }
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      clearFlowData('wechat_low')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },





    clickConfirm() {
      throttle(this.confirmHandler)
    },

    confirmHandler() {
      this.timerStop()
      this.applyProduct()
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #ff3154;

.color-primary {
  color: $color-primary;
}

.page-container {
  height: 100vh;
  background: #f6f6f8;

  .page-content {
    padding-top: 60rpx;
    padding-bottom: 600rpx;
    flex: 1;
    overflow: hidden auto;
    background: linear-gradient(180deg, #30c497 0%, #effbf9 48%);
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .agreement {
      margin-bottom: 20rpx;
      padding: 0 30rpx;
      display: flex;
      gap: 5rpx;
      text-align: center;

      .agree-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;

        .name {
          color: $color-primary;
        }
      }
    }

    .btn {
      display: block;
      padding: 22rpx;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      font-size: 40rpx;
      line-height: 56rpx;
      text-align: center;
    }

    .confirm-btn {
      margin-bottom: 20rpx;
      border: 1rpx solid #62d5ab;
      background: #62d5ab;
      color: #ffffff;
      text-decoration: none;
    }

    .confirm-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      line-height: 34rpx;

      .icon-wechat {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.page-title {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    display: flex;
    align-items: center;
    color: #fff;
    line-height: 100rpx;
    font-size: 72rpx;
    font-weight: 400;
  }

  .icon-wechat-2 {
    margin-right: 20rpx;
    width: 89rpx;
    height: 73rpx;
  }
}

.card {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;

  image {
    width: 633rpx;
    height: 298rpx;
  }
}

.agreement-popup {
  padding: 40rpx 50rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0a9a3a75797c401ab0170f69c0b923e7.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style>
