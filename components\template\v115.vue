<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <scroll-view
      class="page-content"
      scroll-y="true"
      :scroll-into-view="scrollIntoView"
      scroll-with-animation="true"
      @scrolltolower="scrollIntoView = ''"
    >
      <Header />

      <view class="header">
        <view class="header-title">
          <view class="header-title-text">还差一步，即可获取额度</view>
          <view class="header-title-subtext">最高可借200,000元</view>
        </view>
        <view class="tag">
          <view class="tag-number">1000</view>
          <view class="tag-gap-text">人</view>
          已放款
        </view>
      </view>

      <view
        class="qualification card"
        :class="{ activeForm: activeForm === 'qualification' }"
        @click="setActiveForm('qualification')"
      >
        <view class="card-header">
          <view class="card-header-label">
            <image
              class="card-header-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/3139180415c74936ba4a334fd2ae5c86.png"
            ></image>
            <view>资质信息</view>
          </view>
        </view>

        <view class="qualification-form" v-if="activeForm === 'qualification'">
          <view class="identity-form-item form-item">
            <view class="form-item-label">手机号</view>
            <view class="form-item-value">
              <input
                type="tel"
                style="text-align: right"
                placeholder="请输入手机号"
                maxlength="11"
                placeholder-class="placeholder"
                v-model="form.phone"
                @blur="phoneBlur"
              />
            </view>
          </view>

          <template v-if="isVisible('age')">
            <view class="form-item-line"></view>
            <view class="identity-form-item form-item">
              <view class="form-item-label">年龄</view>
              <view class="form-item-value">
                <input
                  placeholder="请填写年龄"
                  maxlength="3"
                  placeholder-class="placeholder"
                  v-model="form.age"
                  @blur="ageBlur"
                />
              </view>
            </view>
          </template>

          <template v-if="isVisible('sesameId')">
            <view class="form-item-line"></view>
            <view class="qualification-form-item form-item" @click="activeFormItem = 'sesameScore'">
              <view class="form-item-label">芝麻分</view>
              <view class="form-item-value">
                <input
                  disabled
                  style="pointer-events: none"
                  :value="
                    sesameScoreOptions[form.sesameScoreIndex]
                      ? sesameScoreOptions[form.sesameScoreIndex].label
                      : ''
                  "
                  placeholder="请选择芝麻分"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>
            <view class="form-item-line" v-if="activeFormItem === 'sesameScore'"></view>
            <view class="radio-group" v-if="activeFormItem === 'sesameScore'">
              <view
                class="radio"
                v-for="(item, index) in sesameScoreOptions"
                :key="index"
                :class="{ selected: index === form.sesameScoreIndex }"
                @click="clickSesameScore(index)"
              >
                {{ item.label }}

                <image
                  v-if="index === form.sesameScoreIndex"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/2f1f958b4a514db385a2351f525a6d93.png"
                ></image>
              </view>
            </view>
          </template>

          <template v-if="isVisible('isOverdue')">
            <view class="form-item-line"></view>
            <view class="qualification-form-item form-item" @click="activeFormItem = 'credit'">
              <view class="form-item-label">信用情况</view>
              <view class="form-item-value">
                <input
                  disabled
                  style="pointer-events: none"
                  :value="
                    creditOptions[form.creditIndex] ? creditOptions[form.creditIndex].label : ''
                  "
                  placeholder="请选择信用情况"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>
            <view class="form-item-line" v-if="activeFormItem === 'credit'"></view>
            <view class="radio-group" v-if="activeFormItem === 'credit'">
              <view
                class="radio"
                v-for="(item, index) in creditOptions"
                :key="index"
                :class="{ selected: index === form.creditIndex }"
                @click="clickCredit(index)"
              >
                {{ item.label }}

                <image
                  v-if="index === form.creditIndex"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/2f1f958b4a514db385a2351f525a6d93.png"
                ></image>
              </view>
            </view>
          </template>
        </view>
      </view>
    </scroll-view>
    <view class="page-footer">
      <view class="agreement">
        <view class="agreement-text">
          我已阅读并同意
          <text
            class="name"
            v-for="(item, index) in agreementList"
            :key="item.protocolId"
            @click="clickAgreement(item, index)"
          >
            《{{ item.name }}》
          </text>
        </view>
      </view>
      <view class="submit-btn" @click="clickSubmit">提交申请</view>
    </view>

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/148142372cca4f9ba688a388447b518e.png"
          class="agreement-popup-bg"
        ></image>

        <view class="agreement-popup-header">
          <view class="agreement-popup-title">请勿接听任何境外电话</view>
          <view class="agreement-popup-desc">
            任何放款前以缴纳
            <text class="highlight">保证金、利息等</text>
            名义的行为均为
            <text class="highlight">诈骗</text>
            ，任何要求您提供
            <text class="highlight">银行卡密码、验证码等</text>
            个人信息均为
            <text class="highlight">诈骗</text>
            。
          </view>
        </view>

        <view class="agreement-popup-body" v-if="agreementList.length > 0">
          <view class="agreement-name-container">
            <view
              class="agreement-name"
              v-for="(item, index) in agreementList"
              :key="index"
              @click="toggleAgreement(index)"
              :class="{ selected: index === agreementIndex }"
            >
              <view>{{ item.name }}</view>
              <view v-if="index !== agreementList.length - 1" class="agreement-name-line"></view>
            </view>
          </view>
          <view class="agreement-content" ref="agreementContent">
            <div v-html="agreementList[agreementIndex].content"></div>
          </view>
        </view>

        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickAgreeAndContinue">
            同意协议，继续申请
          </view>
          <view class="agreement-agree-tips"
            >若当前暂无匹配机构，请允许稍后持续为您匹配合适机构</view
          >
        </view>
      </view>
    </uni-popup>
    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { validateMobile } from '@/utils/validators'
import LockNativeBack from '@/utils/lock-native-back'
import {
  fetchAptitudeRestock,
  fetchChannelAndTemplate,
  matchingOnlineProduct,
  reportUV,
  saveAptitude
} from '@/apis/common'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hyh.vue'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getAgreements } from '@/utils/agreement'
import { fetchFlow } from '@/apis/common-2'
import routeMap from '@/extreme/v115/packages/routeMap.js'
import {isInWechatMiniProgram} from '@/utils/user-agent'
import {saveAppletOpenRecord} from '@/apis/common-3'

export default {
  name: 'template-v',

  components: {
    Header,
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        templateVersion: 'v115',
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        age: '',
        channelId: '',
        consumerId: '',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      // 默认展示资质信息
      activeForm: 'qualification',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 113,
          label: '650以上'
        },
        {
          value: 110,
          label: '650以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      agreementList: [],
      agreementIndex: 0,
      scrollIntoView: '',
      lockBack: null,
      visibleFields: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  async mounted() {
    // 从路由参数获取数据
    const query = this.$route.query

    this.$u.vuex('vuex_invite', query.i)

    if (query.param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(query.param)))
    }

    if (this.vuex_invite) {
      await this.fetchChannelAndTemplate()
      this.fetchVisibleFields()
    }

    this.fetchAgreement()

    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
     async fetchFlow() {
      const res = await fetchFlow({
        channelId: this.form.channelId
      })
      let nodes = res.data || []
      const defaultNodes = ['wechat', 'overloan', 'wechat_low', 'small_loan', 'wechat_official_account']
      if (nodes.length) {
        // 过滤掉defaultNodes之外的节点
        nodes = nodes.filter((node) => defaultNodes.includes(node))
      } else {
        nodes = defaultNodes
      }
      setFlowNodes(nodes)
    },

    isVisible(fieldName) {
      if (!this.visibleFields || this.visibleFields.length === 0) {
        return false
      }
      return this.visibleFields.includes(fieldName)
    },

    async fetchVisibleFields() {
      if (!this.form.channelId) {
        this.visibleFields = []
        return
      }
      try {
        const res = await fetchAptitudeRestock({ channelId: this.form.channelId })
        if (res.code == 200 && Array.isArray(res.data)) {
          this.visibleFields = res.data
        } else {
          this.visibleFields = []
        }
      } catch (error) {
        this.visibleFields = []
      }
    },

    async fetchAgreement() {
      // 注册页协议
      this.agreementList =
        (await getAgreements('hyh_info_stream_registration_page_agreement')) || []
    },

    async fetchChannelAndTemplate() {
      const res = await fetchChannelAndTemplate({
        invite: this.vuex_invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        this.fetchFlow()

        reportUV({
          channelId: this.form.channelId
        })
      }
      return res
    },

    validateAge() {
      if (!this.isVisible('age')) return true

      this.form.age = this.form.age ? this.form.age.trim() : ''

      if (!this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(this.form.age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      if (this.form.age < 22 || this.form.age > 55) {
        uni.showToast({
          title: '仅支持 22-55 岁的用户',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateSesameScore() {
      if (!this.isVisible('sesameId')) return true

      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateCredit() {
      if (!this.isVisible('isOverdue')) return true

      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateQualificationForm() {
      let isValid = true
      if (this.isVisible('age')) {
        isValid = isValid && this.validateAge()
      }
      if (this.isVisible('sesameId')) {
        isValid = isValid && this.validateSesameScore()
      }
      if (this.isVisible('isOverdue')) {
        isValid = isValid && this.validateCredit()
      }
      return isValid
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (this.isVisible('age') && !this.validateAge()) {
        return
      }

      if (this.isVisible('sesameId') && !this.validateSesameScore()) {
        return
      }

      if (this.isVisible('isOverdue') && !this.validateCredit()) {
        return
      }

      const saveData = {
        phone: this.form.phone,
        channelId: this.form.channelId
      }
      if (this.isVisible('age')) {
        saveData.age = this.form.age
      }
      if (this.isVisible('sesameId')) {
        saveData.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      }
      if (this.isVisible('isOverdue')) {
        saveData.isOverdue = this.form.isOverdue
      }

      this.$u.vuex('vuex_phone', this.form.phone)

      const res = await saveAptitude(saveData)

      if (res.code == 200) {
        this.form.consumerId = res.data
        this.$u.vuex('vuex_consumerId', res.data)

        if (isInWechatMiniProgram()) {
          await saveAppletOpenRecord({
            channelId: this.form.channelId,
            appUserId: this.form.consumerId
          })   
        }
      }

      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickAgreeAndContinue() {
      throttle(async () => {
        if (this.isVisible('age') && !this.validateAge()) {
          return
        }
        if (this.isVisible('sesameId') && !this.validateSesameScore()) {
          return
        }
        if (this.isVisible('isOverdue') && !this.validateCredit()) {
          return
        }

        this.navigateToNextFlow()
      })
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    getUserParams() {
      const params = {}
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.monthIndex = this.form.monthIndex

      return params
    },

    clickAgreement(item, index) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
      this.$nextTick(() => {
        this.toggleAgreement(index)
      })
    },

    toggleAgreement(index) {
      this.agreementIndex = index
      this.$refs.agreementContent.$el.scrollTop = 0
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    ageBlur() {
      this.validateAge()
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index
      this.activeFormItem = 'credit'
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  height: 100vh;
  background: #f6f6f8;

  .page-content {
    flex: 1;
    overflow: hidden auto;
    position: relative;
    padding-bottom: 400rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/5084b971c8934043a1450d76a3adec25.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .agreement {
      margin-bottom: 20rpx;
      display: flex;
      gap: 5rpx;

      .agree-icon {
        flex-shrink: 0;
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;

        .name {
          color: $color-primary;
        }
      }
    }

    .submit-btn {
      padding: 21rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 56rpx;
      text-align: center;
    }
  }
}

.header {
  position: relative;
  padding: 0 0 45rpx 30rpx;

  .header-title {
    .header-title-text {
      margin-bottom: 16rpx;
      font-weight: 700;
      font-size: 38rpx;
      color: #333333;
      line-height: 50rpx;
    }

    .header-title-subtext {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;
    }
  }

  .tag {
    position: absolute;
    top: 50rpx;
    right: 0;
    padding: 6rpx 7rpx 6rpx 10rpx;
    background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
    border-radius: 20rpx 0rpx 0rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #5a352f;
    line-height: 16rpx;
    display: flex;
    align-items: center;

    .tag-number {
      font-weight: 400;
      font-size: 24rpx;
      color: #5a352f;
      line-height: 28rpx;
    }

    .tag-gap-text {
      margin-left: 3rpx;
      margin-right: 6rpx;
    }
  }
}

.card {
  position: relative;
  margin: 40rpx 30rpx 0;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  padding-bottom: 20rpx;
  border: 1rpx solid #ffffff;

  &.activeForm {
    border: 1rpx solid $color-primary;
  }

  .card-header {
    padding: 30rpx 32rpx 10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-header-label {
    display: flex;
    gap: 12rpx;
    align-items: center;
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 50rpx;
  }

  .card-header-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.form-item-line {
  margin-left: 32rpx;
  height: 1rpx;
  background: #e2e2e2;
}

.form-item {
  padding: 25rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item-label {
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
}

.form-item-value {
  display: flex;
  align-items: center;
  gap: 22rpx;
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
  text-align: right;
}

.qualification {
  .qualification-form {
    .qualification-form-item {
      .form-item-label {
        width: 380rpx;

        .form-item-desc {
          margin-left: 10rpx;
          color: #f01515;
          font-size: 24rpx;
        }
      }
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15rpx;
      padding: 20rpx 30rpx;

      .radio {
        overflow: hidden;
        position: relative;
        width: 198rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f6f6f8;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;
        text-align: center;
        border: 1rpx solid #f6f6f8;

        &.selected {
          border-color: $color-primary;
          background-color: #ebf3ff;
          color: $color-primary;
        }

        .radio-selected-icon {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 34rpx;
          height: 34rpx;
        }

        .radio-high-quality-icon {
          position: absolute;
          right: 0;
          top: 0;
          width: 52rpx;
          height: 23rpx;
        }
      }
    }
  }
}

.placeholder {
  font-weight: normal;
  font-size: 32rpx;
  color: #a2a3a5;
  line-height: 40rpx;
  font-style: normal;
  text-transform: none;
}

.agreement-popup {
  padding: 40rpx 30rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0e5ec5a88f59400293ab9a9df2638efd.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-popup-header {
    position: relative;
  }

  .agreement-popup-bg {
    position: absolute;
    top: 15rpx;
    right: 40rpx;
    width: 185rpx;
    height: 173rpx;
  }

  .agreement-popup-title {
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 46rpx;
  }

  .agreement-popup-desc {
    margin-top: 23rpx;
    padding: 25rpx 30rpx;
    background: #fff7eb;
    border-radius: 14rpx 14rpx 14rpx 14rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    line-height: 40rpx;

    .highlight {
      color: #864508;
    }
  }

  .agreement-popup-body {
    padding: 25rpx 25rpx 0;
    margin-top: 25rpx;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    border: 1rpx solid $color-primary;
    border-bottom: none;

    .agreement-name-container {
      margin-bottom: 28rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 46rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .selected {
        color: #333333;
      }

      .agreement-name {
        display: flex;
        align-items: center;

        .agreement-name-line {
          margin: 0 20rpx;
          width: 2rpx;
          height: 22rpx;
          background: #d8d8d8;
        }

        &:last-child {
          .agreement-name-line {
            display: none;
          }
        }
      }
    }

    .agreement-content {
      overflow: auto;
      height: 400rpx;
    }
  }

  .agreement-agree-container {
    position: relative;
    padding: 10rpx 0 0;

    &::after {
      content: '';
      position: absolute;
      left: -2rpx;
      right: -2rpx;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      padding: 24rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;

      &.disabled {
        opacity: 0.7;
      }
    }

    .agreement-agree-tips {
      margin-top: 10rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #a3898a;
      line-height: 32rpx;
      text-align: center;
    }
  }
}
</style>
