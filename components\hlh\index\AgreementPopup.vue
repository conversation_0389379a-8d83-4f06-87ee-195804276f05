<template>
  <uni-popup
    background-color="#fff"
    ref="popup"
    type="bottom"
    border-radius="32rpx 32rpx 0 0"
  >
    <view class="agreement-popup" :style="{ backgroundImage: `url('${backgroundImage}')` }">
      <view class="agreement-container">
        <view v-for="agreement in agreementList" :key="agreement.protocolId">
          <view class="agreement-content">
            <div v-html="agreement.content"></div>
          </view>
        </view>
      </view>
      <view class="agreement-agree-container">
        <view 
          class="agreement-agree-btn" 
          :style="{ background: primaryColor }"
          @click="handleAgree"
        >
          同意并继续
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'AgreementPopup',
  
  props: {
    // 协议列表
    agreementList: {
      type: Array,
      default: () => []
    },
    // 主题色
    primaryColor: {
      type: String,
      default: '#ff5828'
    },
    // 背景图
    backgroundImage: {
      type: String,
      default: 'https://cdn.oss-unos.hmctec.cn/common/path/a2b94098ce43424aa6d2b4b722c36c65.png'
    }
  },

  methods: {
    // 打开弹窗
    open() {
      this.$refs.popup.open()
    },
    
    // 关闭弹窗
    close() {
      this.$refs.popup.close()
    },
    
    // 处理同意事件
    handleAgree() {
      this.$emit('agree')
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-popup {
  padding: 40rpx 50rpx 40rpx;
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style>
