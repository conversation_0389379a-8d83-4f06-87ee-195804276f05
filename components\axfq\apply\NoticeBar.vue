<template>
  <view class="notice-bar">
    <image
      class="notice-icon"
      src="https://cdn.oss-unos.hmctec.cn/common/path/860d103a4a7042dd96f263ce92fdd641.png"
    />
    <uni-notice-bar
      background-color="transparent"
      color="#c57b23"
      single
      scrollable
      :speed="speed"
      class="notice-text"
      :text="noticeText"
    />
  </view>
</template>

<script>
export default {
  name: 'NoticeBar',

  props: {
    // 通知文本
    noticeText: {
      type: String,
      default: '恭喜 李先生 132****7741 成功借款 50000 元'
    },
    // 滚动速度
    speed: {
      type: Number,
      default: 80
    }
  }
}
</script>

<style scoped lang="scss">
.notice-bar {
  margin: 20rpx 30rpx;
  height: 65rpx;
  background: #FFFFFF;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  gap: 17rpx;
  padding: 15rpx 30rpx;

  .notice-icon {
    width: 36rpx;
    height: 36rpx;
    flex-shrink: 0;
  }

  .notice-text {
    flex: 1;
    margin: 0;
    padding: 0;
    height: 36rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }
}
</style>
