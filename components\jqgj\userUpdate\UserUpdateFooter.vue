<template>
  <view class="footer">
    <view class="footer-text">
      当前系统预估最高可借额度
      <text class="footer-text-money">{{ estimatedAmount }}</text>
      元
    </view>
    <view class="footer-btn" @click="handleSubmitApplication">确认申请</view>
  </view>
</template>

<script>
export default {
  name: 'UserUpdateFooter',
  props: {
    estimatedAmount: {
      type: String, // 或者 Number，根据实际传入格式
      default: '0'
    }
  },
  methods: {
    handleSubmitApplication() {
      this.$emit('submit-application');
    }
  }
}
</script>

<style scoped lang="scss">
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30rpx 30rpx 60rpx;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.302);
  border-radius: 8rpx 8rpx 0rpx 0rpx;

  .footer-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 32rpx;
    text-align: center;
    margin-bottom: 23rpx;

    .footer-text-money {
      color: #FF6200;
    }
  }
  .footer-btn {
    width: 690rpx;
    height: 98rpx;
    background: #FF3154;
    border-radius: 429rpx 429rpx 429rpx 429rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
  }
}
</style>
