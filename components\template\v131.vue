<template>
  <view class="page-container">
    <notice-bar
      notice-text="您当前正在申请贷悦通助贷产品，我们将根据您自主填写的资质信息为您匹配"
    />

    <loan-amount-section
      :loan-amount="loanAmount"
      @input-focus="onInputFocus"
      @input-blur="onInputBlur"
      @set-amount="setAmount"
      @update:loan-amount="(value) => (loanAmount = value)"
    />

    <loan-options
      :duration="duration"
      :phone-number="phoneNumber"
      :agreed-terms="agreedTerms"
      :agreement-list="agreementList"
      :loan-amount="loanAmount"
      @set-duration="setDuration"
      @update:phone-number="(value) => (phoneNumber = value)"
      @submit-apply="submitApply"
      @toggle-agreement="toggleAgreement"
      @open-agreement="openAgreement"
    />

    <disclaimer />

    <validate-code-popup
      ref="validateCodePopup"
      :phone-number="phoneNumber"
      :validate-code="validateCode"
      :count-down="countDown"
      @update:validate-code="(value) => (validateCode = value)"
      @resend-code="resendCode"
      @submit-code="submitValidateCode"
    />

    <agreement-popup
      ref="agreementPopup"
      :agreement-list="agreementList"
      @agree="onAgreementAgree"
    />

    <RetainPopup ref="retainPopup" @continue="continueApply" @exit="exitApply" />

    <Declaration />
  </view>
</template>

<script>
import { formatAmount, parseAmount } from '@/utils/amount.js'
import NoticeBar from '@/components/dai-yue-tong/index/NoticeBar.vue'
import LoanAmountSection from '@/components/dai-yue-tong/index/LoanAmountSection.vue'
import LoanOptions from '@/components/dai-yue-tong/index/LoanOptions.vue'
import ValidateCodePopup from '@/components/dai-yue-tong/index/ValidateCodePopup.vue'
import Disclaimer from '@/components/dai-yue-tong/index/Disclaimer.vue'
import AgreementPopup from '@/components/dai-yue-tong/index/AgreementPopup.vue'
import RetainPopup from '@/components/dai-yue-tong/index/retain-popup.vue'
import { reportUV } from '@/apis/common'
import { saveLoan, sendSmsCode } from '@/apis/common-2'
import { saveAppletOpenRecord } from '@/apis/common-3'
import { encryptByDES } from '@/utils/encrypt'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import { isInWechatMiniProgram } from '@/utils/user-agent'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import LockNativeBack from '@/utils/lock-native-back'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  components: {
    NoticeBar,
    LoanAmountSection,
    LoanOptions,
    ValidateCodePopup,
    Disclaimer,
    AgreementPopup,
    RetainPopup,
    Declaration
  },
  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loanAmount: '48,000', // 初始化为带逗号的格式
      rawAmount: '48000', // 存储无逗号的原始数值
      duration: 12, // 默认选择12个月
      phoneNumber: '', // 手机号
      validateCode: '', // 验证码
      countDown: 0, // 倒计时
      timer: null, // 计时器
      agreedTerms: false, // 是否同意协议
      agreementList: [], // 协议列表
      lockBack: null // 返回拦截实例
    }
  },
  mounted() {
    // 上报UV数据
    if (this.channelId) {
      reportUV({ channelId: this.channelId })
    }
    // 获取协议列表
    this.fetchAgreement()

    // 初始化返回拦截
    this.initLockBack()
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }

    // 页面销毁时解除返回拦截
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },
  methods: {
    // 初始化返回拦截
    initLockBack() {
      this.lockBack = new LockNativeBack({
        onPopState: () => {
          // 当浏览器历史发生变化时，显示挽留弹窗
          this.$refs.retainPopup.open()
        }
      })
      this.lockBack.lock()
    },

    // 继续申请
    continueApply() {
      // 弹窗组件内部会自动关闭,不需要其他操作
    },

    // 确认离开
    exitApply() {
      // 弹窗组件内部会自动关闭,不需要其他操作
    },

    // 获取协议列表和详情
    async fetchAgreement() {
      // 注册页协议
      this.agreementList = await getAgreements('dyt-hls-ag1')
    },
    // 输入框聚焦时，显示无逗号的原始数值
    onInputFocus() {
      this.loanAmount = this.rawAmount.toString()
    },
    // 输入框失焦时，将数值格式化为带逗号的形式
    onInputBlur() {
      // 先确保数值有效
      if (this.loanAmount) {
        // 移除非数字字符，保存原始数值
        this.rawAmount = parseAmount(this.loanAmount).toString()
        // 使用项目提供的格式化工具函数
        this.loanAmount = formatAmount(this.rawAmount)
      }
    },
    // 设置指定金额
    setAmount(amount) {
      this.rawAmount = amount.toString()
      this.loanAmount = formatAmount(this.rawAmount)
    },
    // 设置借款期限
    setDuration(months) {
      this.duration = months
    },
    // 提交申请
    submitApply() {
      if (!this.phoneNumber || !/^1\d{10}$/.test(this.phoneNumber)) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        })
        return
      }

      if (!this.agreedTerms) {
        uni.showToast({
          title: '请阅读并同意注册协议',
          icon: 'none'
        })
        return
      }

      // 发送验证码
      if (this.countDown === 0) {
        this.sendSmsCode()
      }
      this.$refs.validateCodePopup.open()
    },
    // 发送短信验证码
    async sendSmsCode() {
      try {
        const res = await sendSmsCode({
          phone: this.phoneNumber,
          channelId: this.channelId
        })

        if (res.code !== 200) {
          uni.showToast({
            title: res.msg || '发送失败',
            icon: 'none'
          })
          return
        }

        uni.showToast({
          title: '验证码已发送',
          icon: 'none'
        })

        // 开始倒计时
        this.startCountDown()
      } catch (error) {
        uni.showToast({
          title: '发送验证码失败',
          icon: 'none'
        })
        console.error('发送验证码失败', error)
      }
    },
    // 重新获取验证码
    resendCode() {
      if (this.countDown > 0) return
      this.sendSmsCode()
    },
    // 开始倒计时
    startCountDown() {
      this.countDown = 60
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.countDown > 0) {
          this.countDown--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    // 获取设备指纹
    async getVisitorId() {
      const fp = await fpPromise
      const { visitorId } = await fp.get()
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },
    // 获取登录参数
    async getLoginParams() {
      const params = {}

      params.phoneBlack = getBlackPhone()
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = parseAmount(this.loanAmount)
      params.phone = this.phoneNumber
      params.code = this.validateCode
      params.channelId = this.channelId

      return params
    },
    // 提交验证码
    async submitValidateCode() {
      if (!this.validateCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }

      try {
        const params = await this.getLoginParams()
        const res = await saveLoan(params)

        if (res.code !== 200) {
          uni.showToast({
            title: res.msg || '操作失败',
            icon: 'none'
          })
          return
        }

        // 登录成功后，记录手机号
        setBlackPhone(params.phone)
        this.$u.vuex('vuex_phone', params.phone)

        // 如果在微信小程序环境中，记录小程序打开记录
        if (isInWechatMiniProgram()) {
          await saveAppletOpenRecord({
            channelId: this.channelId,
            appUserId: res.data
          })
        }

        // 跳转到认证页面
        const urlParam = {
          consumerId: res.data,
          phone: this.phoneNumber,
          demandAmount: parseAmount(this.loanAmount),
          channelId: this.channelId,
          monthIndex: this.getMonthIndex(),
          templateVersion: 'v131'
        }
        this.$u.vuex('vuex_consumerId', res.data)

        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `/extreme/v131/auth/index?param=${urlParamString}`
        })

        // 重置表单数据
        this.$refs.validateCodePopup.close()
        this.validateCode = ''
      } catch (error) {
        uni.showToast({
          title: '提交失败',
          icon: 'none'
        })
        console.error('提交验证码失败', error)
      }
    },
    // 获取月份索引，用于传递给下一页面
    getMonthIndex() {
      // 根据duration获取对应的月份索引
      const monthMap = {
        3: 0,
        6: 1,
        12: 2,
        36: 3
      }
      return monthMap[this.duration] || 2 // 默认为12个月(索引2)
    },
    // 切换是否同意协议
    toggleAgreement() {
      this.agreedTerms = !this.agreedTerms
    },
    // 打开协议
    openAgreement(agreement) {
      // 打开底部弹窗展示协议内容
      if (agreement) {
        // 找到当前点击的协议在列表中的索引
        const index = this.agreementList.findIndex(
          (item) => item.protocolId === agreement.protocolId
        )
        if (index !== -1) {
          this.$refs.agreementPopup.open(index)
        } else {
          this.$refs.agreementPopup.open()
        }
      } else {
        this.$refs.agreementPopup.open()
      }
    },
    // 同意协议并继续
    onAgreementAgree() {
      this.agreedTerms = true
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  // 页面容器样式
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/276067e5eed343f49b3cfc46dbd70aa8.png);
  min-height: 100vh;
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  padding-top: 22rpx;
  padding-bottom: 88rpx;
}
</style>
