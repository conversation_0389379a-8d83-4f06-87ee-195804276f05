<template>
  <view class="borrowing-options card" :class="{ activeForm: isActive }" @click="setActive">
    <view style="padding: 0 30rpx 30rpx" class="tips-text">最长借多久</view>
    <view class="option term" style="padding-top: 0">
      <view class="label">申请期数</view>
      <picker range-key="label" :range="monthRange" :value="monthIndex" @change="handleMonthChange">
        <view class="value">
          <view>{{ monthRange[monthIndex].value }}个月</view>
          <view class="recommend" v-if="monthRange[monthIndex].value === 12">高通过率</view>
          <uni-icons style="margin-left: 15rpx" color="#A2A3A5" type="right" size="16"></uni-icons>
        </view>
      </picker>
    </view>
    <view class="item-line"></view>
    <view class="option repayment-method">
      <view class="label">还款计划</view>
      <view class="value">
        <view class="repayment-amount">
          每月约应还
          <text class="amount-number">￥{{ monthlyPay }}</text>
        </view>
      </view>
    </view>
    <view class="item-line"></view>
    <view class="option coupon" style="padding-bottom: 0">
      <view class="label">年利率</view>
      <view class="value">
        <view class="annual-interest-rate">12%起</view>
        <view class="limited-time-offer">限时优惠</view>
      </view>
    </view>
    <view style="padding: 15rpx 30rpx 0rpx" class="tips-text"
      >实际贷款利息及放款金额以最终审批为准</view
    >
  </view>
</template>

<script>
import { parseAmount } from '@/utils/amount'

export default {
  name: 'LoanTermSelector',

  props: {
    amount: {
      type: [String, Number],
      default: '50000'
    },
    monthIndex: {
      type: Number,
      default: 0
    },
    isActive: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ]
    }
  },

  computed: {
    monthlyPay() {
      let price = Number(parseAmount(this.amount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.monthIndex].value

      return ((price + mLatte * month) / month).toFixed(2)
    }
  },

  methods: {
    handleMonthChange({ detail }) {
      this.$emit('update:monthIndex', detail.value)
    },

    setActive() {
      this.$emit('active')
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #ff5828;

.card {
  position: relative;
  padding: 30rpx 0;
  margin: 0 30rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  border: 1rpx solid #ffffff;

  &.activeForm {
    border: 1rpx solid $color-primary;
  }
}

.tips-text {
  font-weight: 400;
  font-size: 20rpx;
  color: #999999;
  line-height: 29rpx;
}

.item-line {
  margin-left: 30rpx;
  height: 2rpx;
  background-color: #f6f6f6;
}

.borrowing-options {
  .option {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    &.term .value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
      display: flex;
      align-items: center;

      .recommend {
        margin-left: 3rpx;
        padding: 7rpx 10rpx;
        background: #ff3636;
        border-radius: 203rpx 203rpx 203rpx 203rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #eef9f3;
        line-height: 18rpx;
      }
    }

    &.repayment-method .value {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      .amount-number {
        color: #333333;
      }

      .repayment-reminder {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 29rpx;
      }
    }

    &.coupon .value {
      gap: 12rpx;
      display: flex;
      align-items: center;

      .annual-interest-rate {
        font-weight: 400;
        font-size: 28rpx;
        color: #ffc93b;
        line-height: 32rpx;
      }

      .limited-time-offer {
        padding: 5rpx 7rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #ffffff;
        line-height: 23rpx;
        background: linear-gradient(90deg, #ff8b5f 0%, #ffba61 55%, #ff8058 100%);
        border-radius: 4rpx 4rpx 4rpx 4rpx;
      }
    }
  }
}
</style>
