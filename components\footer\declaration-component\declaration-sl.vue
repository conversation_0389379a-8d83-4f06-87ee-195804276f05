<script>
export default {
  name: 'declaration-sl',

  methods: {
    bindOpenUrl(url) {
      window.location.href = url
    }
  }
}
</script>

<template>
  <view class="declaration-container">
    <view v-if="vuex_declaration && !vuex_templateSig" v-html="vuex_declaration"></view>
    <template v-else>
      <view>
        <view
          @click="
            bindOpenUrl(
              'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=51010702002566'
            )
          "
        >
          <image
            class="police-icon"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/h5/images/icon/police.png"
          ></image>
        </view>
        <view>滇ICP备2023003380号-1</view>
        <view>云南盛略科技信息服务有限公司</view>
        <view>客服热线（工作日9：30-18：00）</view>
        <view>400-0819315</view>
        <view style="padding-top: 30rpx"
          >郑重声明：平台只提供贷款咨询和推荐服务，放款由银行或金融机构进行，
          所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全，
          请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。</view
        >
      </view>

      <view>
        <view>年化综合利率(单利)8.88%-18.36%</view>
        <view>贷款有风险，借款需谨慎;</view>
        <view>请根据个人能力合理贷款，理性消费，避免逾期;</view>
        <view>贷款额度，放款时间以实际审批结果为准;</view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.declaration-container {
  padding: 30rpx 60rpx;
  font-size: 20rpx;
  color: #ccc;
  line-height: 36rpx;
  text-align: center;

  .police-icon {
    width: 30rpx;
    height: 30rpx;
  }
}
</style>
