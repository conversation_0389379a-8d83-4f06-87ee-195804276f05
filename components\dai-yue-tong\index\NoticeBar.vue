<template>
  <view class="notice-bar">
    <!-- icon喇叭 -->
    <image
      class="notice-icon"
      src="https://cdn.oss-unos.hmctec.cn/common/path/740d461e875141dcaed9847617ac9fa9.png"
    />
    <uni-notice-bar
      background-color="transparent"
      color="#FFFFFF"
      single
      scrollable
      class="notice-text"
      :text="noticeText"
    />
  </view>
</template>

<script>
export default {
  props: {
    noticeText: {
      type: String,
      default: '您当前正在申请贷悦通助贷产品，我们优产品'
    }
  }
}
</script>

<style lang="scss" scoped>
.notice-bar {
  // 通知栏样式
  width: 752rpx;
  height: 48rpx;
  padding: 0 16rpx;
  background: #3fa9be;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;

  .notice-icon {
    // 通知图标样式
    width: 32rpx;
    height: 32rpx;
  }

  .notice-text {
    // 通知文本样式
    margin: 0;
    padding: 0;
    width: 680rpx;
    height: 28rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 28rpx;
  }
}
</style>
