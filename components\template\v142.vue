<template>
  <view class="page-container">
    <view class="product-container">
      <product-tips @showPopup="showPopup" />
      <city-selector @cityChanged="handleCityChange" />
      <view class="query-button" @click="queryClick">立即查询</view>
    </view>
    <product-intro-popup :visible="isPopupVisible" @close="closePopup" />
  </view>
</template>

<script>
import ProductTips from '@/components/loan-city-query-blue/ProductTips.vue'
import CitySelector from '@/components/loan-city-query-blue/CitySelector.vue'
import ProductIntroPopup from '@/components/loan-city-query-blue/ProductIntroPopup.vue'
import { encodeUrlParam } from '@/utils/queryParams.js'

export default {
  components: {
    ProductTips,
    CitySelector,
    ProductIntroPopup
  },
  data() {
    return {
      form: {
        cityName: '',
        cityCode: ''
      },
      isPopupVisible: false
    }
  },
  methods: {
    handleCityChange(cityInfo) {
      this.form.cityName = cityInfo.cityName
      this.form.cityCode = cityInfo.cityCode
    },
    showPopup() {
      this.isPopupVisible = true
    },
    closePopup() {
      this.isPopupVisible = false
    },
    queryClick() {
      if (!this.form.cityName) {
        uni.showToast({
          title: '请选择常驻城市',
          icon: 'none'
        })
        return
      }
      uni.navigateTo({
        url: `/extreme/v142/query-result/index?param=${encodeUrlParam(this.form)}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background-color: #f6f6f8;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/********************************.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  padding-top: 333rpx;
}

.product-container {
  margin: 0 31rpx;
  height: 540rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  padding: 30rpx 20rpx 132rpx;

  .city-container {
    margin: 15rpx 0 80rpx;
  }

  .query-button {
    margin: 0 auto;
    height: 92rpx;
    background: #3974f6;
    border-radius: 492rpx 492rpx 492rpx 492rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    margin: 0 20rpx;
  }
}
</style>
