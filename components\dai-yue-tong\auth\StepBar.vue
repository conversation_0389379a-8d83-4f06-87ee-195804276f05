<template>
  <view class="step-bar">
    <view class="step-item" :class="{ active: currentStep >= 0 }">
      <view class="step-number">1</view>
      <view class="step-text">个人信息</view>
    </view>
    <view class="divider" :class="{ active: currentStep >= 1 }"></view>
    <view class="step-item" :class="{ active: currentStep >= 1 }">
      <view class="step-number">2</view>
      <view class="step-text">资产信息</view>
    </view>
    <view class="divider" :class="{ active: currentStep >= 2 }"></view>
    <view class="step-item" :class="{ active: currentStep >= 2 }">
      <view class="step-number">3</view>
      <view class="step-text">信用情况</view>
    </view>
    <view class="divider" :class="{ active: currentStep >= 3 }"></view>
    <view class="step-item" :class="{ active: currentStep >= 3 }">
      <view class="step-number">4</view>
      <view class="step-text">提交成功</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'StepBar',
  props: {
    currentStep: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style lang="scss" scoped>
.step-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 46rpx 0 32rpx;
  height: 150rpx;

  .divider {
    flex-shrink: 0;
    width: 100rpx;
    height: 2rpx;
    background-color: #e4e4ee;
    align-self: flex-start;
    margin-top: 18rpx;

    &.active {
      background-color: #3fbeb8;
    }
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10rpx;

    .step-number {
      width: 36rpx;
      height: 36rpx;
      background: #e4e4ee;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 34rpx;
    }

    .step-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #d0d0d9;
      line-height: 28rpx;
    }

    &.active {
      .step-number {
        background-color: #3fbeb8;
        color: #ffffff;
      }

      .step-text {
        color: #3fbeb8;
      }
    }
  }
}
</style>
