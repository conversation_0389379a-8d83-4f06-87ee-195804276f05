<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <scroll-view
        class="page-content"
        scroll-y="true"
        :scroll-into-view="scrollIntoView"
        scroll-with-animation="true"
        @scrolltolower="scrollIntoView = ''"
      >
        <Header />

        <view class="title-container">
          <view class="title">只差一步，完成提现</view>
          <view class="warning">
            <view class="warning-text">请勿将平台资金用于虚拟货币、</view>
            <view class="warning-text">网络赌博等违法活动</view>
          </view>
        </view>

        <view class="phone-container">
          <input
            type="tel"
            maxlength="11"
            class="phone-input"
            placeholder="输入手机号获取额度(已加密)"
            @blur="phoneBlur"
            v-model="form.phone"
            placeholder-style="color: #A2A3A5"
          />

          <template v-if="isVisible('age')">
            <input
              type="number"
              maxlength="3"
              class="age-input"
              placeholder="请填写年龄"
              @blur="ageBlur"
              v-model="form.age"
              placeholder-style="color: #A2A3A5"
            />
          </template>

          <template v-if="isVisible('sesameId')">
            <view class="select-container" @click="openSesameScorePopup">
              <view class="select-value">{{
                sesameScoreOptions[form.sesameScoreIndex]
                  ? sesameScoreOptions[form.sesameScoreIndex].label
                  : '请选择芝麻分'
              }}</view>
              <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
            </view>
          </template>

          <template v-if="isVisible('isOverdue')">
            <view class="select-container" @click="openCreditPopup">
              <view class="select-value">{{
                creditOptions[form.creditIndex]
                  ? creditOptions[form.creditIndex].label
                  : '请选择信用情况'
              }}</view>
              <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
            </view>
          </template>

          <view class="submit-btn" @click="clickSubmit">提交申请</view>

          <view class="agreement" @click="isAgree = !isAgree" v-if="shouldShowAgreement">
            <view :class="['agree-icon', isAgree ? 'agree-icon-selected' : '']"></view>
            <view class="agreement-text">
              我已阅读并同意
              <text class="name" v-for="item in agreementList" :key="item.protocolId"
                @click.stop="clickAgreement">
                《{{ item.name }}》
              </text>
              的所有内容，同意并授权平台推荐/匹配多个产品
            </view>
          </view>
        </view>
      </scroll-view>

      <uni-popup
        background-color="#fff"
        ref="remindPopup"
        type="center"
        border-radius="56rpx 56rpx 56rpx 56rpx"
      >
        <view class="remind-popup">
          <view class="remind-popup-title">确定要放弃以下限时特权吗？</view>
          <view class="remind-popup-desc">额度仅限今日领取</view>
          <view class="remind-popup-feature">
            <view class="remind-popup-feature-item">
              <image
                class="remind-popup-feature-icon"
                src="https://cdn.oss-unos.hmctec.cn/common/path/bfa6afd3bab9489b813569d8de22eac4.png"
              ></image>
              <view class="remind-popup-feature-text">
                已匹配到产品的
                <text class="highlight">申请机会</text>
              </view>
            </view>
            <view class="remind-popup-feature-item">
              <image
                class="remind-popup-feature-icon"
                src="https://cdn.oss-unos.hmctec.cn/common/path/bfa6afd3bab9489b813569d8de22eac4.png"
              ></image>
              <view class="remind-popup-feature-text">
                初审已通过，确认额度
                <text class="highlight">立即领取</text>
              </view>
            </view>
          </view>
          <view class="remind-popup-tips">
            信息加密储存仅用于资质申请，平台承诺不会泄露您的任何信息
          </view>
          <view class="remind-popup-confirm" @click="$refs.remindPopup.close()">继续申请</view>
          <view class="remind-popup-cancel" @click="$refs.remindPopup.close()">狠心离开</view>
        </view>
      </uni-popup>

      <uni-popup
        background-color="#fff"
        ref="agreementPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="agreement-popup">
          <view class="agreement-popup-body">
            <view class="agreement-content">
              <view v-for="agreement in agreementList" :key="agreement.protocolId">
                <div v-html="agreement.content"></div>
              </view>
            </view>
          </view>
          <view class="agreement-agree-container">
            <view class="agreement-agree-btn" @click="clickAgreeAndContinue">同意并继续</view>
          </view>
        </view>
      </uni-popup>

      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>

<script>
import { validateMobile } from '@/utils/validators'
import LockNativeBack from '@/utils/lock-native-back'
import {
  fetchAptitudeRestock,
  fetchChannelAndTemplate,
  getTemplateConfig,
  matchingOnlineProduct,
  reportUV,
  saveAptitude,
  searchUserAptitude
} from '@/apis/common'
import { applyOnlineProduct } from '@/apis/common-2'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-custom.vue'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getAgreements } from '@/utils/agreement'

export default {
  name: 'template-v',

  components: {
    Header,
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        templateVersion: 'v124',
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        age: '',
        channelId: '',
        consumerId: '',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      // 默认展示资质信息
      activeForm: 'qualification',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 113,
          label: '650以上'
        },
        {
          value: 110,
          label: '650以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      agreementList: [],
      agreementIndex: 0,
      scrollIntoView: '',
      lockBack: null,
      visibleFields: [],
      isAgree: false
    }
  },

  computed: {
    shouldShowAgreement() {
      return this.vuex_templateConfig.agreementKeyIndex && this.agreementList && this.agreementList.length > 0
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  async mounted() {
    setFlowNodes(['wechat', 'overloan', 'wechat_official_account'])
    // 从路由参数获取数据
    const query = this.$route.query

    this.$u.vuex('vuex_invite', query.i)

    if (query.param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(query.param)))
    }

    if (this.vuex_invite) {
      await this.fetchChannelAndTemplate()
    }

    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
    async getTemplateConfig() {
      const res = await getTemplateConfig({
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        this.$u.vuex('vuex_theme', res.data.theme)
        this.$u.vuex('vuex_templateConfig', res.data)
        // 获取模板配置后，重新获取协议
        this.fetchAgreement()
      } else {
        this.$u.vuex('vuex_theme', '')
        this.$u.vuex('vuex_templateConfig', {})
      }
    },

    async fetchAgreement() {
      // 注册页协议
      this.agreementList = (await getAgreements(this.vuex_templateConfig.agreementKeyIndex)) || []
      
      // 设置初始同意状态
      if (!this.shouldShowAgreement) {
        this.isAgree = true
      } else {
        this.isAgree = false
      }
    },

    async fetchChannelAndTemplate() {
      const res = await fetchChannelAndTemplate({
        invite: this.vuex_invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        reportUV({
          channelId: this.form.channelId
        })

        this.getTemplateConfig()
      }
    },

    validateAge() {
      if (!this.isVisible('age')) return true

      this.form.age = this.form.age ? String(this.form.age).trim() : ''

      if (!this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(this.form.age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      // if (this.form.age < 22 || this.form.age > 55) {
      //   uni.showToast({
      //     title: '仅支持 22-55 岁的用户',
      //     icon: 'none'
      //   })
      //   return false
      // }

      return true
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (this.shouldShowAgreement && !this.isAgree) {
        uni.showToast({
          title: '请先同意相关协议',
          icon: 'none'
        })
        return
      }

      uni.showLoading()

      // 先搜索用户资质
      const searchRes = await searchUserAptitude({
        phone: this.form.phone
      })

      if (searchRes.code == 200) {
        // 已有资质信息，填充到表单中
        if (searchRes.data.age) {
          this.form.age = searchRes.data.age
        }

        if (searchRes.data.sesameId) {
          // 根据返回的芝麻分找到对应的选项索引
          const sesameIdIndex = this.sesameScoreOptions.findIndex(
            (option) => option.value == searchRes.data.sesameId
          )
          if (sesameIdIndex >= 0) {
            this.form.sesameScoreIndex = sesameIdIndex
          }
        }

        if (searchRes.data.hasOwnProperty('isOverdue')) {
          // 根据返回的信用情况找到对应的选项索引
          const creditIndex = this.creditOptions.findIndex(
            (option) => option.value == searchRes.data.isOverdue
          )
          if (creditIndex >= 0) {
            this.form.creditIndex = creditIndex
            this.form.isOverdue = searchRes.data.isOverdue
          }
        }
      }

      // 需要输入资质信息，获取需要显示的字段
      await this.fetchVisibleFields()

      uni.hideLoading()

      if (this.isVisible('age') && !this.validateAge()) {
        return
      }
      if (this.isVisible('sesameId') && !this.validateSesameScore()) {
        return
      }
      if (this.isVisible('isOverdue') && !this.validateCredit()) {
        return
      }

      // 保存资质信息
      const saveData = {
        phone: this.form.phone,
        channelId: this.form.channelId
      }

      if (this.isVisible('age')) {
        saveData.age = this.form.age
      }

      if (this.isVisible('sesameId')) {
        if (typeof this.form.sesameScoreIndex === 'number' && this.form.sesameScoreIndex >= 0) {
          saveData.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
        }
      }

      if (this.isVisible('isOverdue')) {
        saveData.isOverdue = this.form.isOverdue
      }

      this.$u.vuex('vuex_phone', this.form.phone)

      const res = await saveAptitude(saveData)      

      if (res.code == 200) {
        this.form.consumerId = res.data
        this.$u.vuex('vuex_consumerId', res.data)
      }

      // this.$refs.agreementPopup.open()
      // this.$forceUpdate()

     this.navigateToNextFlow()
    },

    async navigateToNextFlow() {      
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v124/qw/index',
        overloan: '/extreme/v124/applyOther/index',
        end: '/extreme/v124/download/index',
        wechat_official_account: '/extreme/v124/wechatOfficialAccount/index'
      }

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      if (nextFlow.flow === 'wechat') {
        const res = await applyOnlineProduct({
          productId: nextFlow.productList[0].id,
          consumerId: this.form.consumerId
        })

        if (res.data) {
          window.location.href = res.data
          return
        }
      }

      this.$refs.loading.close()

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    getUserParams() {
      const params = {}
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.monthIndex = this.form.monthIndex

      return params
    },

    clickAgreement() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickAgreeAndContinue() {
      this.$refs.agreementPopup.close()
      this.isAgree = true
      // 执行和提交申请一样的功能
      this.clickSubmit()
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    ageBlur() {
      this.validateAge()
    },

    isVisible(fieldName) {
      if (!this.visibleFields || this.visibleFields.length === 0) {
        return false
      }
      return this.visibleFields.includes(fieldName)
    },

    async fetchVisibleFields() {
      if (!this.form.channelId) {
        this.visibleFields = []
        return
      }
      try {
        const res = await fetchAptitudeRestock({ channelId: this.form.channelId })
        if (res.code == 200 && Array.isArray(res.data)) {
          this.visibleFields = res.data
        } else {
          this.visibleFields = []
        }
      } catch (error) {
        this.visibleFields = []
      }
    },

    validateSesameScore() {
      if (!this.isVisible('sesameId')) return true

      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateCredit() {
      if (!this.isVisible('isOverdue')) return true

      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })
        return false
      }
      return true
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
    },

    openSesameScorePopup() {
      uni.showActionSheet({
        itemList: this.sesameScoreOptions.map((item) => item.label),
        success: (res) => {
          this.clickSesameScore(res.tapIndex)
        }
      })
    },

    openCreditPopup() {
      uni.showActionSheet({
        itemList: this.creditOptions.map((item) => item.label),
        success: (res) => {
          this.clickCredit(res.tapIndex)
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/theme/v124/export/index.scss';
</style>
