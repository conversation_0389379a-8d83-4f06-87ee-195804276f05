<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="48"
    height="48"
    viewBox="0 0 48 48"
  >
    <g>
      <g>
        <path
          d="M24,45C35.598,45,45,35.598,45,24C45,12.40203,35.598,3,24,3C12.40203,3,3,12.40203,3,24C3,35.598,12.40203,45,24,45C24,45,24,45,24,45ZM13.5,24.6195C13.5,24.6195,15.6195,22.5,15.6195,22.5C15.6195,22.5,21,27.879,21,27.879C21,27.879,32.3775,16.5,32.3775,16.5C32.3775,16.5,34.5,18.622500000000002,34.5,18.622500000000002C34.5,18.622500000000002,21,32.120999999999995,21,32.120999999999995C21,32.120999999999995,13.5,24.6195,13.5,24.6195C13.5,24.6195,13.5,24.6195,13.5,24.6195Z"
          :fill="color"
          fill-opacity="1"
        />
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  name: 'checked-circle',
  props: {
    color: {
      type: String,
      default: '#013DDF'
    }
  }
}
</script>
