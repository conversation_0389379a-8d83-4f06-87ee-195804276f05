<script>
import Header from '@/components/header/header-custom.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: {
    Header,
    Services
  }
}
</script>

<template>
  <view :class="vuex_theme">
    <view class="page-container">
      <view class="page-content">
        <Header />
        <view class="feature-image">
          <view class="icon-speaker"></view>
        </view>

        <Services />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
@import '@/theme/v106/export/download.scss';
</style>
