<template>
  <view class="container">
    <view class="notice">
      <uni-notice-bar
        color="#F0A400"
        showIcon="true"
        :speed="50"
        scrollable="true"
        single="true"
        text="近期有不法分子，冒充各大银行或知名平台，以保证金，解冻费或其他理由实施电信诈骗，请广大用户提高警惕，警惕陌生来电，防范上当受骗！！！！！"
      ></uni-notice-bar>
    </view>

    <view class="wrap mt-13 mlr-16 pb-27">
      <view class="price-count card">
        <view class="flex-row align-center f19">
          <view class="pt-5">最高可借</view>
          <view class="pt-5">200,000元</view>
          <view class="all-btn f12 ml-auto" @click="totalPrice = 200000">全部借出</view>
        </view>
        <view class="f12 pb-14 color_c8c8">具体额度以实际审批为准</view>
        <view class="flex-row align-end input-row">
          <view class="mb-6 f24 mr-14">￥</view>
          <input
            class="f36 f_weight flex-wid"
            type="digit"
            v-model="totalPrice"
            style="height: 1.3em; min-height: 1.3em"
            @blur="handleChangePrice"
          />
          <view class="f12 mb-6 color_a2a3">金额可修改</view>
          <view
            class="iconfont icon-close mb-8 f10 ml-5 color_a2a3"
            @click="totalPrice = 50000"
          ></view>
        </view>
      </view>

      <view class="card mt-10">
        <view class="f19">借款期限</view>
        <view class="desc flex-row align-center f12 mb-6">
          <image
            class="desc-tag mr-7"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/app/202311070004.png"
          />
          <text> 参考年化12% </text>
          <text class="color_f68f">（10000元1天只需3.29元）</text>
        </view>
        <view class="v-22-grid-2 f14">
          <view
            :class="['example-item', { 'example-active': exampleCurrent == 1 }]"
            @click="exampleCurrent = 1"
          >
            <view>分6个月</view>
            <view class="mt-2">
              <text class="color_a2a3 mr-1">每月应还</text>
              <text class="color_3382">￥{{ getNowPrice(6) }}</text>
            </view>
          </view>
          <view
            :class="['example-item', { 'example-active': exampleCurrent == 2 }]"
            @click="exampleCurrent = 2"
          >
            <view>分12个月</view>
            <view class="mt-2">
              <text class="color_a2a3 mr-1">每月应还</text>
              <text class="color_3382">￥{{ getNowPrice(12) }}</text>
            </view>
          </view>
          <view
            :class="['example-item', { 'example-active': exampleCurrent == 3 }]"
            @click="exampleCurrent = 3"
          >
            <view>分24个月</view>
            <view class="mt-2">
              <text class="color_a2a3 mr-1">每月应还</text>
              <text class="color_3382">￥{{ getNowPrice(24) }}</text>
            </view>
          </view>
          <view
            :class="['example-item', { 'example-active': exampleCurrent == 4 }]"
            @click="exampleCurrent = 4"
          >
            <view>分36个月</view>
            <view class="mt-2">
              <text class="color_a2a3 mr-1">每月应还</text>
              <text class="color_3382">￥{{ getNowPrice(36) }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="card mt-14">
        <view class="input-wrap align-center flex-row">
          <view class="iconfont icon-a-zu876 mr-5 f18"></view>
          <input
            class="flex-wid"
            placeholder="请输入您的电话号码"
            v-model="form.phone"
            type="digit"
            maxlength="11"
            placeholder-class="text-color-9"
          />
        </view>

        <view class="input-wrap align-center flex-row mt-12">
          <view class="iconfont icon-a-zu875 mr-5 f18"></view>
          <input
            class="flex-wid"
            placeholder="输入验证码"
            v-model="form.code"
            type="digit"
            maxlength="6"
            placeholder-class="text-color-9"
          />
          <view
            class="input-btn text-28 color_3382"
            v-show="time == 60"
            @click.stop.prevent="bindSendSmsCode"
          >
            获取验证码</view
          >
          <view class="input-btn text-28 color_3382" v-show="time < 60">{{ time }} S</view>
        </view>

        <button class="btn flex-row-cc color_f register-btn" @click.stop="bindSubmit">
          立即领取额度
        </button>

        <view class="flex-row agree-wrap">
          <view
            :class="[
              'iconfont',
              'radio',
              'text-color-9',
              { 'icon-a-ziyuan23 color_3382': isChecked },
              { 'icon-a-ziyuan22': !isChecked }
            ]"
            @click="isChecked = !isChecked"
          ></view>
          <view class="agree">
            <text class="text-color-9">我已阅读并同意</text>
            <text
              v-for="protocol in protocolList"
              class="color_3382"
              :key="protocol.protocolId"
              @click="navigateToProtocolDetail(protocol)"
            >
              《{{ protocol.name }}》
            </text>
          </view>
        </view>
      </view>
    </view>
    <!-- 页脚文案 -->
    <view class="declaration">
      <view class="footer-tips">
        <view
          class="police label flex-row-cc"
          @click="
            bindOpenUrl(
              'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=51010702002566'
            )
          "
        >
          <image
            class="police-logo"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/h5/images/icon/police.png"
          ></image>
          <!-- <text>川公网安备 51010702002566号</text> -->
        </view>
        <view class="label">蜀ICP备2024108032号-1</view>
        <view class="label">成都晶源道科技有限责任公司</view>
        <view class="label">客服热线（工作日9：30-18：00）</view>
        <view class="label">028-83225733</view>
        <view class="label" style="padding-top: 30rpx"
          >郑重声明：平台只提供贷款咨询和推荐服务，放款由银行或金融机构进行，
          所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全，
          请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。</view
        >
      </view>

      <view class="footer-tips">
        <view class="label">本平台IRR综合年化利率7%~24%（IRR）;</view>
        <view class="label">贷款有风险，借款需谨慎;</view>
        <view class="label">请根据个人能力合理贷款，理性消费，避免逾期;</view>
        <view class="label">贷款额度，放款时间以实际审批结果为准;</view>
      </view>
    </view>

    <v-drawer
      v-model="show"
      @close="show = false"
      direction="bottom"
      width="100vw"
      height="auto"
      backgroundColor="rgba(0,0,0,0)"
    >
      <view class="drawer-wrap">
        <view class="header flex-row-sbc">
          <text>请先仔细阅相关协议</text>
          <view class="iconfont icon-close text-32 text-color-9" @click="show = false"></view>
        </view>
        <view class="rich-wrap">
          <block v-for="(protocol, index) in protocolList">
            <div v-html="protocol.content"></div>
            <view style="padding: 40rpx 0"></view>
          </block>
        </view>
        <view class="drawer-footer flex-row">
          <view class="fail-btn color_3" @click="show = false">不同意</view>
          <view class="drawer-btn" @click="bindCloseDialog">同意协议并继续</view>
        </view>
      </view>
    </v-drawer>
  </view>
</template>

<script>
import { validateMobile } from '@/utils/validators'
import dialog from '@/components/dialog/dialog'
import drawer from '@/components/drawer/drawer'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()
export default {
  name: 'template-v52',
  data() {
    return {
      isChecked: true,
      totalPrice: 50000,
      protocolList: [],
      form: {
        phone: '',
        code: '',
        channelId: ''
      },
      show: false,
      time: 60, // 验证码倒计时间
      timer: null, // 验证码倒计时实体类
      sendCode: false,
      exampleCurrent: 2,
      richs: [],
      isFocus: false
    }
  },
  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  components: {
    'v-dialog': dialog,
    'v-drawer': drawer
  },
  computed: {
    getNowPrice: () => {
      return function (month) {
        let price = Number(this.totalPrice)
        let mLatte = (price * 12) / 100 / 12

        let mALatee = (price + mLatte * month) / month

        return mALatee.toFixed(2)
      }
    }
  },
  created() {
    this.form.channelId = this.channelId

    this.init()
    uni.setNavigationBarTitle({
      title: '金元花'
    })

    // reportUV({ channelId: this.channelId })
  },

  methods: {
    init() {
      this.fetchProtocol()
    },

    async fetchProtocol() {
      this.protocolList = await getAgreements('jyd-jyh-registration')
    },

    handleChangePrice(e) {
      let value = e.detail.value / 1
      setTimeout(() => {
        if (value < 50000) {
          this.totalPrice = 50000
        }
        if (value > 200000) {
          this.totalPrice = 200000
        }
      })
    },

    /**
     *  @param { Function } 发送验证码
     */
    bindSendSmsCode() {
      if (!this.form.phone)
        return uni.showToast({
          title: '请输入手机号	',
          icon: 'none'
        })
      if (!validateMobile(this.form.phone))
        return uni.showToast({
          title: '请输入正确格式手机号',
          icon: 'none'
        })

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      this.sendCode = true

      clearInterval(this.timer)
      this.time -= 1
      this.timer = setInterval(() => {
        if (this.time < 1) {
          this.time = 60
          clearInterval(this.timer)
          return
        }
        this.time -= 1
      }, 1000)
    },

    /**
     *  @param { Function } bindSubmit 提交信息
     */
    bindSubmit() {
      if (!this.form.phone)
        return uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
      if (!validateMobile(this.form.phone))
        return uni.showToast({
          title: '请输入正确格式手机号',
          icon: 'none'
        })
      if (!this.form.code)
        return uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
      if (!this.isChecked) {
        return (this.show = true)
      }
      if (!this.sendCode) {
        uni.showToast({
          title: '请先获取验证码',
          icon: 'none'
        })
        return
      }
      this.bindApply()
    },

    /**
     *  @param { Function } bindApply 立即拿钱
     */
    async bindApply() {
      this.$u.vuex('vuex_phone', this.form.phone)
      if (this.form.code == 122943) {
        uni.navigateTo({
          url: '/extreme/v52/applyOther/index'
        })
      } else {
        uni.showToast({
          title: '验证码错误',
          icon: 'none'
        })
      }
    },

    /**
     * @param { Function } 关闭弹窗
     */
    bindCloseDialog() {
      this.isChecked = true
      this.bindSubmit()
    },

    async getPhoneInfo() {
      const fp = await fpPromise
      const result = await fp.get()
      const visitorId = result.visitorId
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },

    navigateToProtocolDetail(protocol) {
      protocol = {
        ...protocol
      }
      delete protocol.content
      uni.navigateTo({
        url: `/pages/rich/index?obj=${encodeURIComponent(JSON.stringify(protocol))}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 100vw;
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/app/statis/app/202311170001.png);
  background-size: 100vw 326rpx;
  background-position-y: 0;
  background-repeat: no-repeat;
  overflow: hidden;
  background-color: #fff;
  padding: 62rpx 0 100rpx;
  background-position: 0 63rpx;

  .notice {
    position: absolute;
    width: 100vw;
    z-index: 9;
    top: 0;
    left: 0;

    .uni-noticebar {
      padding: 0 20rpx !important;
      margin-bottom: 0;
      color: #ff4504 !important;
    }
  }

  .logo {
    width: 130rpx;
    height: 34rpx;
  }
  .name-img {
    width: 228rpx;
    height: 40rpx;
  }

  .pro-name {
    width: 136rpx;
    height: 44rpx;
  }

  .title-desc {
    color: rgba(255, 255, 255, 0.85);
    font-size: 30rpx;
    padding: 0 10rpx;
    margin: 0 10rpx;
    border-left: 1rpx solid rgba(255, 255, 255, 0.85);
    line-height: 1;
  }

  .price {
    font-size: 90rpx;
    padding: 10rpx 0;
    font-weight: 600;
    text-align: left;
    position: relative;
    &:before {
      content: '￥';
      font-weight: normal;
      font-size: 48rpx;
    }
  }
  .slider-wrap {
    width: 100%;
    height: 22rpx;
    background-color: #e1e1e1;
    display: flex;
    align-items: center;
    border-radius: 12rpx;
    .slider-bg {
      width: calc(25%);
      background: #2c52ef;
      height: 22rpx;
      border-radius: 12rpx;
      flex-shrink: 1;
      transition: 0.3s ease all;
    }
  }
  .slider-step {
    // margin: 0 -10rpx;
    padding: 6rpx 0 0;
    .slider-item {
      font-size: 30rpx;
    }
  }
  .move-circle {
    width: 34rpx;
    height: 34rpx;
    background: #fefefc;
    box-shadow: 0px 0px 3rpx #999999;
    border-radius: 50%;
    margin-top: -6rpx;
    margin-left: -18rpx;
    transition: 0.3s ease all;
    .move-circle-mini {
      width: 24rpx;
      height: 24rpx;
      background: #2c52ef;
      box-shadow: 0px 0px 3rpx #999999;
      border-radius: 50%;
    }
  }
  .wrap {
    background-image: linear-gradient(180deg, #deeaff 0%, #ffffff 20%, #ffffff 100%);
    background-size: 100% 326rpx;
    background-repeat: no-repeat;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
  }
  .card {
    position: relative;
    overflow: hidden;
    .agree-wrap {
      margin-top: 24rpx;
      font-size: 24rpx;
    }

    .desc {
      height: 66rpx;
      background: rgba(255, 255, 255, 0.38);
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      .desc-tag {
        width: 130rpx;
        height: 40rpx;
      }
    }

    .item {
      padding: 32rpx 32rpx 36rpx;

      .circle {
        background-color: #f4f1f4;
        border-radius: 50%;
        width: 54rpx;
        height: 54rpx;
        color: #ffffff;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;

        .circle-icon {
          width: 30rpx;
          display: block;
        }

        &:nth-of-type(2) {
          .circle-icon {
            width: 26rpx;
            display: block;
          }
        }
      }

      .desc {
        padding-top: 20rpx;
        text-align: center;
      }
    }

    .line {
      flex: 1;
      min-width: 1rpx;
      height: 1px;
      border-bottom: 3rpx dashed #999999;
      margin: -46rpx -8rpx 0;
    }
  }

  .price-count {
    padding: 28rpx 32rpx 20rpx;
    align-items: flex-end;
    background-size: 100% 100%;
    background-color: transparent;
    background-image: url(https://cdn.oss-unos.hmctec.cn/app/statis/app/202311200001.png);
    background-size: 188rpx 188rpx;
    background-repeat: no-repeat;
    background-position: calc(100% - 50rpx) 4rpx;
    box-shadow: 0rpx 2rpx 14rpx 0rpx #dfedff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    .all-btn {
      width: 146rpx;
      height: 52rpx;
      background: #3882e7;
      border-radius: 26rpx 26rpx 26rpx 26rpx;
      color: #fff;
      text-align: center;
      line-height: 52rpx;
    }
    .input-row {
      border-bottom: 2rpx solid #c8c8c9;
    }
  }
  .card-input {
    box-shadow: 0px 3px 8px rgba(204, 204, 204, 0.8);
    padding: 60rpx 32rpx 64rpx;
    border-radius: 20rpx;
  }
  .card-pro {
    background-color: #f9f9f9;
    padding-bottom: 4rpx;

    .flex-row-sbc {
      padding: 14rpx 0;
      font-size: 28rpx;
    }
  }
  .example-wrap {
    background-color: #3882e7;
    margin: 0 42rpx;
    padding: 12rpx 0;
    border-radius: 16rpx 16rpx 0 0;
  }
  .example {
    color: #fff;
    ~ .example {
      position: relative;
      &:before {
        content: '';
        width: 1rpx;
        height: 28rpx;
        background-color: #fff;
        top: calc(50% - 14rpx);
        left: -30rpx;
        position: absolute;
      }
    }
  }
  .example-item {
    background-color: #fff;
    border-radius: 12rpx;
    padding: 18rpx 24rpx;
    border: 1px solid #c8c8c9;
    background-image: url(https://cdn.oss-unos.hmctec.cn/app/statis/app/202311200003.png);
    background-repeat: no-repeat;
    background-size: 48rpx 48rpx;
    background-position: 100% 100%;
  }
  .example-active {
    background: #e4ecff;
    border-color: #3882e7;
    background-image: url(https://cdn.oss-unos.hmctec.cn/app/statis/app/202311200002.png);
    background-repeat: no-repeat;
    background-size: 48rpx 48rpx;
    background-position: 100% 100%;
  }

  .radio {
    width: 34rpx;
    height: 34rpx;
    margin-right: 8rpx;
    font-size: 30rpx;
  }

  .btn {
    width: 512rpx;
    height: 88rpx;
    background: #3882e7;
    border-radius: 44rpx;
    font-size: 34rpx;
    letter-spacing: 2rpx;
    font-weight: bold;
    margin: 42rpx auto 32rpx;
    animation: mymove 0.8s linear infinite;
    transform-origin: center center;
  }

  .dialog-content-liner {
    width: 100%;
    margin: 0;
    background: linear-gradient(174deg, #d1e7ff 0%, #ffffff 100%);
  }

  .dialog-content {
    .dialog-header {
      background: #3882e7;
      color: #fff;
      margin: -30rpx -28rpx 30rpx;
      border-radius: 16rpx 16rpx 0 0;
      padding: 30rpx 0;
      font-size: 40rpx;
    }

    .dialog-btn {
      background-color: #3882e7;
    }
  }
  .dialog-code {
    padding: 40rpx 32rpx 60rpx;
    .dialog-code-btn {
      width: 100%;
      margin-top: 34rpx;
      height: 90rpx;
      line-height: 90rpx;
      font-size: 34rpx;
      font-weight: bold;
      letter-spacing: 2rpx;
    }
  }

  .input-wrap {
    border-radius: 50rpx;
    background: #fff;
    padding: 30rpx 24rpx;
    border: 1rpx solid #c8c8c9;
  }

  .input-btn {
    width: 170rpx;
    position: relative;
    text-align: center;
    padding-left: 16rpx;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: calc(50% - 20rpx);
      width: 2rpx;
      height: 40rpx;
      background-color: #d8d8d8;
    }
  }
}
.drawer-btn {
  background-color: #3882e7 !important;
}

.step-header-icon {
  width: 34rpx;
  height: 24rpx;
}
.step-header-icon-right {
  transform: rotate(180deg);
}
.step-wraper {
  padding: 0 12rpx 20rpx;
  .step-item {
    text-align: center;
    .icon {
      display: block;
      width: 100rpx;
      height: 100rpx;
      margin: 0 auto;
    }
  }
  .icon-line {
    width: 32rpx;
    height: 32rpx;
    margin: 34rpx 0 0;
  }
}

@keyframes mymove {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@-moz-keyframes mymove

  /* Firefox */ {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@-webkit-keyframes mymove

  /* Safari and Chrome */ {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

@-o-keyframes mymove

  /* Opera */ {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.v-22-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 30rpx;
  grid-row-gap: 26rpx;
}

.drawer-wrap {
  background: linear-gradient(180deg, #ddedff 0%, #ffffff 18%);
  border-radius: 32rpx 32rpx 0 0;
  padding: 40rpx 46rpx 0;

  .header {
    font-size: 36rpx;
    font-weight: bold;
    padding: 0 0 18rpx;

    .iconfont {
      font-weight: normal;
    }
  }

  .rich-wrap {
    padding: 20rpx 0 0;
    max-height: 55vh !important;
    overflow-y: scroll;
    font-size: 26rpx;
  }

  .drawer-footer {
    font-size: 36rpx;
    padding: 20rpx 0 calc(20rpx + env(safe-area-inset-bottom));

    .fail-btn {
      border: 1rpx solid #e5e5e5;
      padding: 0 12rpx;
      height: 98rpx;
      line-height: 98rpx;
      border-radius: 8rpx;
      text-align: center;
      margin-right: 20rpx;
      flex: 1;
      min-width: 1rpx;
    }

    .drawer-btn {
      height: 98rpx;
      line-height: 98rpx;
      background-color: #1e63f9;
      border-radius: 8rpx;
      color: #fff;
      padding: 0 12rpx;
      text-align: center;
      flex: 1;
      min-width: 1rpx;
    }
  }
}

.declaration {
  padding: 32rpx 32rpx 44rpx;
  color: #999999;

  .footer-tips {
    font-size: 26rpx;
    line-height: 1.5;
    text-align: center;
    padding: 32rpx 0 32rpx;

    .police-logo {
      width: 30rpx;
      height: 30rpx;
      margin-right: 8rpx;
    }
  }
}
</style>
