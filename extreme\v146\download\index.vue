<script>
import Declaration from '@/components/footer/declaration-component/declaration-szhh-2.vue'

export default {
  name: 'Download',
  components: { Declaration }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <image class="page-content" src="https://cdn.oss-unos.hmctec.cn/common/path/b7a115e96b0e4ed3845dcb85390ed939.png" mode="widthFix" />
    <Declaration />
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  height: 100dvh;
  background: #EFF6FF;
  display: flex;
  flex-direction: column;

  .page-content {
    width: 100%;
  }
}
</style>
