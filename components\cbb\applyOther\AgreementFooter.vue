<template>
  <view class="agreement-footer" v-if="agreementList.length > 0">
    <view class="agreement-content">
      <text class="agreement-text">
        点击申请即表示您已阅读并同意
        <text 
          class="agreement-link" 
          v-for="(agreement, index) in agreementList" 
          :key="agreement.protocolId"
          @click="clickAgreement(agreement)"
        >
          《{{ agreement.name }}》{{ index < agreementList.length - 1 ? '、' : '' }}
        </text>
      </text>
    </view>

    <!-- 协议弹窗 -->
    <AgreementPopup 
      ref="agreementPopup" 
      :agreementList="agreementList"
      @agree="handleAgreementAgree"
    />
  </view>
</template>

<script>
import { getAgreements } from '@/utils/agreement'
import AgreementPopup from '@/components/cbb/index/AgreementPopup.vue'

export default {
  name: 'AgreementFooter',

  components: {
    AgreementPopup
  },

  props: {
    agreementKey: {
      type: String,
      default: 'cbb-dc'
    }
  },

  data() {
    return {
      agreementList: []
    }
  },

  mounted() {
    this.fetchProtocol()
  },

  methods: {
    async fetchProtocol() {
      // 获取协议数据
      this.agreementList = await getAgreements(this.agreementKey)
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
    },

    handleAgreementAgree() {
      // 协议同意后的处理逻辑
      this.$emit('agree')
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #e95628;

.agreement-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #ffffff;
  padding: 20rpx 30rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 1rpx solid #f0f0f0;
  z-index: 100;

  .agreement-content {
    .agreement-text {
      font-size: 24rpx;
      color: #666666;
      line-height: 34rpx;

      .agreement-link {
        color: $color-primary;
      }
    }
  }
}
</style>
