<template>
  <view class="page">
    <view>贷超产品</view>
    <view class="main-product" v-if="mainProduct.id">
      <view class="product-info">
        <image class="logo" :src="mainProduct.logo"></image>
        <text>{{ mainProduct.name }}</text>
      </view>
      <button @click="clickApply(mainProduct)">申请</button>
    </view>

    <view class="other-products">
      <view class="product-item" v-for="(item, index) in productList" :key="index">
        <view class="product-info">
          <image class="logo" :src="item.logo"></image>
          <text>{{ item.name }}</text>
        </view>
        <button @click="clickApply(item)">申请</button>
      </view>
    </view>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct } from '@/apis/common-2'

export default {
  data() {
    return {
      form: {},
      productList: [],
      mainProduct: {}
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.getFlowData()
  },

  methods: {
    async getFlowData() {
      const flowData = getFlowData('overloan')
      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '申请中',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
        window.location.href = res.data
        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow == 'halfapi') {
        window.location.href = nextFlow.url
        return
      }

      const routeMap = {
        offline: '/extreme/v100/apply/index',
        wechat: '/extreme/v100/qw/index',
        overloan: '/extreme/v100/applyOther/index',
        end: '/extreme/v100/download/index'
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = encodeURIComponent(encryptByDES(JSON.stringify(this.form)))
      uni.navigateTo({
        url: `${path}?param=${urlParam}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding: 30rpx;
}

.main-product,
.product-item {
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 16rpx;
}

.product-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .logo {
    width: 48rpx;
    height: 48rpx;
    margin-right: 20rpx;
  }
}

button {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #1678ff;
  color: #fff;
  border-radius: 40rpx;
}
</style>
