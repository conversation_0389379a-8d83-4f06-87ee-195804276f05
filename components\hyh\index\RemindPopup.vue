<template>
  <uni-popup
    :is-mask-click="false"
    background-color="#fff"
    ref="remindPopup"
    type="center"
    border-radius="56rpx 56rpx 56rpx 56rpx"
  >
    <view class="remind-popup">
      <view class="remind-popup-title">确定要放弃以下限时特权吗？</view>
      <view class="remind-popup-desc">额度仅限今日领取</view>
      <view class="remind-popup-feature">
        <view class="remind-popup-feature-item">
          <image
            class="remind-popup-feature-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/d2196ac9fdb94ed28f61479de3fe821c.png"
          ></image>
          <view class="remind-popup-feature-text">
            已匹配到产品的
            <text class="highlight">申请机会</text>
          </view>
        </view>
        <view class="remind-popup-feature-item">
          <image
            class="remind-popup-feature-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/d2196ac9fdb94ed28f61479de3fe821c.png"
          ></image>
          <view class="remind-popup-feature-text">
            初审已通过，确认额度
            <text class="highlight">立即领取</text>
          </view>
        </view>
      </view>
      <view class="remind-popup-tips"
        >信息加密储存仅用于资质申请，平台承诺不会泄露您的任何信息</view
      >
      <view class="remind-popup-confirm" @click="handleConfirm">继续申请</view>
      <view class="remind-popup-cancel" @click="handleCancel">狠心离开</view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'RemindPopup',
  
  methods: {
    // 打开弹窗
    open() {
      this.$refs.remindPopup.open()
    },
    
    // 关闭弹窗
    close() {
      this.$refs.remindPopup.close()
    },
    
    // 处理确认按钮点击
    handleConfirm() {
      this.$emit('confirm')
      this.close()
    },
    
    // 处理取消按钮点击  
    handleCancel() {
      this.$emit('cancel')
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.remind-popup {
  width: 650rpx;
  padding: 88rpx 66rpx 72rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/47517249d74c409daf1ccfee58dfb4eb.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .remind-popup-title {
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-desc {
    margin-top: 14rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-feature {
    margin-top: 50rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;

    .remind-popup-feature-item {
      display: flex;
      align-items: center;
      gap: 5rpx;

      .remind-popup-feature-icon {
        width: 36rpx;
        height: 30rpx;
      }

      .remind-popup-feature-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #3d3d3d;
        line-height: 37rpx;
        letter-spacing: 2px;

        .highlight {
          color: $color-primary;
        }
      }
    }
  }

  .remind-popup-tips {
    margin-top: 43rpx;
    background: #fff7eb;
    font-weight: 400;
    font-size: 24rpx;
    color: #864508;
    line-height: 32rpx;
    padding: 16rpx 22rpx;
  }

  .remind-popup-confirm {
    margin-top: 48rpx;
    background: $color-primary;
    border-radius: 38rpx 38rpx 38rpx 38rpx;
    font-weight: normal;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 45rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding: 15rpx;
  }

  .remind-popup-cancel {
    margin-top: 22rpx;
    font-weight: normal;
    font-size: 26rpx;
    color: #919094;
    line-height: 36rpx;
    text-align: center;
  }
}
</style> 