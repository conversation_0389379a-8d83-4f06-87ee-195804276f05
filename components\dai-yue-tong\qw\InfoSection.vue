<template>
  <view class="info-section">
    <view class="info-title">关于贷悦通</view>
    <view class="info-desc">保障用户资金及个人信息安全</view>

    <view class="features-container">
      <view class="feature-item">
        <!-- icon -->
        <image
          class="feature-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/133a62db4cdd4e3ba630e927bb25aebe.png"
          mode="scaleToFill"
        />
        <view class="feature-title">申请简单</view>
        <view class="feature-desc">大额低息申请便捷</view>
      </view>

      <view class="feature-item">
        <!-- icon -->
        <image
          class="feature-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/df15c9891f1b4bada13a365eedce33fd.png"
          mode="scaleToFill"
        />
        <view class="feature-title">极速放款</view>
        <view class="feature-desc">审核后最快1分钟放款</view>
      </view>

      <view class="feature-item">
        <!-- icon -->
        <image
          class="feature-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/8d8220e11e9842628b0d77f6eea610ab.png"
          mode="scaleToFill"
        />
        <view class="feature-title">灵活分期</view>
        <view class="feature-desc">随时随地借款还款</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'DytQwInfoSection'
}
</script>

<style lang="scss" scoped>
.info-section {
  margin: 0 auto;
  padding: 28rpx 0 42rpx;
  width: 690rpx;
  height: 342rpx;
  background: #ffffff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;

  .info-title {
    margin-bottom: 6rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 46rpx;
    text-align: center;
  }

  .info-desc {
    margin-bottom: 42rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #4d4d4d;
    line-height: 34rpx;
    text-align: center;
  }

  .features-container {
    padding: 0 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .feature-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .feature-icon {
        width: 40rpx;
        height: 40rpx;
      }

      .feature-title {
        margin: 18rpx 0 12rpx;
        font-weight: 400;
        font-size: 28rpx;
        color: #1a1a1a;
        line-height: 34rpx;
      }

      .feature-desc {
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        line-height: 28rpx;
      }
    }
  }
}
</style>
