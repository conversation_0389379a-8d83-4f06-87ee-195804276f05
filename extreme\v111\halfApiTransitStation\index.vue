<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import { fetchHalfApiProduct } from '@/apis/common-2'

export default {
  name: 'halfApiTransitStation',

  data() {
    return {
      form: {
        consumerId: '',
        firstFlag: '',
        templateVersion: 'v111'
      },
      fetchHalfApiProductLoading: true,
      fetchHalfApiProductProgress: 0,
      progressTimer: null
    }
  },

  onLoad({ param, consumerId, first }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (first) {
      this.form.firstFlag = first
    }

    if (consumerId) {
      this.form.consumerId = decryptByDES(decodeURIComponent(consumerId))
      this.$u.vuex('vuex_consumerId', this.form.consumerId)
      this.navigateToNextPage()
    } else {
      this.navigateToEnd()
    }
  },

  onUnload() {
    this.progressDone()
  },

  methods: {
    async fetchHalfApiProduct() {
      const res = await fetchHalfApiProduct(this.form)
      if (res.data) {
        return res.data
      }
    },

    async navigateToNextPage() {
      try {
        this.updateProgress(10 * 1000)

        const url = await this.fetchHalfApiProduct()
        if (url) {
          window.location.href = url
          return
        }

        const routeMap = {
          offline: '/extreme/v111/apply/index',
          wechat: '/extreme/v111/qw/index',
          overloan: '/extreme/v111/applyOther/index',
          end: '/extreme/v111/download/index',
          wechat_official_account: '/extreme/v111/wechatOfficialAccount/index'
        }

        const nextFlow = await nextFlowNode({
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        })

        if (nextFlow.flow === 'halfapi') {
          window.location.href = nextFlow.url
          return
        }

        const path = routeMap[nextFlow.flow]
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `${path}?param=${urlParamString}`
        })
      } catch (e) {
        this.navigateToEnd()
      } finally {
        this.progressDone()
      }
    },

    progressDone() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      this.fetchHalfApiProductProgress = 100
    },

    updateProgress(duration) {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }

      const startTime = Date.now()
      const startProgress = this.fetchHalfApiProductProgress
      const targetProgress = 100

      this.progressTimer = setInterval(() => {
        const currentTime = Date.now()
        const elapsedTime = currentTime - startTime

        if (elapsedTime >= duration) {
          this.fetchHalfApiProductProgress = targetProgress
          clearInterval(this.progressTimer)
        } else {
          const progress = this.easeInOutQuad(
            elapsedTime,
            startProgress,
            targetProgress - startProgress,
            duration
          )
          this.fetchHalfApiProductProgress = Math.round(progress)
        }
      }, 50) // 每50毫秒更新一次进度
    },

    // easeInOutQuad 缓动函数
    easeInOutQuad(t, b, c, d) {
      t /= d / 2
      if (t < 1) return (c / 2) * t * t + b
      t--
      return (-c / 2) * (t * (t - 2) - 1) + b
    },

    navigateToEnd() {
      uni.navigateTo({
        url: `/extreme/v111/download/index?param=${encodeURIComponent(encryptByDES(JSON.stringify(this.form)))}&result=2`
      })
    }
  }
}
</script>

<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <view class="fetchHalfApiProductLoading" v-if="fetchHalfApiProductLoading">
        <view class="fetchHalfApiProductLoading-content">
          <view class="icon-loading">
            <view class="out-img"></view>
            <view class="inner-img"></view>
          </view>

          <view class="tips-1">尊敬的用户，您已完成资料填写</view>
          <view class="tips-2">正在为您评估额度和匹配服务机构，请耐心等待</view>
        </view>
      </view>

      <view class="footer">
        <view class="text">正在加载 ({{ fetchHalfApiProductProgress }}%)</view>
        <view class="process">
          <view class="inner" :style="{ width: `${fetchHalfApiProductProgress}%` }"></view>
        </view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '@/theme/v111/export/halfApiTransitStation.scss';
</style>
