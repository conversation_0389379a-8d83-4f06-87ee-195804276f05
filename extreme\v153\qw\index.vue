<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/269bbbddfdf844cebd8a8ae0debc392e.png"
        class="feature-image"
      ></image>
      <view class="product-status">
        <view class="status-item">
          <image
            class="status-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/5e2644e52c6342139e13e1b4d34c4b88.png"
          />
        </view>
        <view class="countdown">
          <text class="countdown-label">限时领取</text>
          <view class="countdown-timer">
            <text class="time-number">{{ formatTime(countdownHours) }}</text>
            <text class="time-separator">小时</text>
            <text class="time-number">{{ formatTime(countdownMinutes) }}</text>
            <text class="time-separator">:</text>
            <text class="time-number">{{ formatTime(countdownSeconds) }}</text>
          </view>
          <text class="countdown-label">失效</text>
        </view>
      </view>

      <view class="card">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/d974e8c0abc54786adf491275cae2275.png"
        />
      </view>
    </view>
    <view class="page-footer">
      <view class="confirm-tips">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/55328190f6c14d1297e166abb133b20a.png"
          class="icon-wechat"
        />
        <text>使用您本人微信验证后才可领取额度</text>
      </view>
      <template v-if="applyUrl">
        <a class="btn confirm-btn" :href="applyUrl" target="_blank">
          立即联系
          <text v-if="confirmCountdownNumber">（{{ confirmCountdownNumber }}S）</text>
        </a>
        <view class="btn cancel-btn" @click="clickCancel">额度够用</view>
      </template>
    </view>



    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyOnlineProduct, fetchOnlineProduct, fetchWechatAutoJump, fetchWechatAutoTime, checkProductSequenceEnd } from '@/apis/common-2'

import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getApplyOnlineProductLink, matchingOnlineProduct } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import routeMap from '../packages/routeMap.js'
export default {
  name: 'qwIndex',
  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},

      countdownHours: 24,
      countdownMinutes: 0,
      countdownSeconds: 0,
      productCountdownTimer: null,
      confirmCountdownNumber: 0,
      confirmCountdownTimer: null,
      productList: [],
      // 企微自动跳转: 1 为不自动跳转 2为自动跳转
      wechatAutoJump: '',
      // 跳转链接
      applyUrl: '',
      // 平台
      platform: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
      if (isPageNested()) {
        savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    }

    this.startProductCountdown()
  },

  async onShow() {
    // 1. 判断当前流程是否结束
    const isEnd = await this.checkProductSequenceEnd()

    if (isEnd) {
      this.navigateToNextFlow()
      return
    }

    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      // 2. 根据接口获取是否自动跳
      await this.fetchWechatAutoJump()

      this.platform = uni.getSystemInfoSync().osName

      this.applyUrl = getApplyOnlineProductLink({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId,
        redirectUrl: encodeURIComponent(window.location.href)
      })

      // 3. 如果自动跳，根据接口获取时间
      if (this.wechatAutoJump == 2) {
        // 获取倒计时时间
        await this.fetchWechatAutoTime()
        // 启动倒计时，倒计时结束后自动申请产品
        this.timerStart()
      }
    } else {
      this.navigateToNextFlow()
    }
  },

  onUnload() {
    this.timerStop()
    // 清理产品倒计时定时器
    if (this.productCountdownTimer) {
      clearInterval(this.productCountdownTimer)
      this.productCountdownTimer = null
    }
  },

  methods: {
    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
        // limit: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },
    async fetchWechatAutoTime() {
      const res = await fetchWechatAutoTime({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 0 立即跳转 5 5秒后跳转 10 10秒后跳转
      this.confirmCountdownNumber = res.data || 0
    },
    async checkProductSequenceEnd() {
      const res = await checkProductSequenceEnd({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      return res.data
    },
    startProductCountdown() {
      this.countdownHours = 24
      this.countdownMinutes = 0
      this.countdownSeconds = 0

      // 清除已有计时器
      if (this.productCountdownTimer) {
        clearInterval(this.productCountdownTimer)
      }

      // 启动新的计时器
      this.productCountdownTimer = setInterval(() => {
        if (this.countdownSeconds > 0) {
          this.countdownSeconds--
        } else if (this.countdownMinutes > 0) {
          this.countdownMinutes--
          this.countdownSeconds = 59
        } else if (this.countdownHours > 0) {
          this.countdownHours--
          this.countdownMinutes = 59
          this.countdownSeconds = 59
        } else {
          // 倒计时结束，停止计时
          clearInterval(this.productCountdownTimer)
          this.productCountdownTimer = null
        }
      }, 1000)
    },
    formatTime(time) {
      return time.toString().padStart(2, '0')
    },
    timerStart() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
      }

      // 如果倒计时时间为0，立即跳转
      if (this.confirmCountdownNumber === 0) {
        this.applyProduct()
        return
      }

      this.confirmCountdownTimer = setInterval(() => {
        this.confirmCountdownNumber--
        if (this.confirmCountdownNumber === 0) {
          this.applyProduct()
        }
      }, 1000)
    },

    timerStop() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
        this.confirmCountdownTimer = null
        this.confirmCountdownNumber = 0
      }
    },

    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 1 不自动跳转 2自动跳转
      this.wechatAutoJump = res.data || 1
    },

    async getFlowData() {
      const flowData = getFlowData('wechat')
      if (flowData) {
        this.productList = flowData.productList

        if (!this.form.consumerId) {
          this.form.consumerId = this.productList[0].consumerId
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await matchingOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )
      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      if (nextFlow.flow === 'end') {
        if (this.vuex_halfJumpUrl) {
          window.location.href = this.vuex_halfJumpUrl
          return
        }
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      clearFlowData('wechat')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },



    clickCancel() {
      throttle(this.cancelHandler)
    },

    cancelHandler() {
      this.timerStop()
      this.navigateToNextFlow()
    }
  }
}
</script>

<style lang="scss" scoped>
.feature-image {
  width: 100%;
  height: 784rpx;
}
.page-container {
  height: 100vh;
  background: #fff;
  position: relative;
  // display: flex;
  // flex-direction: column;

  .page-content {
    // padding-bottom: 600rpx;
    flex: 1;
    overflow: hidden auto;
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.302);

    .btn {
      display: block;
      padding: 22rpx;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      font-size: 40rpx;
      line-height: 56rpx;
      text-align: center;
    }

    .confirm-btn {
      margin-bottom: 15rpx;
      border: 1rpx solid #2aa245;
      background: #2aa245;
      color: #ffffff;
      text-decoration: none;
    }

    .cancel-btn {
      margin-bottom: 20rpx;
      // border: 1rpx solid #666;
      color: #666;
    }

    .confirm-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 37rpx;
      gap: 8rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      line-height: 34rpx;

      .icon-wechat {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.product-status {
  position: absolute;
  width: 498rpx;
  height: 91rpx;
  top: 410rpx;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 10rpx;
  padding: 40rpx 20rpx 20rpx 20rpx;
  background: url('https://cdn.oss-unos.hmctec.cn/common/path/1566019efcda4b5ba0506016d228bb3e.png')
    no-repeat center;
  background-size: cover;
  border-radius: 8rpx;
}

.status-item {
  // 状态项布局
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
}

.status-icon {
  // 状态图标样式
  width: 48rpx;
  height: 48rpx;
}

.status-text {
  // 状态文字样式

  font-weight: 400;
  font-size: 28rpx;
  color: #6a7a96;
  line-height: 35rpx;
}

/* 倒计时 */
.countdown {
  // 倒计时容器布局
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15rpx;
}

.countdown-label {
  // 倒计时标签样式

  font-weight: 400;
  font-size: 24rpx;
  color: #fe1010;
  line-height: 35rpx;
}

.countdown-timer {
  // 倒计时数字容器
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10rpx;
}

.time-number {
  // 时间数字样式
  width: 38rpx;
  height: 38rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fe1010;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-weight: normal;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 34rpx;
}

.time-separator {
  // 时间分隔符样式

  font-weight: 400;
  font-size: 24rpx;
  color: #6a7a96;
  line-height: 35rpx;
}

.card {
  position: absolute;
  top: 596rpx;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;

  image {
    width: 683rpx;
    height: 276rpx;
  }
}


</style>
