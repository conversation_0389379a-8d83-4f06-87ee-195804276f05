<template>
  <view class="services">
    <view class="services-title">专享超级服务</view>
    <view class="services-list">
      <view class="service-item">
        <image
          class="service-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/ff0d54d6dec94ec788f5bf8ce7804261.png"
        ></image>
        <text class="service-name">循环额度</text>
      </view>
      <view class="service-item">
        <image
          class="service-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/e83e34770c8d475cbd982f09d090eb97.png"
        ></image>
        <text class="service-name">灵活借还</text>
      </view>
      <view class="service-item">
        <image
          class="service-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/428186e8af0a44438cc9226a66e5335c.png"
        ></image>
        <text class="service-name">快速放款</text>
      </view>
    </view>
    <!-- <view class="services-bottom" v-if="developer">
      <view class="services-bottom-text">
        <text>{{ developer }}</text>
        <text>{{ developerPhone }}</text>
      </view>
    </view> -->
  </view>
</template>

<script>
export default {
  name: 'Services',
  // props: {
  //   developer: {
  //     type: String,
  //     default: ''
  //   },
  //   developerPhone: {
  //     type: String,
  //     default: ''
  //   }
  // },
}
</script>

<style lang="scss" scoped>
.services {
  padding: 60rpx 40rpx 30rpx;
  margin: 30rpx;
  background: linear-gradient(
    135deg,
    #f6e6d6 0%,
    #fffefe 51%,
    #ffffff 53%,
    #ffffff 55%,
    #f0d0a9 100%
  );
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .services-title {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30rpx;
    font-weight: 400;
    font-size: 34rpx;
    color: #3e1000;
    line-height: 45rpx;

    &::before,
    &::after {
      content: '';
      width: 140rpx;
      height: 1rpx;
      background-color: #3e1000;
    }
  }

  .services-list {
    margin-top: 30rpx;
    padding: 30rpx 60rpx;
    background: #fffcf7;
    border-radius: 23rpx 23rpx 23rpx 23rpx;
    display: flex;
    justify-content: space-between;

    .service-item {
      display: flex;
      flex-direction: column;
      gap: 5rpx;
      align-items: center;

      .service-icon {
        width: 80rpx;
        height: 80rpx;
      }

      .service-name {
        font-weight: 400;
        font-size: 24rpx;
        color: #171a1d;
        line-height: 32rpx;
      }
    }
  }
  .services-bottom {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
    font-size: 24rpx;
    margin-top: 21rpx;
    color:#ACAAAF;
    line-height: 32rpx;
    .services-bottom-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10rpx;
    }
  }
}
</style>
