<template>
  <uni-popup ref="popup" type="center" :mask-click="false" backgroundColor="#fff">
    <view class="loading-popup">
      <half-api-transit-station
        :customTotalTime="customTotalTime"
        @progress-complete="handleProgressComplete"
      />
    </view>
  </uni-popup>
</template>

<script>
import HalfApiTransitStation from './index.vue'

export default {
  name: 'LoadingPopup',
  components: {
    HalfApiTransitStation
  },
  props: {
    customTotalTime: {
      type: Number,
      default: 10000
    }
  },
  methods: {
    // 打开弹窗
    open() {
      this.$refs.popup.open()
    },
    // 关闭弹窗
    close() {
      this.$refs.popup.close()
    },
    // 处理进度完成事件
    handleProgressComplete() {
      this.$emit('progress-complete')
    }
  }
}
</script>

<style lang="scss" scoped>
.loading-popup {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}
</style>
