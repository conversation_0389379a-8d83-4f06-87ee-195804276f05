<template>
  <view class="loan-details" @click="$emit('card-click')">
    <view class="detail-row">
      <view class="detail-label">借款期限</view>
      <view class="detail-right">
        <picker
          range-key="label"
          :range="monthRange"
          :value="monthIndex"
          @change="handleMonthChange"
        >
          <view class="picker-content">
            <text class="period-value">{{ monthRange[monthIndex].value }}个月</text>
            <view class="rate-badge" v-if="monthRange[monthIndex].value === 12">高通过率</view>
            <image class="detail-arrow" src="https://cdn.oss-unos.hmctec.cn/common/path/c06fa46e843141ba9961ba39386a7766.png" />
          </view>
        </picker>
      </view>
    </view>

    <view class="detail-row">
      <view class="detail-label">还款计划</view>
      <view class="repayment-value">
        每月约应还<text class="amount-highlight">￥{{ monthlyPay }}</text>
      </view>
    </view>

    <view class="disclaimer-text">实际贷款利息及放款金额以最终审批为准</view>
  </view>
</template>

<script>
export default {
  name: 'LoanDetailsCard',

  props: {
    // 当前选中的月份索引
    monthIndex: {
      type: Number,
      default: 0
    },
    // 月份选项数组
    monthRange: {
      type: Array,
      default: () => [
        { label: '3个月', value: 3 },
        { label: '6个月', value: 6 },
        { label: '12个月', value: 12 },
        { label: '36个月', value: 36 }
      ]
    },
    // 每月还款金额
    monthlyPay: {
      type: [String, Number],
      default: '0.00'
    }
  },



  methods: {
    handleMonthChange(e) {
      this.$emit('month-change', e.detail.value)
    }
  }
}
</script>

<style scoped lang="scss">
.loan-details {
  margin: 30rpx;
  padding: 30rpx;
  background: #ffffff;
  border-radius: 8rpx;

  .detail-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20rpx 0;

    &:first-child {
      border-bottom: 1rpx solid #f0f0f0;
    }

    .detail-label {
      font-weight: 500;
      font-size: 28rpx;
      color: #3D3D3D;
      line-height: 41rpx;
    }

    .detail-right {
      display: flex;
      align-items: center;

      .picker-content {
        display: flex;
        align-items: center;
      }

      .period-value {
        font-weight: 400;
        font-size: 28rpx;
        color: #1D283A;
        line-height: 39rpx;
      }

      .rate-badge {
        padding: 6rpx 12rpx;
        background: #ff4444;
        border-radius: 20rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #ffffff;
        line-height: 28rpx;
        margin-left: 10rpx;
      }

      .detail-arrow {
        width: 40rpx;
        height: 48rpx;
        margin-left: 10rpx;
      }
    }

    .repayment-value {
      font-weight: 400;
      font-size: 28rpx;
      color: #1D283A;
      line-height: 39rpx;

      .amount-highlight {
        color: #333333;
        font-weight: 500;
      }
    }
  }

  .disclaimer-text {
    font-weight: 400;
    font-size: 20rpx;
    color: #999999;
    line-height: 29rpx;
  }
}
</style>
