<template>
  <view class="step-buttons">
    <view v-if="showPrev" class="step-button prev-step" @click="handlePrevClick">
      <image
        class="step-button__icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/ec457cbeaea14a3fb9712ad730c48b5e.png"
        mode="scaleToFill"
      />
      <view class="step-button__text">上一步</view>
    </view>
    <view v-if="showNext" class="step-button next-step" @click="handleNextClick">
      <view class="step-button__text">下一步</view>
      <image
        class="step-button__icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/dc9b1d3daf2d4725838141f6e2a61d15.png"
        mode="scaleToFill"
      />
    </view>
  </view>
</template>

<script>
export default {
  name: 'StepButtons',
  props: {
    showPrev: {
      type: <PERSON>olean,
      default: true
    },
    showNext: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleNextClick() {
      this.$emit('next')
    },
    handlePrevClick() {
      this.$emit('prev')
    }
  }
}
</script>

<style lang="scss" scoped>
.step-buttons {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 32rpx 120rpx;
}

.step-button {
  display: flex;
  align-items: center;
  gap: 12rpx;

  &__text {
    font-weight: 400;
    font-size: 28rpx;
    color: #49c2c4;
    line-height: 40rpx;
  }

  &__icon {
    width: 48rpx;
    height: 48rpx;
  }
}

.prev-step {
  justify-content: flex-start;
}

.next-step {
  justify-content: flex-end;
}

// 当只有一个按钮时的样式
.step-buttons:has(.prev-step:only-child) {
  justify-content: flex-start;
}

.step-buttons:has(.next-step:only-child) {
  justify-content: flex-end;
}
</style>
