<script>
export default {
  name: 'declaration-hr-yg-hmc-zxbc',

  methods: {
    bindOpenUrl(url) {
      window.location.href = url
    }
  }
}
</script>

<template>
  <view class="declaration-container">
    <view v-if="vuex_declaration && !vuex_templateSig" v-html="vuex_declaration"></view>
    <template v-else>
      <view>
        <view
          @click="
            bindOpenUrl(
              'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=51010702002566'
            )
          "
        >
          <image
            class="police-icon"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/h5/images/icon/police.png"
          ></image>
        </view>
        <view>
          <view>资金来源：安庆市银谷小额贷款有限责任公司</view>
          <view
            >郑重声明：官方不会以任何名义向您收取手续费，一切贷前收取手续费的均有可能是诈骗</view
          >
          <view>请根据个人能力合理贷款，理性消费，避免逾期</view>
          <view
            >任何假借惠融钱包官方、员工或合作机构名义，通过电话、短信、微信等要求您点击其发送的链接、转账、索取验证码，诱使您支付手续费等款项的行为，均为欺诈。请您谨慎付款，并与惠融钱包客服400-963-8005联系核实。</view
          >
          <view>举报邮箱：<EMAIL></view>
          <view>客服热线：400-963-8005</view>
          <view>Copyright©2019 重庆惠融数字科技有限公司</view>
          <view>渝ICP备2023000211号-3</view>
        </view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.declaration-container {
  padding: 30rpx 60rpx;
  font-size: 20rpx;
  color: #ccc;
  line-height: 36rpx;
  text-align: center;

  .police-icon {
    width: 30rpx;
    height: 30rpx;
  }
}
</style>
