<template>
  <view class="form-section">
    <view class="form-header">
      <image
        class="form-header__icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/c248778624ff427ba37fa110c7fbcf79.png"
        mode="scaleToFill"
      />
      <view class="form-header__title">信用情况</view>
    </view>

    <view class="form-item">
      <view class="form-item__label">芝麻分</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: formData.sesameScoreIndex === 0 }"
          @click="selectSesameScore(0)"
          >700分以上</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: formData.sesameScoreIndex === 1 }"
          @click="selectSesameScore(1)"
          >650-700分</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: formData.sesameScoreIndex === 2 }"
          @click="selectSesameScore(2)"
          >600-650分</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: formData.sesameScoreIndex === 3 }"
          @click="selectSesameScore(3)"
          >600分以下</view
        >
      </view>
    </view>

    <view class="form-item">
      <view class="form-item__label">逾期</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: formData.isOverdue === true }"
          @click="selectOverdue(true)"
          >有逾期</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: formData.isOverdue === false }"
          @click="selectOverdue(false)"
          >无逾期</view
        >
      </view>
    </view>
  </view>
</template>

<script>
const SESAME_VALUES = [115, 113, 112, 110]

export default {
  name: 'CreditInfoForm',
  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({
        sesameScoreIndex: -1,
        sesameId: null,
        isOverdue: null
      })
    }
  },
  computed: {
    formData: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    selectSesameScore(index) {
      this.formData = {
        ...this.formData,
        sesameScoreIndex: index,
        sesameId: SESAME_VALUES[index]
      }
    },
    selectOverdue(value) {
      this.formData = {
        ...this.formData,
        isOverdue: value
      }
    },
    getFormData() {
      return this.formData
    },
    validateForm() {
      const fieldChecks = [
        {
          field: 'sesameScoreIndex',
          value: this.formData.sesameScoreIndex,
          message: '请选择芝麻分'
        },
        {
          field: 'isOverdue',
          value: this.formData.isOverdue,
          message: '请选择是否有逾期'
        }
      ]

      // 找到第一个未选择的字段
      const firstUnselectedField = fieldChecks.find((item) =>
        item.field === 'sesameScoreIndex' ? item.value === -1 : item.value === null
      )

      if (firstUnselectedField) {
        uni.showToast({
          title: firstUnselectedField.message,
          icon: 'none'
        })
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.form-section {
  padding: 44rpx 32rpx 0;
  background-color: #ffffff;
  margin-bottom: 90rpx;

  .form-header {
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    &__icon {
      width: 46rpx;
      height: 46rpx;
    }

    &__title {
      font-weight: 500;
      font-size: 32rpx;
      color: #3d3d3d;
      line-height: 46rpx;
    }
  }
}

.form-item {
  margin-bottom: 40rpx;

  &__label {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 40rpx;

    &::before {
      content: '';
      width: 8rpx;
      height: 24rpx;
      background: linear-gradient(179deg, #40bdb7 9%, #49c2c4 30%);
    }
  }

  &__content {
    display: flex;
    gap: 20rpx;

    &-item {
      width: 216rpx;
      height: 64rpx;
      background: #f2f2f2;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #9e9e9e;
      line-height: 40rpx;
      border: 2rpx solid transparent;

      &.active {
        background: rgba(63, 193, 188, 0.17);
        border: 2rpx solid #3fc1bc;
        color: #49c2c4;
      }
    }
  }
}
</style>
