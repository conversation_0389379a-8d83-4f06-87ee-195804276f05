<template>
  <view class="bottom-confirm">
    <view class="agreement" v-if="agreementList && agreementList.length > 0">
      我已阅读并同意
      <text
        class="agreement-link"
        v-for="item in agreementList"
        :key="item.protocolId"
        @click="handleAgreementClick(item)"
      >
        《{{ item.name }}》
      </text>
    </view>
    <view class="confirm-btn" @click="handleConfirmClick">{{ confirmText }}</view>
  </view>
</template>

<script>
export default {
  name: 'BottomConfirm',

  props: {
    // 协议列表
    agreementList: {
      type: Array,
      default: () => []
    },
    // 确认按钮文本
    confirmText: {
      type: String,
      default: '确认申请'
    }
  },

  methods: {
    handleAgreementClick(agreement) {
      this.$emit('agreement-click', agreement)
    },

    handleConfirmClick() {
      this.$emit('confirm-click')
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #226ef7;

.bottom-confirm {
  padding: 30rpx 30rpx 60rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0,0,0,0.302);
  border-radius: 8rpx 8rpx 0rpx 0rpx;

  .agreement {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 39rpx;

    .agreement-link {
      color: $color-primary;
    }
  }

  .confirm-btn {
    margin-top: 23rpx;
    background: $color-primary;
    border-radius: 30rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #FFFFFF;
    line-height: 56rpx;
    padding: 22rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
