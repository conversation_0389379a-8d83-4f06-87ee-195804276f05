<template>
  <view class="page-container">
    <view class="result-container">
      <query-result-header />
      <service-list :institutionList="institutionList" />
      <query-description />
    </view>
    <result-ad />
  </view>
</template>

<script>
import { decodeUrlParam } from '@/utils/queryParams.js'
import { getInstitutionByCity } from '@/apis/common.js'
import QueryResultHeader from '@/components/loan-city-query-blue/QueryResultHeader.vue'
import ServiceList from '@/components/loan-city-query-blue/ServiceList.vue'
import QueryDescription from '@/components/loan-city-query-blue/QueryDescription.vue'
import ResultAd from '@/components/loan-city-query-blue/ResultAd.vue'

export default {
  components: {
    QueryResultHeader,
    ServiceList,
    QueryDescription,
    ResultAd
  },
  data() {
    return {
      form: {
        cityName: '',
        cityCode: ''
      },
      institutionList: []
    }
  },

  onLoad({ param }) {
    if (param) {
      Object.assign(this.form, decodeUrlParam(param))
    }
    this.getInstitutionByCity()
  },

  methods: {
    async getInstitutionByCity() {
      try {
        const res = await getInstitutionByCity({
          cityCode: this.form.cityCode
        })
        this.institutionList = res.data || []
      } catch (error) {
        console.error('Failed to get institution by city:', error)
        this.institutionList = []
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background-color: #f6f6f8;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/********************************.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  padding-top: 333rpx;
}

.result-container {
  margin: 0 31rpx;
  background: #ffffff;
  border-radius: 16rpx;
  padding-bottom: 20rpx;
}
</style>
