<template>
  <view class="card amount-input-container">
    <view class="amount-title">最高可借(元）</view>
    <view class="input-container">
      <view class="amount-symbol">￥</view>
      <input
        class="amount-input"
        type="text"
        value="20,0000"
        :value="formattedAmount"
        @blur="demandAmountBlur"
        @focus="demandAmountFocus"
        ref="amountInput"
      />
      <view class="edit-amount-btn" @click="clickEditAmount">修改金额</view>
    </view>
    <view class="amount-tips">该额度为授信金额，您可根据实际需求金额提现</view>
  </view>
</template>

<script>
import { formatAmount, parseAmount } from '@/utils/amount'

export default {
  name: 'AmountInput',

  props: {
    amount: {
      type: [String, Number],
      default: '50000'
    }
  },

  data() {
    return {
      inputValue: ''
    }
  },

  computed: {
    formattedAmount() {
      return this.inputValue || formatAmount(this.amount)
    }
  },

  watch: {
    amount: {
      immediate: true,
      handler(newVal) {
        this.inputValue = formatAmount(newVal)
      }
    }
  },

  methods: {
    demandAmountBlur() {
      this.inputValue = ''
      let amount = parseAmount(this.$refs.amountInput.$el.querySelector('input').value)
      amount = parseInt(amount)

      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.$emit('update:amount', '50000')
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.$emit('update:amount', '50000')
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.$emit('update:amount', '200000')
        return
      }

      this.$emit('update:amount', amount.toString())
    },

    demandAmountFocus() {
      this.inputValue = parseAmount(this.amount)
    },

    clickEditAmount() {
      this.$refs.amountInput.$el.querySelector('input').focus()
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #ff5828;

.card {
  position: relative;
  padding: 30rpx 0;
  margin: 0 30rpx 40rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  border: 1rpx solid #ffffff;
}

.amount-input-container {
  padding: 30rpx !important;

  .amount-title {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 35rpx;
  }

  .input-container {
    display: flex;
    align-items: center;
    border-bottom: 1rpx solid #cdcdcd;

    .amount-symbol {
      font-weight: 700;
      font-size: 48rpx;
      color: #333333;
      line-height: 63rpx;
    }

    .amount-input {
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      line-height: 106rpx;
      font-family: DIN;
    }

    .edit-amount-btn {
      flex-shrink: 0;
      background: $color-primary;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #eef9f3;
      line-height: 22rpx;
      text-align: center;
      padding: 15rpx 20rpx;
    }
  }

  .amount-tips {
    margin-top: 20rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 35rpx;
  }
}
</style>
