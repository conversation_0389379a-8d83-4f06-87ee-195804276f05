<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="40"
    height="40"
    viewBox="0 0 40 40"
  >
    <defs>
      <clipPath id="master_svg0_559_12914">
        <rect x="0" y="0" width="40" height="40" rx="0" />
      </clipPath>
      <linearGradient x1="0.5" y1="1" x2="0.5" y2="0" id="master_svg1_559_18364">
        <stop offset="0%" :stop-color="startColor" stop-opacity="1" />
        <stop offset="100%" :stop-color="endColor" stop-opacity="1" />
      </linearGradient>
      <linearGradient x1="0.5" y1="1" x2="0.5" y2="0" id="master_svg2_559_18196">
        <stop offset="0%" :stop-color="endColor" stop-opacity="1" />
        <stop offset="100%" stop-color="#FFFFFF" stop-opacity="0" />
      </linearGradient>
    </defs>
    <g clip-path="url(#master_svg0_559_12914)">
      <g>
        <path
          d="M16.433,0L23.5675,0C32.6441,0,40,7.35678,40,16.4323L40,23.5671C40,32.6426,32.6441,39.9994,23.5681,40L16.433,40C7.3571,40,0,32.6426,0,23.5677L0,16.4323C0,7.35678,7.3571,0,16.433,0Z"
          fill-rule="evenodd"
          fill="url(#master_svg1_559_18364)"
          fill-opacity="1"
        />
        <path
          d="M23.5675,0L16.433,0C7.3571,0,0,7.35678,0,16.4323L0,23.5677C0,32.6426,7.3571,40,16.433,40L23.5681,40C32.6441,39.9994,40,32.6426,40,23.5671L40,16.4323C40,7.35678,32.6441,0,23.5675,0ZM5.52013,5.51996Q10.0403,1,16.433,1L23.5675,1Q29.9605,1,34.4803,5.51993Q39,10.0397,39,16.4323L39,23.5671Q39,29.9596,34.4804,34.4796Q29.9607,38.9996,23.5681,39L16.433,39Q10.0404,39,5.52014,34.4798Q1,29.9598,0.999999,23.5677L1,16.4323Q1,10.0399,5.52013,5.51996Z"
          fill-rule="evenodd"
          fill="url(#master_svg2_559_18196)"
          fill-opacity="1"
        />
      </g>
      <g>
        <ellipse cx="20.5" cy="14.5" rx="5.5" ry="5.5" :fill="iconColor" fill-opacity="1" />
      </g>
      <g>
        <path
          d="M11 27C11 24.238576250846034 13.238576250846034 22 16 22L25 22C27.761423749153966 22 30 24.238576250846034 30 27L30 28C30 30.209138999323173 28.209138999323173 32 26 32L15 32C12.790861000676827 32 11 30.209138999323173 11 28Z"
          :fill="iconColor"
          fill-opacity="1"
        />
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  name: 'user',
  props: {
    startColor: {
      type: String,
      default: '#78B1FF'
    },
    endColor: {
      type: String,
      default: '#1678FF'
    },
    iconColor: {
      type: String,
      default: '#FFFFFF'
    }
  }
}
</script>
