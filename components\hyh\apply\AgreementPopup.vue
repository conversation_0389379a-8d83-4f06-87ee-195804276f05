<template>
  <uni-popup
    background-color="#fff"
    ref="popup"
    type="bottom"
    border-radius="32rpx 32rpx 0 0"
  >
    <view class="agreement-popup">
      <view class="agreement-container">
        <view v-for="agreement in agreementList" :key="agreement.protocolId">
          <view class="agreement-content">
            <div v-html="agreement.content"></div>
          </view>
        </view>
      </view>
      <view class="agreement-agree-container">
        <view class="agreement-agree-btn" @click="handleAgreeClick">{{ agreeText }}</view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'AgreementPopup',

  props: {
    // 协议列表
    agreementList: {
      type: Array,
      default: () => []
    },
    // 同意按钮文本
    agreeText: {
      type: String,
      default: '同意并继续'
    }
  },

  methods: {
    open() {
      this.$refs.popup.open()
    },

    close() {
      this.$refs.popup.close()
    },

    handleAgreeClick() {
      this.$emit('agree-click')
      this.close()
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #226ef7;

.agreement-popup {
  padding: 40rpx 50rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/a2b94098ce43424aa6d2b4b722c36c65.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-content {
    padding-bottom: 20rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style>
