<template>
  <view class="page-container">

    <!-- <Header @click.native="navigateToIndex" /> -->

    <view class="header">
      <view class="header-text">恭喜您，预审通过！</view>
      <view class="sub-header-text">经评估，您有机会享更高额度，更低利息</view>
    </view>

    <view class="main-product">
      <image
        class="right-icon-header"
        src="https://cdn.oss-unos.hmctec.cn/common/path/a15bb1dbcc5d4bfba070ca3a72a92654.png"
      />
      <view class="main-product-info">
        <view class="product">
          <image
            class="icon-header"
            src="https://cdn.oss-unos.hmctec.cn/common/path/a7d3f678fba04d1c9bce5ab2ae9ae87f.png"
          />
          <text class="product-name">将由{{mainProduct.name}}提供</text>
        </view>
      </view>
      <view class="main-product-content">
        <view class="main-product-content-item">
          <view class="product-amount">
            <view class="amount-label">最高借款额度(元)</view>
            <view class="amount-value">{{mainProduct.loanableFundsBig | toThousandFilter }}</view>
          </view>
          <view class="product-amount">
            <view class="amount-label">最低年化利率</view>
            <view class="amount-value"> {{mainProduct.interestRateLittle | toPriceAbbreviations }}%</view>
          </view>
        </view>
        <view class="apply-button" @click="clickApply(mainProduct)">立即申请</view>
      </view>
    </view>

    <view class="recommended-products">
      <view class="title">
        <text class="section-title">更多推荐</text>
      </view>

      <view class="product-item" v-for="(item, index) in productList" :key="index">
        <view class="product">
          <image class="product-icon" :src="item.logo"></image>
          <text class="product-name">{{ item.name }}</text>
        </view>
        <view class="product-details">
          <view class="product-features">
            <view class="product-features-item">
              <text class="product-features-value highlight">{{
                item.loanableFundsBig | toThousandFilter
              }}</text>
              <text class="product-features-label">最高额度(元)</text>
            </view>
            <view class="product-features-item">
              <text class="product-features-value"
                >{{item.interestRateLittle | toPriceAbbreviations }}%</text
              >
              <text class="product-features-label">最低年化利率</text>
            </view>
          </view>
          <view class="apply-button-small" @click="clickApply(item)">立即申请</view>
        </view>
      </view>
    </view>
    <!-- <Services /> -->
    <!-- <Services :developer="developer" :developerPhone="developerPhone" style="margin: 30rpx 30rpx 60rpx 30rpx;" /> -->
    <!-- <view class="page-footer">
      <view class="submit-btn" @click="navigateToEnd">下载APP，享优质产品</view>
    </view> -->
    <!-- <Declaration /> -->

    <FullScreenMatchLoading ref="loading" />

    <!-- 底部协议组件（固定定位） -->
    <AgreementFooter agreementKey="jqgj-dc" @agree="handleAgreementAgree" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import AlertBar from '@/components/alert/AlertBar.vue'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct } from '@/apis/common-2'
import Header from '@/components/header/header-jqgj.vue'
import Declaration from '@/components/footer/declaration-component/declaration-ycl.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import Services from '@/components/services/index.vue'
import { matchingOnlineProduct } from '@/apis/common'
import AgreementFooter from '@/components/jqgj/applyOther/AgreementFooter.vue'
import routeMap from '../packages/routeMap.js'
export default {
  name: 'applyOther',

  components: {
    AlertBar,
    Declaration,
    Header,
    FullScreenMatchLoading,
    Services,
    AgreementFooter
  },

  data() {
    return {
      form: {},
      // developer: '开发者：成都雨诚兰信息科技有限公司',
      // developerPhone: '客服电话：028-83225733',
      productList: [],
      mainProduct: {},
      productCount: 0
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getFlowData()
  },

  methods: {
    navigateToEnd() {
      const path = '/extreme/v148/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },
    handleAgreementAgree() {
      // 协议同意后的处理逻辑，如果需要的话
    },

    async getFlowData() {
      const flowData = getFlowData('overloan')
      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
        this.productCount = flowData.productList.length
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
         // 设置 url 到 vuex
        this.$u.vuex('vuex_overloanWebview.url', res.data)
        // 跳转到 overloan-webview 页面
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `/extreme/v148/overloanWebview/index?param=${urlParamString}`
        })
        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style scoped lang="scss">

$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}
.page-container {
  min-height: 100vh;
  position: relative;
  background-color: #f6f6f8;
  padding-bottom: calc(130rpx + env(safe-area-inset-bottom));
  background: linear-gradient(180deg, #1678ff 13%, #f2f2f2 30%);
}

.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;
  background-color: #fff7eb;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}

.header {
  margin-bottom: 43rpx;
  margin-left: 30rpx;
  position: relative;
  padding-top: 48rpx;
  .header-text {
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 700;
    font-size: 52rpx;
    color: #fff;
    line-height: 78rpx;
    letter-spacing: 3px;
    text-align: left;
  }

  .sub-header-text {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 28rpx;
    color: rgba(255,255,255,0.9);
    line-height: 41rpx;
    text-align: left;
  }
}

.main-product {
  padding: 25rpx 29rpx 40rpx 29rpx;
  margin: 0 30rpx;
  position: relative;
  background: linear-gradient(180deg, #ffe7ca 20%, #ffffff 100%);
  border-radius: 8rpx;
  .right-icon-header{
    position: absolute;
    right: 0;
    top: 0;
    width: 222rpx;
    height: 176rpx;
  }
  .main-product-info {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 23rpx;
    .product {
      padding-left: 35rpx;
      display: flex;
      flex-direction: column;
      gap: 15rpx;
      .icon-header{
        width: 170rpx;
        height: 34rpx;
      }
      .product-logo {
        width: 48rpx;
        height: 48rpx;
      }

      .product-name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 24rpx;
        color: #A0826A;
        line-height: 35rpx;
      }
    }

    .product-users {
      display: flex;
      align-items: center;
      padding: 10rpx 7rpx 10rpx 10rpx;
      background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
      border-radius: 20rpx 0rpx 0rpx 20rpx;
      font-weight: 400;
      font-size: 20rpx;
      color: #5a352f;
      line-height: 28rpx;

      .number {
        font-size: 24rpx;
      }
    }
  }
  .main-product-content {
    display: flex;
    flex-direction: column;
    padding:45rpx 26rpx 13rpx 42rpx;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 8rpx;
    align-items: center;
    .main-product-content-item {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 15rpx;
      margin-bottom: 48rpx;
      .product-amount {
        margin-top: 5rpx;

        .amount-label {
          font-family: PingFang SC, PingFang SC;
          font-weight: normal;
          font-size: 24rpx;
          color: rgba(51,51,51,0.6);
          line-height: 34rpx;
          margin-bottom: 10rpx;
          text-align: center;
        }

        .amount-value {
          font-weight: 700;
          font-family: D-DIN;
          font-size: 72rpx;
          color: #333333;
          line-height: 78rpx;
          text-align: center;
        }
      }
    }
  }

  .product-rate {
    font-weight: normal;
    font-size: 24rpx;
    color: #333333;
    line-height: 34rpx;
    text-align: center;
  }

  .apply-button {
    width: 100%;
    margin: 0 55rpx 0;
    background-color: $color-primary;
    border-radius: 46rpx 46rpx 46rpx 46rpx;
    border: 1rpx solid #ffffff;
    font-weight: 400;
    font-size: 36rpx;
    color: #fff;
    line-height: 52rpx;
    padding: 20rpx;
    text-align: center;
  }
}

.recommended-products {
  margin: 30rpx;

  .title {
    margin-bottom: 25rpx;
    display: flex;
    gap: 16rpx;
    align-items: flex-end;

    .section-title {
      font-family: Microsoft YaHei, Microsoft YaHei;
      font-weight: 700;
      font-size: 36rpx;
      color: #3d3d3d;
      line-height: 48rpx;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
    }
  }

  .product-item {
    margin-bottom: 30rpx;
    padding: 35rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 1rpx solid #ffffff;

    .product {
      margin-bottom: 20rpx;
      display: flex;
      gap: 20rpx;

      .product-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .product-name {
        font-weight: 400;
        font-size: 32rpx;
        color: #171a1d;
        line-height: 45rpx;
      }
    }

    .product-details {
      display: flex;
      gap: 80rpx;

      .product-features {
        flex: 1;
        display: flex;
        justify-content: space-between;

        .product-features-item {
          display: flex;
          flex-direction: column;
          gap: 8rpx;
          // align-items: center;

          .product-features-value {
            font-weight: 700;
            font-family: D-DIN;
            font-size: 38rpx;
            color: #171a1d;
            line-height: 41rpx;

            &.highlight {
              color: #f55c4d;
            }
          }

          .product-features-label {
            font-family: PingFang SC, PingFang SC;
            font-weight: normal;
            font-size: 24rpx;
            color: #919094;
            line-height: 34rpx;
          }
        }
      }

      .apply-button-small {
        padding: 12rpx 25rpx;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        border: 2rpx solid $color-primary;
        font-weight: 400;
        background-color: $color-primary;
        font-size: 36rpx;
        color: #fff;
        line-height: 50rpx;
      }
    }
  }
}
.page-footer {
  padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
  background: #ffffff;
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.302);
  .agreement {
    display: none;
    margin-bottom: 20rpx;
    // display: flex;
    gap: 5rpx;

    .agree-icon {
      flex-shrink: 0;
      width: 32rpx;
      height: 32rpx;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 34rpx;

      .name {
        color: $color-primary;
      }
    }
  }

  .submit-btn {
    padding: 21rpx;
    background: $color-primary;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-align: center;
  }
}
</style>
