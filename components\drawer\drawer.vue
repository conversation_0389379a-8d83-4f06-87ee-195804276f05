<template>
  <!-- 抽屉 -->
  <v-mask :show="show" @close="bindCloseMask" :maskCloseAble="maskCloseAble">
    <view
      class="drawer"
      :class="{
        'drawer-left': direction == 'left',
        'drawer-right': direction == 'right',
        'drawer-top': direction == 'top',
        'drawer-bottom': direction == 'bottom',
        'drawer-show': show
      }"
      :style="{
        width: width,
        borderRadius: radius,
        height: height,
        backgroundColor,
        backgroundImage,
        borderBottomLeftRadius: direction == 'bottom' ? 0 : radius,
        borderBottomRightRadius: direction == 'bottom' ? 0 : radius
      }"
      @tap.stop.prevent="bindDefault"
      @touchmove.stop.passive="bindDefault"
    >
      <view
        class="drawer-conent"
        :style="{
          overflow: contentOverflowLock ? 'hidden' : ''
        }"
        @tap.stop.prevent="bindDefault"
        @touchmove.stop.passive="bindDefault"
      >
        <slot></slot>
      </view>
      <slot name="drawer-btn"></slot>
    </view>
  </v-mask>
</template>

<script>
/**
 *  @FileName 抽屉
 *  @create    2021/06/23
 *  @update    2021/06/23
 *  <AUTHOR>
 */
import mask from '../mask/mask.vue'

export default {
  name: 'drawer',
  data() {
    return {
      CustomBar: this.CustomBar,
      show: false
    }
  },
  components: {
    'v-mask': mask
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    direction: {
      type: String,
      default: 'left'
    },
    width: {
      type: String,
      default: '100px'
    },
    height: {
      type: String,
      default: '100%'
    },
    radius: {
      type: String,
      default: ''
    },
    maskCloseAble: {
      type: Boolean,
      default: true
    },
    backgroundColor: {
      type: String,
      default: '#ffffff'
    },
    backgroundImage: {
      type: String,
      default: ''
    },
    contentOverflowLock: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value: {
      deep: true,
      handler(show) {
        this.show = show
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    /**
     *  @param { Function } init 初始化
     */
    init() {
      this.show = this.value
    },

    /**
     *  @param { Function } bindCloseMask 关闭弹窗
     */
    bindCloseMask(e) {
      this.$emit('close', e)
    },

    /**
     *  @param { Function } bindDefault 阻止冒泡 && 默认事件
     */
    bindDefault() {
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer {
  background-color: #ffffff;
  min-width: 100px;
  height: 100vh;
  border-radius: 0;
  margin: initial;
  -webkit-transition-duration: 0.3s;
  transition-duration: 0.3s;
  position: absolute;
  display: flex;
  flex-direction: column;
  .drawer-conent {
    flex: 1;
    min-height: 1upx;
    overflow-y: scroll;
    overflow: hidden scroll;
    -webkit-overflow-scrolling: touch;
  }
}

.drawer-left {
  left: 0;
  transform: translateX(-100%);
}
.drawer-right {
  right: 0;
  transform: translateX(100%);
}
.drawer-top {
  top: 0;
  transform: translateY(-100%);
}
.drawer-bottom {
  bottom: 0;
  transform: translateY(100%);
}
.drawer-show {
  transform: translateX(0%);
}
</style>
