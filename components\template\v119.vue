<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <scroll-view
        class="page-content"
        scroll-y="true"
        :scroll-into-view="scrollIntoView"
        scroll-with-animation="true"
        @scrolltolower="scrollIntoView = ''"
      >
        <Header />

        <view class="header">
          <view class="header-title">
            <view class="header-title-text">还差一步，即可获取额度</view>
            <view class="header-title-subtext">最高可借200,000元</view>
          </view>
          <view class="tag">
            <view class="tag-number">1000</view>
            <view class="tag-gap-text">人</view>
            已放款
          </view>
        </view>

        <view
          class="qualification card"
          :class="{ activeForm: activeForm === 'qualification' }"
          @click="setActiveForm('qualification')"
        >
          <view class="card-header">
            <view class="card-header-label">
              <view class="card-header-icon"></view>
              <view>资质信息</view>
            </view>
          </view>

          <view class="qualification-form" v-if="activeForm === 'qualification'">
            <view class="identity-form-item form-item">
              <view class="form-item-label">手机号</view>
              <view class="form-item-value">
                <input
                  type="tel"
                  style="text-align: right"
                  placeholder="请输入手机号"
                  maxlength="11"
                  placeholder-class="placeholder"
                  v-model="form.phone"
                  @blur="phoneBlur"
                />
              </view>
            </view>

            <template v-if="isVisible('age')">
              <view class="form-item-line"></view>
              <view class="identity-form-item form-item">
                <view class="form-item-label">年龄</view>
                <view class="form-item-value">
                  <input
                    placeholder="请填写年龄"
                    maxlength="3"
                    placeholder-class="placeholder"
                    v-model="form.age"
                    @blur="ageBlur"
                  />
                </view>
              </view>
            </template>

            <template v-if="isVisible('sesameId')">
              <view class="form-item-line"></view>
              <view
                class="qualification-form-item form-item"
                @click="activeFormItem = 'sesameScore'"
              >
                <view class="form-item-label">芝麻分</view>
                <view class="form-item-value">
                  <input
                    disabled
                    style="pointer-events: none"
                    :value="
                      sesameScoreOptions[form.sesameScoreIndex]
                        ? sesameScoreOptions[form.sesameScoreIndex].label
                        : ''
                    "
                    placeholder="请选择芝麻分"
                    placeholder-class="placeholder"
                  />
                  <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
                </view>
              </view>
              <view class="form-item-line" v-if="activeFormItem === 'sesameScore'"></view>
              <view class="radio-group" v-if="activeFormItem === 'sesameScore'">
                <view
                  class="radio"
                  v-for="(item, index) in sesameScoreOptions"
                  :key="index"
                  :class="{ selected: index === form.sesameScoreIndex }"
                  @click="clickSesameScore(index)"
                >
                  {{ item.label }}
                  <view v-if="index === form.sesameScoreIndex" class="radio-selected-icon"></view>
                </view>
              </view>
            </template>

            <template v-if="isVisible('isOverdue')">
              <view class="form-item-line"></view>
              <view class="qualification-form-item form-item" @click="activeFormItem = 'credit'">
                <view class="form-item-label">信用情况</view>
                <view class="form-item-value">
                  <input
                    disabled
                    style="pointer-events: none"
                    :value="
                      creditOptions[form.creditIndex] ? creditOptions[form.creditIndex].label : ''
                    "
                    placeholder="请选择信用情况"
                    placeholder-class="placeholder"
                  />
                  <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
                </view>
              </view>
              <view class="form-item-line" v-if="activeFormItem === 'credit'"></view>
              <view class="radio-group" v-if="activeFormItem === 'credit'">
                <view
                  class="radio"
                  v-for="(item, index) in creditOptions"
                  :key="index"
                  :class="{ selected: index === form.creditIndex }"
                  @click="clickCredit(index)"
                >
                  {{ item.label }}
                  <view v-if="index === form.creditIndex" class="radio-selected-icon"></view>
                </view>
              </view>
            </template>
          </view>
        </view>
      </scroll-view>
      <view class="page-footer">
        <view class="agreement">
          <view class="agreement-text">
            我已阅读并同意
            <text
              class="name"
              v-for="(item, index) in agreementList"
              :key="item.protocolId"
              @click="clickAgreement(item, index)"
            >
              《{{ item.name }}》
            </text>
          </view>
        </view>
        <view class="submit-btn" @click="clickSubmit">提交申请</view>
      </view>

      <uni-popup
        background-color="#fff"
        ref="agreementPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="agreement-popup">
          <view class="agreement-popup-bg"></view>

          <view class="agreement-popup-header">
            <view class="agreement-popup-title">请勿接听任何境外电话</view>
            <view class="agreement-popup-desc">
              任何放款前以缴纳
              <text class="highlight">保证金、利息等</text>
              名义的行为均为
              <text class="highlight">诈骗</text>
              ，任何要求您提供
              <text class="highlight">银行卡密码、验证码等</text>
              个人信息均为
              <text class="highlight">诈骗</text>
              。
            </view>
          </view>

          <view class="agreement-popup-body" v-if="agreementList.length > 0">
            <view class="agreement-name-container">
              <view
                class="agreement-name"
                v-for="(item, index) in agreementList"
                :key="index"
                @click="toggleAgreement(index)"
                :class="{ selected: index === agreementIndex }"
              >
                <view>{{ item.name }}</view>
                <view v-if="index !== agreementList.length - 1" class="agreement-name-line"></view>
              </view>
            </view>
            <view class="agreement-content" ref="agreementContent">
              <div v-html="agreementList[agreementIndex].content"></div>
            </view>
          </view>

          <view class="agreement-agree-container">
            <view class="agreement-agree-btn" @click="clickAgreeAndContinue">
              同意协议，继续申请
            </view>
            <view class="agreement-agree-tips"
              >若当前暂无匹配机构，请允许稍后持续为您匹配合适机构</view
            >
          </view>
        </view>
      </uni-popup>
      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>

<script>
import { validateMobile } from '@/utils/validators'
import LockNativeBack from '@/utils/lock-native-back'
import {
  fetchAptitudeRestock,
  fetchChannelAndTemplate,
  getTemplateConfig,
  matchingOnlineProduct,
  reportUV,
  saveAptitude
} from '@/apis/common'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-custom.vue'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v',

  components: {
    Header,
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        templateVersion: 'v119',
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        age: '',
        channelId: '',
        consumerId: '',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      // 默认展示资质信息
      activeForm: 'qualification',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 113,
          label: '650以上'
        },
        {
          value: 110,
          label: '650以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      agreementList: [],
      agreementIndex: 0,
      scrollIntoView: '',
      lockBack: null,
      visibleFields: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  async mounted() {
    setFlowNodes(['wechat', 'overloan', 'wechat_official_account'])
    // 从路由参数获取数据
    const query = this.$route.query

    this.$u.vuex('vuex_invite', query.i)

    if (query.param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(query.param)))
    }

    if (this.vuex_invite) {
      await this.fetchChannelAndTemplate()
      this.fetchVisibleFields()
    }

    this.fetchAgreement()

    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
    // 验证表单数据
    validateForm() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return false
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }

      if (this.isVisible('age') && !this.validateAge()) {
        return false
      }

      if (this.isVisible('sesameId') && !this.validateSesameScore()) {
        return false
      }

      if (this.isVisible('isOverdue') && !this.validateCredit()) {
        return false
      }

      return true
    },

    // 保存用户信息并开始匹配
    async saveUserInfoAndStartMatching() {
      const saveData = {
        phone: this.form.phone,
        channelId: this.form.channelId
      }
      if (this.isVisible('age')) {
        saveData.age = this.form.age
      }
      if (this.isVisible('sesameId')) {
        saveData.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      }
      if (this.isVisible('isOverdue')) {
        saveData.isOverdue = this.form.isOverdue
      }

      this.$u.vuex('vuex_phone', this.form.phone)

      const res = await saveAptitude(saveData)

      if (res.code == 200) {
        this.form.consumerId = res.data
        this.$u.vuex('vuex_consumerId', res.data)

        // 开始匹配
        this.navigateToNextFlow()
      }
    },

    async getTemplateConfig() {
      const res = await getTemplateConfig({
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        this.$u.vuex('vuex_theme', res.data.theme)
        this.$u.vuex('vuex_templateConfig', res.data)
      } else {
        this.$u.vuex('vuex_theme', '')
        this.$u.vuex('vuex_templateConfig', {})
      }
    },

    async fetchAgreement() {
      // 注册页协议
      this.agreementList = (await getAgreements(this.vuex_templateConfig.agreementKeyIndex)) || []
    },

    async fetchChannelAndTemplate() {
      const res = await fetchChannelAndTemplate({
        invite: this.vuex_invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        reportUV({
          channelId: this.form.channelId
        })

        this.getTemplateConfig()
      }
    },

    validateAge() {
      if (!this.isVisible('age')) return true

      this.form.age = this.form.age ? this.form.age.trim() : ''

      if (!this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(this.form.age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      if (this.form.age < 22 || this.form.age > 55) {
        uni.showToast({
          title: '仅支持 22-55 岁的用户',
          icon: 'none'
        })
        return false
      }

      return true
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (!this.validateForm()) {
        return
      }

      await this.saveUserInfoAndStartMatching()
    },

    clickAgreeAndContinue() {
      throttle(async () => {
        if (!this.validateForm()) {
          return
        }

        await this.saveUserInfoAndStartMatching()
      })
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v119/qw/index',
        overloan: '/extreme/v119/applyOther/index',
        end: '/extreme/v119/download/index',
        wechat_official_account: '/extreme/v119/wechatOfficialAccount/index'
      }

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    getUserParams() {
      const params = {}
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.monthIndex = this.form.monthIndex

      return params
    },

    clickAgreement(item, index) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
      this.$nextTick(() => {
        this.toggleAgreement(index)
      })
    },

    toggleAgreement(index) {
      this.agreementIndex = index
      this.$refs.agreementContent.$el.scrollTop = 0
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    ageBlur() {
      this.validateAge()
    },

    isVisible(fieldName) {
      if (!this.visibleFields || this.visibleFields.length === 0) {
        return false
      }
      return this.visibleFields.includes(fieldName)
    },

    async fetchVisibleFields() {
      if (!this.form.channelId) {
        this.visibleFields = []
        return
      }
      try {
        const res = await fetchAptitudeRestock({ channelId: this.form.channelId })
        if (res.code == 200 && Array.isArray(res.data)) {
          this.visibleFields = res.data
        } else {
          this.visibleFields = []
        }
      } catch (error) {
        this.visibleFields = []
      }
    },

    validateSesameScore() {
      if (!this.isVisible('sesameId')) return true

      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateCredit() {
      if (!this.isVisible('isOverdue')) return true

      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })
        return false
      }
      return true
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index
      this.activeFormItem = 'credit'
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
    }
  }
}
</script>

<style scoped lang="scss">
@import '@/theme/v119/export/index.scss';
</style>
