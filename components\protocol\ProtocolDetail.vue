<template>
  <view class="container">
    <web-view class="web-view" :src="rich" v-if="isLink" :fullscreen="false"></web-view>
    <view v-html="rich" v-else></view>
  </view>
</template>

<script>
import { getProtocolDetail } from '@/apis/common'
import { formatRichText } from '@/utils/utils'

export default {
  props: {
    protocolData: {
      type: Object,
      default: () => ({})
    },

    replaceData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      rich: '',
      param: {},
      isLink: false
    }
  },
  watch: {
    protocolData: {
      handler: 'init',
      immediate: true,
      deep: true
    },
    replaceData: {
      handler: 'init',
      immediate: true,
      deep: true
    }
  },
  methods: {
    init() {
      if (Object.keys(this.protocolData).length === 0) {
        return
      }

      getProtocolDetail(this.protocolData).then((res) => {
        this.isLink = res.data.isLink
        if (this.isLink) {
          this.rich = res.data.content
        } else {
          this.rich = formatRichText(res.data.content, this.replaceData)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  word-break: break-all;
  background-color: #ffffff;

  .web-view {
    width: 100%;
    height: 100%;

    ::v-deep iframe {
      width: 100% !important;
      height: 100% !important;
      border: none;
    }
  }
}
</style>
