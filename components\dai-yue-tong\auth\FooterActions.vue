<template>
  <view class="footer-container">
    <!-- 协议 -->
    <view class="agreement-container" @click="$emit('update:isAgreed', !isAgreed)">
      <!-- 协议图标 -->
      <uni-icons
        :type="isAgreed ? 'checkbox-filled' : 'circle'"
        size="12"
        class="agreement-checkbox"
        color="#44BFBD"
      ></uni-icons>

      <view class="agreement-text">
        注册代表同意
        <text
          v-for="(agreement, index) in agreementList"
          :key="agreement.protocolId"
          class="agreement-link"
          @click.stop="openAgreement"
        >
          《{{ agreement.name }}》<text v-if="index < agreementList.length - 1">、</text>
        </text>
      </view>
    </view>

    <view class="submit-btn" @click="handleSubmit">同意协议并激活额度</view>
  </view>
</template>

<script>
export default {
  name: 'FooterActions',
  props: {
    isAgreed: {
      type: Boolean,
      default: false
    },
    agreementList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    openAgreement() {
      this.$emit('open-agreement')
    },
    handleSubmit() {
      this.$emit('submit')
    }
  }
}
</script>

<style lang="scss" scoped>
.footer-container {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 750rpx;
  height: 206rpx;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.3);
  padding: 30rpx 0;
  z-index: 10;

  .agreement-container {
    display: flex;
    justify-content: center;
    gap: 12rpx;
    padding: 0 40rpx;

    .agreement-checkbox {
      flex-shrink: 0;
      font-size: 40rpx !important;
      line-height: 40rpx !important;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 40rpx;

      .agreement-link {
        color: #44bfbd;
      }
    }
  }

  .submit-btn {
    margin: 18rpx auto 0;
    width: 638rpx;
    height: 100rpx;
    background: linear-gradient(179deg, #40bdb7 9%, #49c2c4 30%);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 36rpx;
    color: #ffffff;
    line-height: 64rpx;
  }
}
</style>
