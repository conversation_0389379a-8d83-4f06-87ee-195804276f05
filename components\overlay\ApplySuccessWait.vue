<template>
  <uni-popup ref="popup" type="center" :is-mask-click="false" mask-background-color="#F6F6F8">
    <view class="overlay-apply-success-wait">
      <view class="success-content">
        <image
          class="success-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/4df51d56294f43e888eb1e0d4e3d607e.png"
        >
        </image>
        <view class="success-title">{{ title }}</view>
        <view class="success-desc">{{ desc }}</view>
        <view class="product-hint">
          <svg class="hint-icon rotating-fast" viewBox="0 0 100 100">
            <!-- 背景圆环 -->
            <circle cx="50" cy="50" r="40" stroke="#D8E8FF" stroke-width="8" fill="none" />

            <!-- 高亮圆环 -->
            <circle
              cx="50"
              cy="50"
              r="40"
              stroke="#3882E7"
              stroke-width="8"
              fill="none"
              stroke-linecap="round"
              stroke-dasharray="75.4 251.2"
              transform="rotate(-90 50 50)"
            ></circle>
          </svg>
          <text class="hint-text"
            >根据您的资质将为您匹配更多
            <text class="highlight">优质产品</text>
          </text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'ApplySuccessWait',

  data() {
    return {
      title: '申请成功',
      desc: '请注意接听电话，客户经理稍后将给您联系'
    }
  },

  methods: {
    open(title, desc) {
      this.title = title || '申请成功'
      this.desc = desc || '请注意接听电话，客户经理稍后将给您联系'
      this.$refs.popup.open()
    },
    close() {
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/styles/custom-animation.scss';

.overlay-apply-success-wait {
  .success-content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .success-icon {
    width: 439rpx;
    height: 272.64rpx;
  }

  .success-title {
    margin-top: 35rpx;
    margin-bottom: 10rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .success-desc {
    margin-bottom: 70rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    line-height: 35rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .product-hint {
    display: flex;
    align-items: center;
    gap: 10rpx;

    .hint-icon {
      width: 32rpx;
      height: 32rpx;
    }

    .hint-text {
      font-weight: 500;
      font-size: 28rpx;
      color: #3d3d3d;
      line-height: 41rpx;
      letter-spacing: 2rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;

      .highlight {
        color: #3882e7;
      }
    }
  }
}
</style>
