<script>
export default {
  name: 'declaration-szhh',

  methods: {
    bindOpenUrl(url) {
      window.location.href = url
    }
  }
}
</script>

<template>
  <view class="declaration-container">
    <view v-if="vuex_declaration && !vuex_templateSig" v-html="vuex_declaration"></view>
    <template v-else>
      <view>
        <!--  公安备案图标和链接，如果不需要可以移除 -->
        <!-- <view
          @click="
            bindOpenUrl(
              '在此处替换为深圳瀚华小额贷款有限公司的公安备案链接'
            )
          "
        >
          <image
            class="police-icon"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/h5/images/icon/police.png"
          ></image>
        </view> -->

        <view>粤ICP备2023134169号</view>
        <view>深圳瀚华小额贷款有限公司</view>
        <view>郑重声明：平台只提供贷款咨询和推荐服务，放贷由银行或金融机构进行，所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全，请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。</view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.declaration-container {
  padding: 30rpx 60rpx;
  font-size: 28rpx;
  color: #999;
  text-align: center;

  .police-icon {
    width: 30rpx;
    height: 30rpx;
  }
}
</style> 