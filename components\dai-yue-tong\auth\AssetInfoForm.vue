<template>
  <view class="form-section">
    <view class="form-header">
      <image
        class="form-header__icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/e8ade66da9804ed69f5e1c44660fc1e4.png"
        mode="scaleToFill"
      />
      <view class="form-header__title">资产信息</view>
    </view>

    <view class="form-item">
      <view class="form-item__label">公积金</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: form.isProvident === true }"
          @click="toggleAsset('isProvident', true)"
          >有公积金</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: form.isProvident === false }"
          @click="toggleAsset('isProvident', false)"
          >无公积金</view
        >
      </view>
    </view>

    <view class="form-item">
      <view class="form-item__label">房产</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: form.isHouse === true }"
          @click="toggleAsset('isHouse', true)"
          >有房产</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: form.isHouse === false }"
          @click="toggleAsset('isHouse', false)"
          >无房产</view
        >
      </view>
    </view>

    <view class="form-item">
      <view class="form-item__label">车辆</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: form.isVehicle === true }"
          @click="toggleAsset('isVehicle', true)"
          >有车</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: form.isVehicle === false }"
          @click="toggleAsset('isVehicle', false)"
          >无车</view
        >
      </view>
    </view>

    <view class="form-item">
      <view class="form-item__label">社保</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: form.isSocial === true }"
          @click="toggleAsset('isSocial', true)"
          >有社保</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: form.isSocial === false }"
          @click="toggleAsset('isSocial', false)"
          >无社保</view
        >
      </view>
    </view>

    <view class="form-item">
      <view class="form-item__label">保险单</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: form.isInsure === true }"
          @click="toggleAsset('isInsure', true)"
          >有保险单</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: form.isInsure === false }"
          @click="toggleAsset('isInsure', false)"
          >无保险单</view
        >
      </view>
    </view>

    <view class="form-item">
      <view class="form-item__label">营业执照</view>
      <view class="form-item__content">
        <view
          class="form-item__content-item"
          :class="{ active: form.isBusiness === true }"
          @click="toggleAsset('isBusiness', true)"
          >有营业执照</view
        >
        <view
          class="form-item__content-item"
          :class="{ active: form.isBusiness === false }"
          @click="toggleAsset('isBusiness', false)"
          >无营业执照</view
        >
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AssetInfoForm',
  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({
        isProvident: null,
        isHouse: null,
        isVehicle: null,
        isSocial: null,
        isInsure: null,
        isBusiness: null
      })
    }
  },
  computed: {
    form: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    }
  },
  methods: {
    toggleAsset(key, value = true) {
      this.form = {
        ...this.form,
        [key]: value
      }
    },
    getFormData() {
      return this.form
    },
    validateForm() {
      const fieldNameMap = {
        isProvident: '公积金',
        isHouse: '房产',
        isVehicle: '车辆',
        isSocial: '社保',
        isInsure: '保险单',
        isBusiness: '营业执照'
      }

      // 找到第一个未选择的字段
      const firstUnselectedField = Object.entries(this.form).find(([key, value]) => value === null)

      if (firstUnselectedField) {
        const fieldName = fieldNameMap[firstUnselectedField[0]]
        uni.showToast({
          title: `请选择${fieldName}`,
          icon: 'none'
        })
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped>
.form-section {
  padding: 44rpx 32rpx 0;
  background-color: #ffffff;
  margin-bottom: 90rpx;

  .form-header {
    margin-bottom: 40rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    &__icon {
      width: 46rpx;
      height: 46rpx;
    }

    &__title {
      font-weight: 500;
      font-size: 32rpx;
      color: #3d3d3d;
      line-height: 46rpx;
    }
  }
}

.form-item {
  margin-bottom: 40rpx;

  &__label {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 40rpx;

    &::before {
      content: '';
      width: 8rpx;
      height: 24rpx;
      background: linear-gradient(179deg, #40bdb7 9%, #49c2c4 30%);
    }
  }

  &__content {
    display: flex;
    gap: 20rpx;

    &-item {
      width: 216rpx;
      height: 64rpx;
      background: #f2f2f2;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #9e9e9e;
      line-height: 40rpx;
      border: 2rpx solid transparent;

      &.active {
        background: rgba(63, 193, 188, 0.17);
        border: 2rpx solid #3fc1bc;
        color: #49c2c4;
      }
    }
  }
}
</style>
