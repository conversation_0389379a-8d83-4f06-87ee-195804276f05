<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { formatAmount, parseAmount } from '@/utils/amount'
import { applyFormProduct } from '@/apis/common-2'

import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import routeMap from '../packages/routeMap.js'

// 导入UI组件
import MatchSuccessHeader from '@/components/hyh/apply/MatchSuccessHeader.vue'
import CountdownTimer from '@/components/hyh/apply/CountdownTimer.vue'
import LoanAmountCard from '@/components/hyh/apply/LoanAmountCard.vue'
import LoanDetailsCard from '@/components/hyh/apply/LoanDetailsCard.vue'
import InstitutionMatchCard from '@/components/hyh/apply/InstitutionMatchCard.vue'
import NoticeBar from '@/components/hyh/apply/NoticeBar.vue'
import BottomConfirm from '@/components/hyh/apply/BottomConfirm.vue'

import ProductPopup from '@/components/hyh/apply/ProductPopup.vue'

export default {
  name: 'ApplyIndex',

  components: {
    FullScreenMatchLoading,
    MatchSuccessHeader,
    CountdownTimer,
    LoanAmountCard,
    LoanDetailsCard,
    InstitutionMatchCard,
    NoticeBar,
    BottomConfirm,

    ProductPopup
  },

  data() {
    return {
      form: {
        monthIndex: 0,
        demandAmount: '50000'
      },
      monthRange: [
        { label: '3个月', value: 3 },
        { label: '6个月', value: 6 },
        { label: '12个月', value: 12 },
        { label: '36个月', value: 36 }
      ],
      activeForm: '',
      productList: [],
      monthlyPay: '',
      productPopupData: {}
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.updateAmountUI()
    this.getFlowData()
  },

  methods: {

    clickProductItem(product) {
      this.productPopupData = product
      this.$refs.productPopup.open()
    },

    // 组件事件处理方法
    handleAmountBlur(value) {
      const amount = parseInt(value)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = '50000'
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = '200000'
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = amount.toString()
      this.updateAmountUI()
    },

    handleMonthChange(index) {
      this.form.monthIndex = index
      this.updateAmountUI()
    },

    updateAmountUI() {
      // 计算月供
      const amount = parseAmount(this.form.demandAmount)
      const months = this.monthRange[this.form.monthIndex].value
      // 简单计算，实际应该根据利率计算
      this.monthlyPay = formatAmount(Math.round(amount / months))
    },

    async getFlowData() {
      const flowData = getFlowData('offline')
      if (flowData) {
        this.productList = flowData.productList || []
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyFormProduct({
        productId: product.id,
        consumerId: this.form.consumerId,
        demandAmount: parseAmount(this.form.demandAmount),
        monthIndex: this.form.monthIndex
      })

      uni.hideLoading()

      if (res.data) {
        // 设置 url 到 vuex
        this.$u.vuex('vuex_overloanWebview.url', res.data)
        // 跳转到 overloan-webview 页面
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `/extreme/v153/overloanWebview/index?param=${urlParamString}`
        })
        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      if (nextFlow.flow === 'end') {
        if (this.vuex_halfJumpUrl) {
          window.location.href = this.vuex_halfJumpUrl
          return
        }
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    clickSubmit() {
      throttle(this.submitHandler)
    },

    submitHandler() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      // 申请第一个产品
      this.applyProduct(this.productList[0])
    }
  }
}
</script>

<template>
  <view class="page-container">
    <MatchSuccessHeader />
    
    <CountdownTimer />
    
    <LoanAmountCard 
      :amount="form.demandAmount"
      @amount-blur="handleAmountBlur"
    />
    
    <LoanDetailsCard 
      :monthRange="monthRange"
      :monthIndex="form.monthIndex"
      :monthlyPay="monthlyPay"
      @month-change="handleMonthChange"
    />
    
    <InstitutionMatchCard 
      :productList="productList"
      @product-click="clickProductItem"
    />
    
    <NoticeBar />
    
    <BottomConfirm @submit="clickSubmit" />
    
    <ProductPopup 
      ref="productPopup"
      :productData="productPopupData"
      @apply="clickApply"
    />
    
    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}
</style>
