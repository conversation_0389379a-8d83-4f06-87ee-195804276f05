<template>
  <view class="footer-container">
    <!-- 协议 -->
    <view class="agreement" @click="$emit('update:is-agreed', !isAgreed)">
      <uni-icons
        :type="isAgreed ? 'checkbox-filled' : 'circle'"
        size="12"
        color="#44BFBD"
        class="agreement-checkbox"
      />
      <view class="agreement-text"
        >本人已知晓
        <text
          v-for="(agreement, index) in agreementList"
          :key="agreement.protocolId || agreement.id || index"
          class="agreement-highlight"
          @click.stop="handleOpenAgreement(index)"
        >
          《{{ agreement.name || agreement.protocolName }}》<text v-if="index < agreementList.length - 1">、</text>
        </text>
        的所有内容，同意并授权平台申请已选择的产品
      </view>
    </view>

    <view class="apply-button" :class="{ disabled: selectedCount === 0 }" @click="handleApply"
      >立即申请</view
    >
  </view>
</template>

<script>
export default {
  name: 'ApplyFooter',
  props: {
    selectedCount: {
      type: Number,
      default: 0
    },
    isAgreed: {
      type: Boolean,
      default: false
    },
    agreementList: {
      type: Array,
      default: () => []
    }
  },
  emits: ['apply', 'open-agreement', 'update:is-agreed'], // uni-app vue2 对 emits 支持有限，但作为规范写上
  methods: {
    handleOpenAgreement(index) {
      // Emit 事件给父组件处理导航
      this.$emit('open-agreement', index)
    },
    handleApply() {
      if (this.selectedCount === 0) {
        uni.showToast({
          title: '请先选择产品',
          icon: 'none'
        })
        return
      }
      if (!this.isAgreed) {
        uni.showToast({
          title: '请先同意协议',
          icon: 'none'
        })
        return
      }
      this.$emit('apply')
    }
  }
}
</script>

<style scoped lang="scss">
.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 250rpx;
  background-color: #ffffff;
  box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.3);
  z-index: 10;

  .agreement {
    padding: 42rpx 32rpx 20rpx;
    display: flex;
    align-items: center;
    gap: 8rpx;

    .agreement-checkbox {
      flex-shrink: 0;
      font-size: 40rpx !important;
      line-height: 40rpx !important;
    }

    .agreement-text {
      flex: 1;
      font-weight: 400;
      font-size: 24rpx;
      color: #666666;
      line-height: 40rpx;
    }

    .agreement-highlight {
      color: #41bdb8;
    }
  }

  .apply-button {
    margin: 0 auto 20rpx;
    width: 638rpx;
    height: 100rpx;
    background: linear-gradient(179deg, #40bdb7 9%, #49c2c4 30%);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #ffffff;
    line-height: 64rpx; // 保持和原来一致
    display: flex;
    align-items: center;
    justify-content: center;

    &.disabled {
      opacity: 0.4;
    }
  }
}
</style>
