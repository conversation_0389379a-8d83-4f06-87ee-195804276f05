<template>
  <view class="loan-options-container">
    <view class="loan-option-item">
      <view class="option-label">借多久</view>
      <view class="option-value-selector">
        <view class="selected-value">{{ duration }}个月</view>
        <!-- icon -->
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/94b3bec81ed440d2bb205474328c9be8.png"
          class="dropdown-icon"
        />
      </view>
    </view>

    <view class="duration-options">
      <view
        class="duration-option"
        :class="{ selected: duration === 3 }"
        @click="$emit('set-duration', 3)"
        >3个月</view
      >
      <view
        class="duration-option"
        :class="{ selected: duration === 6 }"
        @click="$emit('set-duration', 6)"
        >6个月</view
      >
      <view
        class="duration-option"
        :class="{ selected: duration === 9 }"
        @click="$emit('set-duration', 9)"
        >9个月</view
      >
      <view
        class="duration-option"
        :class="{ selected: duration === 12 }"
        @click="$emit('set-duration', 12)"
        >12个月</view
      >
    </view>

    <view class="loan-option-item">
      <view class="option-label">借款用途</view>
      <view class="option-value-selector">
        <view class="selected-value">个人生活消费</view>
        <!-- icon -->
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/94b3bec81ed440d2bb205474328c9be8.png"
          class="dropdown-icon"
        />
      </view>
    </view>

    <view class="loan-option-item">
      <view class="option-label">怎么还</view>
      <view class="option-value-selector">
        <view class="selected-value">
          灵活还，首期应还
          <!-- 高亮 -->
          <view class="amount-highlight">{{ monthlyPayment }}元</view>
        </view>
        <!-- icon -->
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/94b3bec81ed440d2bb205474328c9be8.png"
          class="dropdown-icon"
        />
      </view>
    </view>

    <view class="loan-option-item">
      <view class="option-label">享优惠</view>
      <view class="option-value-selector">
        <!-- tag样式 -->
        <view class="discount-tag">整单9.9折</view>
      </view>
    </view>

    <view class="loan-option-item">
      <view class="option-label">收款户</view>
      <view class="option-value-selector">
        <!-- 图标 -->
        <image
          class="bank-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/9954b3aea52042b48ac64f0d317674af.png"
        />
      </view>
    </view>

    <!-- 手机号输入框 -->
    <input
      type="text"
      placeholder="输入手机号获取借款额度"
      class="phone-input"
      maxlength="11"
      :value="phoneNumber"
      @input="onInputPhone"
    />

    <view class="submit-btn" @click="$emit('submit-apply')">
      提交申请
      <view class="submit-btn-float">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/29b3c58aeaa64c54852c136310c6f5e2.png"
          class="lightning-icon"
        />
        <text class="submit-btn-float-text">极速放款</text>
      </view>
    </view>

    <!-- 协议 -->
    <view class="agreement-container" @click="$emit('toggle-agreement')">
      <!-- 协议图标 -->
      <uni-icons
        :type="agreedTerms ? 'checkbox-filled' : 'circle'"
        size="12"
        class="agreement-checkbox"
        color="#44BFBD"
      ></uni-icons>

      <view class="agreement-text">
        注册代表同意
        <!-- 协议链接 -->
        <text
          v-for="(agreement, index) in agreementList"
          :key="agreement.protocolId"
          class="agreement-link"
          @click.stop="$emit('open-agreement', agreement)"
        >
          《{{ agreement.name }}》<text v-if="index < agreementList.length - 1">、</text>
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import { parseAmount } from '@/utils/amount.js'

export default {
  props: {
    duration: {
      type: Number,
      default: 12
    },
    phoneNumber: {
      type: String,
      default: ''
    },
    agreedTerms: {
      type: Boolean,
      default: false
    },
    agreementList: {
      type: Array,
      default: () => []
    },
    loanAmount: {
      type: String,
      default: '48,000'
    }
  },
  computed: {
    monthlyPayment() {
      // 计算月还款金额
      let price = Number(parseAmount(this.loanAmount))
      let mLatte = (price * 12) / 100 / 12 // 月利率为年化12%除以12
      const month = this.duration
      return ((price + mLatte * month) / month).toFixed(2)
    }
  },
  methods: {
    onInputPhone(e) {
      this.$emit('update:phone-number', e.target.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.loan-options-container {
  // 贷款选项容器样式
  width: 686rpx;
  height: 756rpx;
  background: linear-gradient(180deg, #d8faf8 0%, #ffffff 8%, #ffffff 100%);
  margin: 24rpx auto 0;
  border-radius: 20rpx;
  padding: 24rpx;

  .loan-option-item {
    // 贷款选项项目样式
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .option-label {
      // 选项标签样式
      font-weight: 400;
      font-size: 28rpx;
      color: #4d4d4d;
      line-height: 34rpx;
    }

    .option-value-selector {
      // 选项值选择器样式
      display: flex;
      align-items: center;
      gap: 2rpx;

      .selected-value {
        // 已选值样式
        display: flex;
        font-weight: 500;
        font-size: 28rpx;
        color: #1a1a1a;
        line-height: 34rpx;

        .amount-highlight {
          // 金额高亮样式
          color: #3fc1bd;
        }
      }

      .dropdown-icon {
        // 下拉图标样式
        width: 32rpx;
        height: 32rpx;
      }

      .discount-tag {
        // 折扣标签样式
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 500;
        font-size: 24rpx;
        color: #9e3d00;
        line-height: 28rpx;
        width: 116rpx;
        height: 36rpx;
        background: #ffd68e;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
      }

      .bank-icon {
        // 银行图标样式
        width: 138rpx;
        height: 34rpx;
      }
    }
  }

  .duration-options {
    // 期限选项样式
    margin-bottom: 24rpx;
    display: flex;
    gap: 10rpx;
    justify-content: space-between;

    .duration-option {
      // 期限选项项目样式
      width: 152rpx;
      height: 66rpx;
      background: #f2f2f2;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #9e9e9e;
      line-height: 40rpx;
      border: 2rpx solid #f2f2f2;

      // 选中状态
      &.selected {
        background: rgba(63, 193, 188, 0.17);
        border: 2rpx solid #3fc1bc;
        color: #3fc1bc;
      }
    }
  }

  .phone-input {
    // 手机输入框样式
    width: 638rpx;
    height: 100rpx;
    background: #ffffff;
    border-radius: 462rpx 462rpx 462rpx 462rpx;
    border: 2rpx solid #ff9b56;
    font-weight: 400;
    font-size: 32rpx;
    line-height: 64rpx;
    text-align: center;
    margin-top: 40rpx;
  }

  .submit-btn {
    // 提交按钮样式
    position: relative;
    margin-top: 32rpx;
    width: 638rpx;
    height: 100rpx;
    background: linear-gradient(100deg, #ffb173 22%, #ff702a 100%);
    border-radius: 462rpx 462rpx 462rpx 462rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 36rpx;
    color: #ffffff;
    line-height: 64rpx;

    .submit-btn-float {
      position: absolute;
      right: 54rpx;
      top: 0;
      transform: translateY(-50%);
      width: 146rpx;
      height: 42rpx;
      background: linear-gradient(180deg, #e0c389 0%, #cea86a 100%);
      border-radius: 632rpx 632rpx 632rpx 632rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4rpx;

      .lightning-icon {
        width: 24rpx;
        height: 24rpx;
      }

      .submit-btn-float-text {
        font-weight: 500;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 32rpx;
      }
    }
  }

  .agreement-container {
    // 协议容器样式
    display: flex;
    margin-top: 20rpx;
    display: flex;
    justify-content: center;
    gap: 12rpx;

    .agreement-checkbox {
      // 协议复选框样式
      flex-shrink: 0;
      font-size: 40rpx !important;
      line-height: 40rpx !important;
    }

    .agreement-text {
      // 协议文本样式
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 40rpx;

      .agreement-link {
        // 协议链接样式
        color: #44bfbd;
      }
    }
  }
}
</style>
