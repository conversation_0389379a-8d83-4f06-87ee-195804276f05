<template>
  <view class="page-container">
    <view class="title-container">
      <view class="title">只差一步，完成提现</view>

      <view class="warning">
        <view class="warning-text"> 请勿将平台资金用于虚拟货币、</view>
        <view class="warning-text"> 网络赌博等违法活动</view>
      </view>
    </view>

    <view class="phone-container">
      <view class="get-my-quota" @click="clickGetMyQuota">一键提现</view>
      <view class="agreement">
        <view class="agreement-text">
          本人已知晓
          <text
            class="name"
            v-for="item in agreementList"
            :key="item.protocolId"
            @click="clickAgreement(item)"
          >
            《{{ item.name }}》
          </text>
          的所有内容，同意并授权平台推荐/匹配多个产品
        </view>
      </view>
    </view>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="remindPopup"
      type="center"
      border-radius="56rpx 56rpx 56rpx 56rpx"
    >
      <view class="remind-popup">
        <view class="remind-popup-title">确定要放弃以下限时特权吗？</view>
        <view class="remind-popup-desc">额度仅限今日领取</view>
        <view class="remind-popup-feature">
          <view class="remind-popup-feature-item">
            <image
              class="remind-popup-feature-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/bfa6afd3bab9489b813569d8de22eac4.png"
            ></image>
            <view class="remind-popup-feature-text">
              已匹配到产品的
              <text class="highlight">申请机会</text>
            </view>
          </view>
          <view class="remind-popup-feature-item">
            <image
              class="remind-popup-feature-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/bfa6afd3bab9489b813569d8de22eac4.png"
            ></image>
            <view class="remind-popup-feature-text">
              初审已通过，确认额度
              <text class="highlight">立即领取</text>
            </view>
          </view>
        </view>
        <view class="remind-popup-tips"
          >信息加密储存仅用于资质申请，平台承诺不会泄露您的任何信息
        </view>
        <view class="remind-popup-confirm" @click="$refs.remindPopup.close()">继续提现</view>
        <view class="remind-popup-cancel" @click="$refs.remindPopup.close()">狠心离开</view>
      </view>
    </uni-popup>

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <view class="agreement-container">
          <view v-for="agreement in agreementList" :key="agreement.protocolId">
            <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
            <view class="agreement-content">
              <div v-html="agreement.content"></div>
            </view>
          </view>
        </view>
        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickAgreeAndContinue">同意并继续</view>
        </view>
      </view>
    </uni-popup>

    <WaitLoading ref="loading" />
  </view>
</template>
<script>
import LockNativeBack from '@/utils/lock-native-back'
import { reportUV } from '@/apis/common'
import { fetchTencentXJQwMatchV2 } from '@/apis/common-2'
import throttle from '@/utils/throttle'
import WaitLoading from '@/components/overlay/WaitLoading.vue'
import { getAgreements } from '@/utils/agreement'

export default {
  name: 'template-v50',

  components: {
    WaitLoading
  },

  data() {
    return {
      form: {
        channelId: ''
      },
      lockBack: null,
      agreementList: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
    }
    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
    this.fetchAgreement()
  },

  methods: {
    clickAgreeAndContinue() {
      this.$refs.agreementPopup.close()
      this.clickGetMyQuota()
    },

    clickAgreement() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    async fetchAgreement() {
      this.agreementList = (await getAgreements('xj_qrcode_page')) || []
    },

    clickGetMyQuota() {
      throttle(this.getMyQuotaHandler)
    },

    async getMyQuotaHandler() {
      const wechatProduct = await fetchTencentXJQwMatchV2({
        channelId: this.form.channelId
      })

      if (wechatProduct.code == 200 && wechatProduct.data) {
        window.location.href = wechatProduct.data
      } else {
        this.navigateToDownload()
      }
    },

    navigateToDownload() {
      uni.navigateTo({
        url: `/extreme/v50/download/index`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #ffffff;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/d016c5ae99db495284ba81601128f348.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}

.title-container {
  position: relative;
  padding: 215rpx 0rpx 77rpx;

  .title {
    font-weight: 500;
    font-size: 52rpx;
    color: #ff3154;
    line-height: 75rpx;
    text-align: center;
  }

  .warning {
    margin-top: 45rpx;
    display: flex;
    gap: 40rpx;
    flex-direction: column;
    align-items: center;
    font-weight: 500;
    font-size: 28rpx;
    color: #ff0000;
    line-height: 28rpx;

    .warning-text {
      background: linear-gradient(
        to bottom,
        transparent calc(100% - 12rpx),
        #f1ec63 calc(100% - 12rpx)
      );
    }
  }
}

.phone-container {
  position: relative;
  margin: 0 50rpx 44rpx;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .get-my-quota {
    margin: 60rpx 0;
    padding: 22rpx;
    text-align: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    background: #ff3154;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
  }

  .agreement {
    display: flex;
    gap: 5rpx;

    .agree-icon {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 32rpx;

      .name {
        color: #ff3154;
      }
    }
  }
}

.remind-popup {
  width: 650rpx;
  padding: 88rpx 66rpx 72rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/dba54d547fb243cb9097ea8a4245c359.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .remind-popup-title {
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-desc {
    margin-top: 14rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-feature {
    margin-top: 50rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;

    .remind-popup-feature-item {
      display: flex;
      align-items: center;
      gap: 5rpx;

      .remind-popup-feature-icon {
        width: 36rpx;
        height: 30rpx;
      }

      .remind-popup-feature-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #3d3d3d;
        line-height: 37rpx;
        letter-spacing: 2px;

        .highlight {
          color: #ff3154;
        }
      }
    }
  }

  .remind-popup-tips {
    margin-top: 43rpx;
    background: #fffaf3;
    font-weight: 400;
    font-size: 24rpx;
    color: #b68338;
    line-height: 32rpx;
    padding: 16rpx 22rpx;
  }

  .remind-popup-confirm {
    margin-top: 48rpx;
    background: #ff3154;
    border-radius: 38rpx 38rpx 38rpx 38rpx;
    font-weight: normal;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 45rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding: 15rpx;
  }

  .remind-popup-cancel {
    margin-top: 22rpx;
    font-weight: normal;
    font-size: 26rpx;
    color: #919094;
    line-height: 36rpx;
    text-align: center;
  }
}

.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/79cfd3028d884f4cbbebebed92b4a946.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: #ff3154;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style>
