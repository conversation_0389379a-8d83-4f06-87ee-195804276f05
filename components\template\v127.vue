<template>
  <view class="page-container">
    <notice-bar :notice-text="'您当前正在申请贷悦通助贷产品，我们优产品'" />

    <loan-amount-section
      :loan-amount="loanAmount"
      @input-focus="onInputFocus"
      @input-blur="onInputBlur"
      @set-amount="setAmount"
      @update:loan-amount="(value) => (loanAmount = value)"
    />

    <loan-options
      :duration="duration"
      :phone-number="phoneNumber"
      :agreed-terms="agreedTerms"
      @set-duration="setDuration"
      @update:phone-number="(value) => (phoneNumber = value)"
      @submit-apply="submitApply"
      @toggle-agreement="toggleAgreement"
      @open-agreement="openAgreement"
    />

    <disclaimer />

    <validate-code-popup
      ref="validateCodePopup"
      :phone-number="phoneNumber"
      :validate-code="validateCode"
      :count-down="countDown"
      @update:validate-code="(value) => (validateCode = value)"
      @resend-code="resendCode"
      @submit-code="submitValidateCode"
    />
  </view>
</template>

<script>
import { formatAmount, parseAmount } from '@/utils/amount.js'
import NoticeBar from '@/components/dai-yue-tong/index/NoticeBar.vue'
import LoanAmountSection from '@/components/dai-yue-tong/index/LoanAmountSection.vue'
import LoanOptions from '@/components/dai-yue-tong/index/LoanOptions.vue'
import ValidateCodePopup from '@/components/dai-yue-tong/index/ValidateCodePopup.vue'
import Disclaimer from '@/components/dai-yue-tong/index/Disclaimer.vue'

export default {
  components: {
    NoticeBar,
    LoanAmountSection,
    LoanOptions,
    ValidateCodePopup,
    Disclaimer
  },
  data() {
    return {
      loanAmount: '48,000', // 初始化为带逗号的格式
      rawAmount: '48000', // 存储无逗号的原始数值
      duration: 12, // 默认选择12个月
      phoneNumber: '', // 手机号
      validateCode: '', // 验证码
      countDown: 0, // 倒计时
      timer: null, // 计时器
      agreedTerms: false // 是否同意协议
    }
  },
  methods: {
    // 输入框聚焦时，显示无逗号的原始数值
    onInputFocus() {
      this.loanAmount = this.rawAmount
    },
    // 输入框失焦时，将数值格式化为带逗号的形式
    onInputBlur() {
      // 先确保数值有效
      if (this.loanAmount) {
        // 移除非数字字符，保存原始数值
        this.rawAmount = parseAmount(this.loanAmount)
        // 使用项目提供的格式化工具函数
        this.loanAmount = formatAmount(this.rawAmount)
      }
    },
    // 设置指定金额
    setAmount(amount) {
      this.rawAmount = amount.toString()
      this.loanAmount = formatAmount(this.rawAmount)
    },
    // 设置借款期限
    setDuration(months) {
      this.duration = months
    },
    // 提交申请
    submitApply() {
      if (!this.phoneNumber || !/^1\d{10}$/.test(this.phoneNumber)) {
        uni.showToast({
          title: '请输入有效的手机号',
          icon: 'none'
        })
        return
      }

      this.$u.vuex('vuex_phone', this.phoneNumber)

      if (!this.agreedTerms) {
        uni.showToast({
          title: '请阅读并同意注册协议',
          icon: 'none'
        })
        return
      }

      // 发送验证码
      this.sendSmsCode()
      this.$refs.validateCodePopup.open()
    },
    // 发送短信验证码
    sendSmsCode() {
      // 这里应该调用发送验证码的API
      // 模拟API调用
      setTimeout(() => {
        // 开始倒计时
        this.startCountDown()
        uni.showToast({
          title: '验证码已发送',
          icon: 'none'
        })
      }, 500)
    },
    // 重新获取验证码
    resendCode() {
      if (this.countDown > 0) return
      this.sendSmsCode()
    },
    // 开始倒计时
    startCountDown() {
      this.countDown = 60
      clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.countDown > 0) {
          this.countDown--
        } else {
          clearInterval(this.timer)
        }
      }, 1000)
    },
    // 提交验证码
    submitValidateCode() {
      if (!this.validateCode) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }

      // 这里应该调用验证验证码的API
      // 模拟API调用
      setTimeout(() => {
        uni.showToast({
          title: '申请提交成功',
          icon: 'success'
        })
        this.$refs.validateCodePopup.close()
        // 重置表单数据
        this.validateCode = ''
        // 跳转到结果页或其他页面
      }, 1000)
    },
    // 切换是否同意协议
    toggleAgreement() {
      this.agreedTerms = !this.agreedTerms
    },
    // 打开协议
    openAgreement() {
      // 跳转到协议页面
      uni.navigateTo({
        url: '/pages/common/agreement?type=register'
      })
    }
  },
  // 组件销毁时清除计时器
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  // 页面容器样式
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/276067e5eed343f49b3cfc46dbd70aa8.png);
  min-height: 100vh;
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  padding-top: 22rpx;
  padding-bottom: 88rpx;
}
</style>
