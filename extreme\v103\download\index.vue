<script>
import Header from '@/components/header/header-custom.vue'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'

export default {
  name: 'Download',
  components: { Header, Declaration }
}
</script>

<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <Header />

      <!-- 金币图标 -->
      <view class="coin-icon1"></view>
      <view class="coin-icon2"></view>
      <view class="coin-icon3"></view>

      <view class="feature-image"> </view>

      <view class="tips-container">
        <view class="tips-item">
          <view class="icon-tips"></view>
          <view class="tips-title">温馨提示</view>
        </view>
        <view class="tips-text"> 请保持手机畅通，我们的客服专员将尽快与您联系确认借款信息。 </view>
      </view>

      <Declaration />
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '@/theme/v103/export/download.scss';
</style>
