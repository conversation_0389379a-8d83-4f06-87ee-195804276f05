// 获取省市区数据
import { $get, $post } from '@/utils/request'
import queryParams from '@/utils/queryParams'
import md5 from 'js-md5'

export const fetchAreaData = (data = {}) => {
  return uni.request({
    url: 'https://hmc-prod.oss-cn-chengdu.aliyuncs.com/common/area/getArea.json',
    method: 'GET'
  })
}

// 联登 注册
export const register = (data = {}) => {
  return $get({
    url: '/ld/register',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配表单产品
export const fetchFormProduct = (data = {}) => {
  return $post({
    url: '/form/matching',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请表单产品
export const applyFormProduct = (data = {}) => {
  return $post({
    url: '/form/applyProducts',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配线上产品
export const fetchOnlineProduct = (data = {}) => {
  return $post({
    url: '/form/matching/online',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请线上产品
export const applyOnlineProduct = (data = {}) => {
  return $post({
    url: '/form/applyOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// ip解析当前地址
export const getIpArea = (data = {}) => {
  return $get({
    url: '/user/v2/getIpArea',
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取协议信息
export const getProtocolDetail = (data = {}) => {
  return $get({
    url: '/protocol/query/' + data.protocolId,
    data,
    isEncrypt: false,
    deEncrypt: false
  })
}

// 获取协议列表
export const getProtocolList = (data = {}) => {
  return $get({
    url: '/protocol/list',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

export const fetchFlow = (data = {}) => {
  return $get({
    url: '/temp/sequence',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配半 api 产品
export const fetchHalfApiProduct = (data = {}) => {
  return $post({
    url: '/form/matching/half',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 注册，发送短信验证码
// export const sendSmsCode = (data = {}) => {
//   return $post({
//     url: `/user/send${queryParams(data)}`,
//     isEncrypt: false,
//     deEncrypt: false,
//     data
//   })
// }

// 注册，发送短信验证码(加密版)
export const sendSmsCode = (data = {}) => {
  const phone = String(data.phone || '')
  const channelId = String(data.channelId || '')
  const timestamp = String(Date.now())
  const sign = md5(phone + channelId + timestamp)

  const finalData = {
    ...data,
    timestamp
  }

  return $post({
    url: `/user/sendV2`,
    isEncrypt: true,
    deEncrypt: false,
    data: finalData,
    header: {
      sign
    }
  })
}

// 注册，发送短信验证码(测试)
export const sendTestSmsCode = (data = {}) => {
  return $post({
    url: `/user/sendTest${queryParams(data)}`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 注册页，手机号注册
export const saveLoan = (data = {}) => {
  return $post({
    url: '/user/saveLoan',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 注册页，手机号注册（无验证码版本）
export const saveLoanNon = (data = {}) => {
  return $post({
    url: '/user/saveLoanNon',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 一键登录
export const oneClick = (data = {}) => {
  return $post({
    url: '/user/oneClick',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 保存用户资质
export const saveUserInfo = (data = {}) => {
  return $post({
    url: '/form/consumerAssetsUpdate',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 企微是否自动跳转
export const fetchWechatAutoJump = (data = {}) => {
  return $get({
    url: '/temp/qwAutoFlag',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取企微自动跳转倒计时时间
export const fetchWechatAutoTime = (data = {}) => {
  return $get({
    url: '/temp/qwAutoTime',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取星享借贷超产品
export const fetchXxjLoanProduct = (data = {}) => {
  return $post({
    url: '/form/matching/online/xxj',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 百度 发送手机号验证码
export const fetchBdCode = (data = {}) => {
  return $get({
    url: '/user/sendBdCode',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取用户 id 和流程节点
export const verifySmsId = (data = {}) => {
  return $post({
    url: '/sms/v2/verifySmsId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取用户 id 和流程节点
export const verifySmsIdV4 = (data = {}) => {
  return $post({
    url: '/sms/v4/verifySmsId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 更新用户信息
export const consumerAssetsUpdate = (data = {}) => {
  return $post({
    url: '/sms/v2/consumerAssetsUpdate',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 更新用户信息 v4
export const consumerAssetsUpdateV4 = (data = {}) => {
  return $post({
    url: '/sms/v4/consumerAssetsUpdate',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取协议和产品
export const fetchAgreementAndProduct = (data = {}) => {
  return $get({
    url: '/half/getMatchResult',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请产品
export const applyProduct = (data = {}) => {
  return $post({
    url: '/half/applyHalf',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取跳转链接
export const fetchJumpUrl = (data = {}) => {
  return $post({
    url: '/half/getJumpUrl',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取惠逸花聚钱管家贷超产品
export const fetchJqAndHyhProduct = (data = {}) => {
  return $post({
    url: '/half/matchOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 贷超申请接口
export const applyLoan = (data = {}) => {
  return $post({
    url: '/half/applyOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取跳转到外部的链接
export const fetchExternalLink = (data = {}) => {
  return $post({
    url: '/ld/generateLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 腾讯蹊径二维码 H5，根据手机号匹配企微
export const fetchTencentXJWechatProduct = (data = {}) => {
  return $get({
    url: `/txxj/match`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 腾讯蹊径二维码 H5，根据手机号匹配企微
export const fetchTencentXJWechatProduct2 = (data = {}) => {
  return $post({
    url: `/txxj/v1/push`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 蹊径，匹配企微  企微uv匹配
export const fetchTencentXJQwMatch = (data = {}) => {
  return $get({
    url: `/txxj/qw/match`,
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 蹊径，申请线上产品
export const applyTencentXJOnlineProduct = (data = {}) => {
  return $post({
    url: '/form/xj/applyOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 上报完整的网页地址
export const reportFullUrl = (data = {}) => {
  return $get({
    url: '/user/tempLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配出量产品
export const fetchOutputProduct = (data = {}) => {
  return $post({
    url: '/form/match/output',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 申请出量产品
export const applyOutputProduct = (data = {}) => {
  return $post({
    url: '/form/applyOutput',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配虚拟超贷产品
export const fetchVirtualOverloanProduct = (data = {}) => {
  return $get({
    url: '/form/match/virtual/overLoan',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 蹊径，企微接入匹配V2 企微接入匹配
export const fetchTencentXJQwMatchV2 = (data = {}) => {
  return $get({
    url: '/txxj/qw/match/v2',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 半注册
export const halfRegister = (data = {}) => {
  return $get({
    url: '/ld/halfRegister',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 表单匹配
export const formMatching = (data = {}) => {
  return $post({
    url: '/fql/form/matching',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 表单申请
export const formApplyProducts = (data = {}) => {
  return $post({
    url: '/fql/form/applyProducts',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取用户 id 和流程节点 v6
export const verifySmsIdV6 = (data = {}) => {
  return $post({
    url: '/sms/v6/verifySmsId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 更新用户资产信息 v6
export const consumerAssetsUpdateV6 = (data = {}) => {
  return $post({
    url: '/sms/v6/consumerAssetsUpdate',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取用户 id 和流程节点 v7
export const verifySmsIdV7 = (data = {}) => {
  return $post({
    url: '/sms/v7/verifySmsId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取用户 id 和流程节点 v7-2
export const verifySmsIdV7V2 = (data = {}) => {
  return $post({
    url: '/sms/v7/verifySmsIdV2',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取用户 id 和流程节点 v8
export const verifySmsIdV8 = (data = {}) => {
  return $post({
    url: '/sms/v8/verifySmsId',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 更新用户资产信息 v8
export const consumerAssetsUpdateV8 = (data = {}) => {
  return $post({
    url: '/sms/v8/consumerAssetsUpdate',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取跳转链接
export const getJumpLink = (data = {}) => {
  return $post({
    url: '/dyt/getJumpLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取线上产品
export const getOnlineLowProduct = (data = {}) => {
  return $post({
    url: '/form/matching/onlineLow',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取线上产品申请
export const backApplyOnline = (data = {}) => {
  return $get({
    url: '/form/backApplyOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配表单线上产品
export const fetchFormOnlineProduct = (data = {}) => {
  return $post({
    url: '/form/matching/formOnline',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

export const fetchFormMatchApply = (data = {}) => {
  return $post({
    url: '/form/match/apply',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取跳转链接
export const fetchDcJumpUrl = (data = {}) => {
  return $post({
    url: '/output/dc/getJumpLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取短链接
export const generateShortLink = (data = {}) => {
  return $get({
    url: '/ld/generateShortLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 复制短链接埋点上报
export const copyShortLink = (data = {}) => {
  return $get({
    url: '/ld/copyShortLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 检查产品序列是否结束
export const checkProductSequenceEnd = (data = {}) => {
  return $post({
    url: '/form/productSequenceEnd',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 获取注册链接
export const fetchRegisterLink = (data = {}) => {
  return $get({
    url: '/outbound/registerLink',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

// 匹配小贷产品
export const fetchSmallLoanProduct = (data = {}) => {
  return $post({
    url: '/form/matching/smallLoan',
    isEncrypt: false,
    deEncrypt: false,
    data
  })
}

