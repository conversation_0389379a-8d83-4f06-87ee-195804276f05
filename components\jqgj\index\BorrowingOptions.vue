<template>
  <view class="borrowing-options-wrapper">
    <view class="option term">
      <view class="label">最长借多久</view>
      <picker
        range-key="label"
        :range="monthRange"
        :value="currentMonthIndex"
        @change="monthPickerChange"
      >
        <view class="value">
          <view>{{ monthRange[currentMonthIndex].value }}个月</view>
          <view class="recommend" v-if="monthRange[currentMonthIndex].value === 12">推荐</view>
          <uni-icons
            style="margin-left: 15rpx"
            color="#A2A3A5"
            type="right"
            size="16"
          ></uni-icons>
        </view>
      </picker>
    </view>
    <view class="option-line"></view>
    <view class="option repayment-method">
      <view class="label">如何还</view>
      <view class="value">
        <view class="repayment-amount">
          每月约应还
          <text class="amount-number">￥{{ monthlyPay }}</text>
        </view>
        <view class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</view>
      </view>
    </view>
    <view class="option-line"></view>
    <view class="option coupon">
      <view class="label">优惠券</view>
      <view class="value">
        <view class="coupon-container">
          <view class="coupon-text">
            新用户借款享
            <text class="days">30天</text>
            免息
          </view>
          <view class="coupon-tips"> 若提前还清或逾期，本券将失效 </view>
        </view>
        <uni-icons color="#A2A3A5" type="right" size="16"></uni-icons>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'BorrowingOptions',
  props: {
    demandAmount: {
      type: Number,
      default: 50000
    },
    initialMonthIndex: {
      type: Number,
      default: 2 // Default to 12 months
    }
  },
  data() {
    return {
      currentMonthIndex: this.initialMonthIndex,
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ]
    };
  },
  computed: {
    monthlyPay() {
      let price = Number(this.demandAmount);
      if (isNaN(price) || price <= 0) {
        return '0.00';
      }
      if (this.currentMonthIndex < 0 || this.currentMonthIndex >= this.monthRange.length) {
        return '0.00';
      }
      const monthData = this.monthRange[this.currentMonthIndex];
      if (!monthData || !monthData.value) {
          return '0.00';
      }
      let mLatte = (price * 12) / 100 / 12; // Assuming 12% annual interest rate
      const month = monthData.value;
      return ((price + mLatte * month) / month).toFixed(2);
    }
  },
  watch: {
    initialMonthIndex(newVal) {
      this.currentMonthIndex = newVal;
    },
    demandAmount() {
      // Recalculate when demandAmount changes, handled by computed property
    }
  },
  methods: {
    monthPickerChange({ detail }) {
      this.currentMonthIndex = detail.value;
      this.$emit('month-changed', this.currentMonthIndex);
    }
  },
  mounted() {
    // Initial calculation if needed, though computed property handles it
  }
};
</script>

<style lang="scss" scoped>
.borrowing-options-wrapper {
  padding: 15rpx 0;
  margin: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx;

  .option-line {
    margin-left: 60rpx;
    height: 2rpx;
    background-color: #f6f6f6;
  }

  .option {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    &.term .value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
      display: flex;
      align-items: center;

      .recommend {
        margin-left: 3rpx;
        padding: 7rpx 10rpx;
        background: #ff3636;
        border-radius: 203rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #eef9f3;
        line-height: 18rpx;
      }
    }

    &.repayment-method .value {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      .amount-number {
        color: #333333;
      }

      .repayment-reminder {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 29rpx;
      }
    }

    &.coupon .value {
      gap: 15rpx;
      display: flex;
      align-items: center;

      .coupon-container {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .days {
          color: #edbf7c;
        }

        .coupon-tips {
          padding: 8rpx 11rpx;
          background: #fff0f5;
          border-radius: 8rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ff5e7c;
          line-height: 21rpx;
        }
      }
    }
  }
}
</style> 