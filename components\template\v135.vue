<template>
  <view class="page-container">
    <view class="auth-wrapper">
      <view class="auth-content">
        <!-- 基本信息 -->
        <view class="form-section">
          <view class="form-header">
            <image
              class="form-header__icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/0c23a68a426348e6927b7bc6565e869d.png"
              mode="scaleToFill"
            />
            <view class="form-header__title">基本信息</view>
          </view>

          <view class="personal-form-item">
            <view class="personal-form-item__label">手机号</view>
            <view class="personal-form-item__content">
              <input
                class="personal-form-item__input"
                type="tel"
                placeholder="请输入手机号"
                maxlength="11"
                placeholder-class="placeholder"
                v-model="form.phone"
                @blur="phoneBlur"
              />
            </view>
          </view>

          <view v-if="isVisible('age')" class="personal-form-item">
            <view class="personal-form-item__label">年龄</view>
            <view class="personal-form-item__content">
              <input
                class="personal-form-item__input"
                placeholder="请填写年龄"
                maxlength="3"
                placeholder-class="placeholder"
                v-model="form.age"
                @blur="ageBlur"
              />
            </view>
          </view>
        </view>

        <!-- 信用情况 -->
        <view class="form-section" v-if="isVisible('sesameId') || isVisible('isOverdue')">
          <view class="form-header">
            <image
              class="form-header__icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/c248778624ff427ba37fa110c7fbcf79.png"
              mode="scaleToFill"
            />
            <view class="form-header__title">信用情况</view>
          </view>

          <view v-if="isVisible('sesameId')" class="credit-form-item">
            <view class="credit-form-item__label">芝麻分</view>
            <view class="credit-form-item__content">
              <view
                class="credit-form-item__content-item"
                :class="{ active: form.sesameScoreIndex === 0 }"
                @click="clickSesameScore(0)"
                >{{ sesameScoreOptions[0].label }}</view
              >
              <view
                class="credit-form-item__content-item"
                :class="{ active: form.sesameScoreIndex === 1 }"
                @click="clickSesameScore(1)"
                >{{ sesameScoreOptions[1].label }}</view
              >
            </view>
          </view>

          <view v-if="isVisible('isOverdue')" class="credit-form-item">
            <view class="credit-form-item__label">信用情况</view>
            <view class="credit-form-item__content">
              <view
                class="credit-form-item__content-item"
                :class="{ active: form.creditIndex === 0 }"
                @click="clickCredit(0)"
                >{{ creditOptions[0].label }}</view
              >
              <view
                class="credit-form-item__content-item"
                :class="{ active: form.creditIndex === 1 }"
                @click="clickCredit(1)"
                >{{ creditOptions[1].label }}</view
              >
            </view>
          </view>
        </view>

        <view class="privacy-notice">
          *请如实填写以上信息，以便为您推荐适合的贷款产品及贷款咨询服务
          我们将严格保护您的个人隐私安全，请放心填写
        </view>
      </view>
    </view>

    <!-- 提交区域 -->
    <view class="footer-actions">
      <view class="agreement-container">
        <view class="agreement-text">
          我已阅读并同意
          <text
            class="agreement-link"
            v-for="(item, index) in agreementList"
            :key="item.protocolId"
            @click="clickAgreement(item, index)"
          >
            《{{ item.name }}》
          </text>
        </view>
      </view>
      <view class="submit-btn" @click="clickSubmit">提交申请</view>
    </view>

    <!-- 协议弹窗 -->
    <AgreementPopup
      ref="agreementPopup"
      :agreementList="agreementList"
      :selectedIndex="agreementIndex"
      @agree="clickAgreeAndContinue"
    />
    <Loading ref="loading" />
  </view>
</template>

<script>
import { validateMobile } from '@/utils/validators'
import LockNativeBack from '@/utils/lock-native-back'
import {
  fetchAptitudeRestock,
  fetchChannelAndTemplate,
  matchingOnlineProduct,
  reportUV,
  saveAptitude
} from '@/apis/common'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import Loading from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import AgreementPopup from '@/components/dai-yue-tong/index/AgreementPopup.vue'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v135',

  components: {
    Loading,
    AgreementPopup
  },

  data() {
    return {
      form: {
        templateVersion: 'v135',
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        age: '',
        channelId: '',
        consumerId: '',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      // 默认展示资质信息
      activeForm: 'qualification',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 113,
          label: '650以上'
        },
        {
          value: 110,
          label: '650以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      agreementList: [],
      agreementIndex: 0,
      scrollIntoView: '',
      lockBack: null,
      visibleFields: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  async mounted() {
    setFlowNodes(['wechat', 'overloan', 'wechat_official_account'])
    // 从路由参数获取数据
    const query = this.$route.query

    this.$u.vuex('vuex_invite', query.i)

    if (query.param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(query.param)))
    }

    if (this.vuex_invite) {
      await this.fetchChannelAndTemplate()
      this.fetchVisibleFields()
    }

    this.fetchAgreement()

    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
    isVisible(fieldName) {
      if (!this.visibleFields || this.visibleFields.length === 0) {
        return false
      }
      return this.visibleFields.includes(fieldName)
    },

    async fetchVisibleFields() {
      if (!this.form.channelId) {
        this.visibleFields = []
        return
      }
      try {
        const res = await fetchAptitudeRestock({ channelId: this.form.channelId })
        if (res.code == 200 && Array.isArray(res.data)) {
          this.visibleFields = res.data
        } else {
          this.visibleFields = []
        }
      } catch (error) {
        this.visibleFields = []
      }
    },

    async fetchAgreement() {
      // 注册页协议
      this.agreementList = await getAgreements('dyt-hls-ag2')
    },

    async fetchChannelAndTemplate() {
      const res = await fetchChannelAndTemplate({
        invite: this.vuex_invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        reportUV({
          channelId: this.form.channelId
        })
      }
      return res
    },

    validateAge() {
      if (!this.isVisible('age')) return true

      this.form.age = this.form.age ? this.form.age.trim() : ''

      if (!this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(this.form.age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      if (this.form.age < 22 || this.form.age > 55) {
        uni.showToast({
          title: '仅支持 22-55 岁的用户',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateSesameScore() {
      if (!this.isVisible('sesameId')) return true

      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateCredit() {
      if (!this.isVisible('isOverdue')) return true

      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateQualificationForm() {
      let isValid = true
      if (this.isVisible('age')) {
        isValid = isValid && this.validateAge()
      }
      if (this.isVisible('sesameId')) {
        isValid = isValid && this.validateSesameScore()
      }
      if (this.isVisible('isOverdue')) {
        isValid = isValid && this.validateCredit()
      }
      return isValid
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (this.isVisible('age') && !this.validateAge()) {
        return
      }

      if (this.isVisible('sesameId') && !this.validateSesameScore()) {
        return
      }

      if (this.isVisible('isOverdue') && !this.validateCredit()) {
        return
      }

      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    clickAgreeAndContinue() {
      throttle(async () => {
        if (this.isVisible('age') && !this.validateAge()) {
          return
        }
        if (this.isVisible('sesameId') && !this.validateSesameScore()) {
          return
        }
        if (this.isVisible('isOverdue') && !this.validateCredit()) {
          return
        }

        const saveData = {
          phone: this.form.phone,
          channelId: this.form.channelId
        }
        if (this.isVisible('age')) {
          saveData.age = this.form.age
        }
        if (this.isVisible('sesameId')) {
          saveData.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
        }
        if (this.isVisible('isOverdue')) {
          saveData.isOverdue = this.form.isOverdue
        }

        this.$u.vuex('vuex_phone', this.form.phone)

        const res = await saveAptitude(saveData)

        if (res.code == 200) {
          this.form.consumerId = res.data
          this.$u.vuex('vuex_consumerId', res.data)
        }

        this.navigateToNextFlow()
      })
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v135/qw/index',
        overloan: '/extreme/v135/applyOther/index',
        end: '/extreme/v135/download/index',
        wechat_official_account: '/extreme/v135/wechatOfficialAccount/index'
      }

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    getUserParams() {
      const params = {}
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.monthIndex = this.form.monthIndex

      return params
    },

    clickAgreement(item, index) {
      this.agreementIndex = index
      this.$refs.agreementPopup.open(index)
    },

    toggleAgreement(index) {
      this.agreementIndex = index
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    ageBlur() {
      this.validateAge()
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index
      this.activeFormItem = 'credit'
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #3fbeb8;

.page-container {
  padding-top: 222rpx;
  height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/1a661e8ffc944f2f97b7f775f0491628.png);
  background-size: 100% 1442rpx;
  background-repeat: no-repeat;
  overflow: hidden;

  .auth-wrapper {
    width: 750rpx;
    background: #ffffff;
    border-radius: 60rpx 60rpx 0 0;

    .auth-content {
      height: calc(100vh - 222rpx);
      overflow-y: auto;
      padding-bottom: 200rpx;
    }
  }
}

.form-section {
  padding: 44rpx 32rpx 0;
  background-color: #ffffff;
  margin-bottom: 40rpx;

  .form-header {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    &__icon {
      width: 46rpx;
      height: 46rpx;
    }

    &__title {
      font-weight: 500;
      font-size: 32rpx;
      color: #3d3d3d;
      line-height: 46rpx;
    }
  }
}

// 个人信息表单项样式
.personal-form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f2f2f2;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    flex-shrink: 0;
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 40rpx;
  }

  &__content {
    display: flex;
    align-items: center;
  }

  &__input {
    font-weight: 400;
    font-size: 28rpx;
    line-height: 40rpx;
    text-align: right;
  }
}

// 信用情况表单项样式
.credit-form-item {
  margin-bottom: 40rpx;

  &__label {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 16rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 40rpx;

    &::before {
      content: '';
      width: 8rpx;
      height: 24rpx;
      background: linear-gradient(179deg, #40bdb7 9%, #49c2c4 30%);
    }
  }

  &__content {
    display: flex;
    gap: 20rpx;
  }

  &__content-item {
    width: 216rpx;
    height: 64rpx;
    background: #f2f2f2;
    border-radius: 4rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #9e9e9e;
    line-height: 40rpx;
    border: 2rpx solid transparent;

    &.active {
      background: rgba(63, 193, 188, 0.17);
      border: 2rpx solid #3fc1bc;
      color: #49c2c4;
    }
  }
}

.placeholder {
  color: #999999;
}

.privacy-notice {
  padding: 30rpx 70rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 32rpx;
  text-align: center;
}

.footer-actions {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
  background: #ffffff;
  box-shadow: 0rpx -2rpx 10rpx rgba(0, 0, 0, 0.05);

  .agreement-container {
    margin-bottom: 20rpx;

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 34rpx;

      .agreement-link {
        color: $color-primary;
      }
    }
  }

  .submit-btn {
    padding: 21rpx;
    background: $color-primary;
    border-radius: 30rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-align: center;
  }
}
</style>
