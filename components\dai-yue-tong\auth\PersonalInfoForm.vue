<template>
  <view class="personal-info-form">
    <view class="form-section">
      <view class="form-header">
        <image
          class="form-header__icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/0c23a68a426348e6927b7bc6565e869d.png"
          mode="scaleToFill"
        />
        <view class="form-header__title">个人信息</view>
      </view>
      <view class="form-item">
        <view class="form-item__label">姓名</view>
        <view class="form-item__content">
          <input
            class="form-item__input"
            maxlength="10"
            type="text"
            placeholder="请输入您的姓名"
            v-model="formData.name"
            @blur="onNameBlur"
          />
        </view>
      </view>
      <view class="form-item">
        <view class="form-item__label">年龄</view>
        <view class="form-item__content">
          <input
            class="form-item__input"
            type="text"
            maxlength="2"
            placeholder="请输入您的年龄"
            v-model="formData.age"
            @blur="onAgeBlur"
          />
        </view>
      </view>
      <view class="form-item">
        <view class="form-item__label">性别</view>
        <view class="form-item__content">
          <picker
            :range="genderOptions"
            range-key="label"
            :value="genderIndex"
            @change="onGenderChange"
          >
            <view class="picker-value">
              <text v-if="formData.sex !== null" class="form-item__input">{{ genderLabel }}</text>
              <text v-else class="form-item__input placeholder">请选择</text>
              <image
                class="form-item__arrow"
                src="https://cdn.oss-unos.hmctec.cn/common/path/94b3bec81ed440d2bb205474328c9be8.png"
                mode="scaleToFill"
              />
            </view>
          </picker>
        </view>
      </view>
      <view class="form-item">
        <view class="form-item__label">常住地址</view>
        <view class="form-item__content">
          <picker
            mode="multiSelector"
            :range="areaData"
            range-key="name"
            :value="addressIndex"
            @columnchange="onAddressColumnChange"
            @change="onAddressChange"
          >
            <view class="picker-value">
              <text v-if="formData.cityName" class="form-item__input">{{ formData.cityName }}</text>
              <text v-else class="form-item__input placeholder">请选择</text>
              <image
                class="form-item__arrow"
                src="https://cdn.oss-unos.hmctec.cn/common/path/94b3bec81ed440d2bb205474328c9be8.png"
                mode="scaleToFill"
              />
            </view>
          </picker>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { fetchAreaData, getIpArea } from '@/apis/common-2'

export default {
  name: 'PersonalInfoForm',
  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({
        name: '',
        age: '',
        sex: null,
        cityName: '',
        cityCode: ''
      })
    }
  },
  data() {
    return {
      genderOptions: [
        { label: '男', value: 0 },
        { label: '女', value: 1 }
      ],
      areaData: [[], []],
      addressIndex: [0, 0]
    }
  },
  computed: {
    formData: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', newValue)
      }
    },
    genderLabel() {
      const selected = this.genderOptions.find((item) => item.value === this.formData.sex)
      return selected ? selected.label : ''
    },
    genderIndex() {
      return this.genderOptions.findIndex((item) => item.value === this.formData.sex)
    }
  },
  async created() {
    await this.initAreaData()
    if (!this.formData.cityName) {
      await this.getLocationByIP()
    }
  },
  methods: {
    // 姓名失焦验证
    onNameBlur() {
      this.validateName()
    },

    // 年龄失焦验证
    onAgeBlur() {
      this.validateAge()
    },

    // 获取表单数据
    getFormData() {
      return this.formData
    },

    // 性别选择相关方法
    onGenderChange(e) {
      const index = e.detail.value
      this.formData = {
        ...this.formData,
        sex: this.genderOptions[index].value
      }
    },

    // 地址选择相关方法
    async initAreaData() {
      try {
        const res = await fetchAreaData()
        const area = res[1].data
        this.areaData = [area, area[0].citys]
      } catch (error) {
        console.error('获取地址数据失败:', error)
      }
    },
    async getLocationByIP() {
      try {
        const res = await getIpArea()
        if (res.data) {
          this.formData = {
            ...this.formData,
            cityName: res.data.cityName,
            cityCode: res.data.cityCode
          }
        }
      } catch (error) {
        console.error('获取IP地址失败:', error)
      }
    },
    onAddressColumnChange(e) {
      const { column, value } = e.detail
      if (column === 0) {
        const citys = this.areaData[0][value].citys
        this.areaData[1] = citys
      }
    },
    onAddressChange(e) {
      const { value } = e.detail
      const [provinceIndex, cityIndex] = value
      this.addressIndex = value
      this.formData = {
        ...this.formData,
        cityName: this.areaData[1][cityIndex].name,
        cityCode: this.areaData[1][cityIndex].code
      }
    },
    validateAge() {
      const age = this.formData.age.trim()

      if (!age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      if (age < 22 || age > 55) {
        uni.showToast({
          title: '仅支持 22-55 岁的用户',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateName() {
      const name = this.formData.name.trim()

      if (!name) {
        uni.showToast({
          title: '请填写姓名',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.chinese(name)) {
        uni.showToast({
          title: '姓名格式错误',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateSex() {
      if (this.formData.sex === null) {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateCity() {
      if (!this.formData.cityCode) {
        uni.showToast({
          title: '请选择城市',
          icon: 'none'
        })
        return false
      }
      return true
    },

    validateForm() {
      return this.validateName() && this.validateAge() && this.validateSex() && this.validateCity()
    }
  }
}
</script>

<style lang="scss" scoped>
.personal-info-form {
  width: 100%;
}

.form-section {
  padding: 44rpx 32rpx 0;
  background-color: #ffffff;
  margin-bottom: 90rpx;

  .form-header {
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    &__icon {
      width: 46rpx;
      height: 46rpx;
    }

    &__title {
      font-weight: 500;
      font-size: 32rpx;
      color: #3d3d3d;
      line-height: 46rpx;
    }
  }
}

.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 0;
  border-bottom: 2rpx solid #f2f2f2;
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }

  &__label {
    flex-shrink: 0;
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 40rpx;
  }

  &__content {
    display: flex;
    align-items: center;
  }

  &__input {
    font-weight: 400;
    font-size: 28rpx;
    line-height: 40rpx;
    text-align: right;
  }

  &__arrow {
    width: 32rpx;
    height: 32rpx;
    margin-left: 8rpx;
  }
}

.picker-value {
  display: flex;
  align-items: center;
}

.placeholder {
  color: #999999;
}
</style>
