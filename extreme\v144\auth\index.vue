<script>
import {
  fetchAreaData,
  fetchFlow,
  fetchFormMatchApply,
  getIpArea,
  saveUserInfo
} from '@/apis/common-2'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode, setFlowIndex, setFlowNodes } from '@/utils/processFlowFunctions'
import { getAgreements } from '@/utils/agreement'
import throttle from '@/utils/throttle'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { isInWechatMiniProgram } from '@/utils/user-agent'
import { saveAppletOpenRecord } from '@/apis/common-3'

export default {
  name: 'AuthIndex',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {
        templateVersion: 'v144',
        cityName: '',
        cityCode: '',
        name: '',
        age: '',
        sex: '',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false,
        isProvident: false,
        isHouse: false,
        isVehicle: false,
        isSocial: false,
        isInsure: false,
        isBusiness: false,
        clearAll: false,
        consumerId: '',
        channelId: ''
      },

      activeForm: 'identity',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 115,
          label: '700分以上'
        },
        {
          value: 113,
          label: '650-700分'
        },
        {
          value: 112,
          label: '600-650分'
        },
        {
          value: 110,
          label: '600分以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      otherOptions: [
        {
          key: 'isProvident',
          value: true,
          label: '有公积金',
          highQuality: true
        },
        {
          key: 'isHouse',
          value: true,
          label: '有房'
        },
        {
          key: 'isVehicle',
          value: true,
          label: '有车'
        },
        {
          key: 'isSocial',
          value: true,
          label: '有社保'
        },
        {
          key: 'isInsure',
          value: true,
          label: '有保险单'
        },
        {
          key: 'isBusiness',
          value: true,
          label: '有营业执照'
        },
        {
          key: 'clearAll',
          value: true,
          label: '以上都没有'
        }
      ],
      isAgree: false,
      agreementList: [],
      agreementIndex: 0,
      agreeCountdownTimer: null,
      agreeCountdownNumber: 0,
      areaData: [],
      nextFlow: null,
      navigateTimer: null,
      scrollIntoView: '',
      currentRequestId: 0
    }
  },

  onUnload() {
    if (this.navigateTimer) {
      clearInterval(this.navigateTimer)
    }

    if (this.agreeCountdownTimer) {
      clearInterval(this.agreeCountdownTimer)
    }
  },

  async onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (this.form.consumerId) {
      this.fetchAreaData()
      this.fetchAddressByIP()
      this.fetchProtocol()
      this.fetchFlow()

      // 在微信小程序中保存记录
      if (isInWechatMiniProgram()) {
        await saveAppletOpenRecord({
          channelId: this.form.channelId,
          appUserId: this.form.consumerId
        })
      }
    } else {
      this.navigateToEnd()
    }
  },

  methods: {
    async fetchFlow() {
      const res = await fetchFlow(this.form)
      let nodes = res.data || []
      const defaultNodes = ['wechat', 'overloan', 'halfapi', 'wechat_official_account']
      if (nodes.length) {
        // 过滤掉defaultNodes之外的节点
        nodes = nodes.filter((node) => defaultNodes.includes(node))
      } else {
        nodes = defaultNodes
      }
      setFlowNodes(nodes)
    },

    navigateToEnd() {
      const path = '/extreme/v144/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    validateName() {
      this.form.name = this.form.name ? this.form.name.trim() : ''

      if (!this.form.name) {
        uni.showToast({
          title: '请填写姓名',
          icon: 'none'
        })

        return false
      }

      if (!this.$u.test.chinese(this.form.name)) {
        uni.showToast({
          title: '姓名格式错误',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateAge() {
      this.form.age = this.form.age ? this.form.age.trim() : ''

      if (!this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      if (!this.$u.test.digits(this.form.age)) {
        uni.showToast({
          title: '年龄必须为数字',
          icon: 'none'
        })
        return false
      }

      if (this.form.age < 22 || this.form.age > 55) {
        uni.showToast({
          title: '仅支持 22-55 岁的用户',
          icon: 'none'
        })
        return false
      }

      return true
    },

    regionColChange({ detail }) {
      const { column, value: provinceIndex } = detail
      if (column === 0) {
        const citys = this.areaData[0][provinceIndex].citys
        this.areaData[1] = citys
      }
    },

    regionChange({ detail }) {
      const { value } = detail
      const [provinceIndex, cityIndex] = value
      this.form.cityName = this.areaData[1][cityIndex].name
      this.form.cityCode = this.areaData[1][cityIndex].code
    },

    async fetchAreaData() {
      const res = await fetchAreaData()
      const area = res[1].data
      this.areaData = [area, area[0].citys]
    },

    async fetchAddressByIP() {
      const res = await getIpArea()
      if (res.data) {
        this.form.cityName = res.data.cityName
        this.form.cityCode = res.data.cityCode
      }
    },

    validateCity() {
      if (!this.form.cityCode) {
        uni.showToast({
          title: '请选择城市',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validateSex() {
      if (!this.form.sex && typeof this.form.sex !== 'number') {
        uni.showToast({
          title: '请选择性别',
          icon: 'none'
        })
        return false
      }
      return true
    },

    nameBlur() {
      this.validateName()
    },

    ageBlur() {
      this.validateAge()
    },

    clickSex(value) {
      this.form.sex = value

      const namePass = this.form.name && this.validateName()
      const agePass = this.form.age && this.validateAge()
      if (namePass && agePass) {
        setTimeout(() => {
          this.activeForm = 'qualification'
        }, 0)
      }
    },

    validateIdentityForm() {
      return this.validateName() && this.validateAge() && this.validateSex()
    },

    validateSesameScore() {
      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateCredit() {
      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateOther() {
      if (!this.form.clearAll) {
        const keys = ['isProvident', 'isHouse', 'isVehicle', 'isSocial', 'isInsure', 'isBusiness']
        // 至少选择一项
        const selectedKeys = keys.filter((key) => this.form[key])
        if (selectedKeys.length === 0) {
          uni.showToast({
            title: '请选择其他资质',
            icon: 'none'
          })

          return false
        }

        return true
      }

      return true
    },

    validateQualificationForm() {
      return this.validateSesameScore() && this.validateCredit() && this.validateOther()
    },

    setActiveForm(formName) {
      if (formName === 'qualification') {
        if (!this.validateIdentityForm()) {
          return
        }
      }

      this.activeForm = formName
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index

      this.activeFormItem = 'credit'
    },

    async fetchProtocol() {
      // 知情告知书，个人信息共享授权书，通用授权书
      this.agreementList = await getAgreements('axfq-szhh-auth')
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value

      this.activeFormItem = 'other'
    },

    clickOther(item) {
      if (item.key === 'clearAll') {
        this.otherOptions.forEach((option) => {
          this.form[option.key] = false
        })
        this.form.clearAll = true
      } else {
        this.form[item.key] = !this.form[item.key]
        this.form.clearAll = false
      }
    },

    async clickSubmit() {
      throttle(() => {
        if (!this.form.consumerId) {
          return
        }

        setFlowIndex(0)
        this.submitHandler()
      })
    },

    async submitHandler() {
      if (
        !this.validateCity() ||
        !this.validateIdentityForm() ||
        !this.validateQualificationForm()
      ) {
        return
      }

      this.agreeCountdownNumber = 3
      if (this.agreeCountdownTimer) {
        clearInterval(this.agreeCountdownTimer)
      }
      this.agreeCountdownTimer = setInterval(() => {
        this.agreeCountdownNumber--
        if (this.agreeCountdownNumber === 0) {
          clearInterval(this.agreeCountdownTimer)
          this.agreeCountdownTimer = null
        }
      }, 1000)

      this.$refs.agreementPopup.open()
      this.$forceUpdate()

      await saveUserInfo(this.getUserParams())

      this.nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })
    },

    getUserParams() {
      const params = {}
      params.cityCode = this.form.cityCode
      params.name = this.form.name
      params.age = this.form.age
      params.sex = this.form.sex
      params.phone = this.form.phone
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      params.isOverdue = this.form.isOverdue
      params.isProvident = this.form.isProvident
      params.isHouse = this.form.isHouse
      params.isVehicle = this.form.isVehicle
      params.isSocial = this.form.isSocial
      params.isInsure = this.form.isInsure
      params.isBusiness = this.form.isBusiness

      return params
    },

    clickAgreeAndContinue() {
      throttle(this.agreeAndContinueHandler)
    },

    async agreeAndContinueHandler() {
      if (this.agreeCountdownNumber > 0) {
        return
      }

      this.isAgree = true

      await fetchFormMatchApply({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (this.nextFlow) {
        this.navigateToNextFlow()
      } else {
        this.$refs.loading.open()
        if (this.navigateTimer) {
          clearInterval(this.navigateTimer)
        }
        this.navigateTimer = setInterval(() => {
          if (this.nextFlow) {
            this.$refs.loading.close()
            clearInterval(this.navigateTimer)
            this.navigateToNextFlow()
          }
        }, 1000)
      }
    },

    async navigateToNextFlow() {
      const routeMap = {
        wechat: '/extreme/v144/qw/index',
        overloan: '/extreme/v144/applyOther/index',
        end: '/extreme/v144/download/index',
        wechat_official_account: '/extreme/v144/wechatOfficialAccount/index',
        halfapi: '/extreme/v144/webviewDown/index'
      }
      const nextFlow = this.nextFlow

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = {
        ...this.form,
        ...this.getUserParams()
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    clickAgreement() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    toggleAgreement(index) {
      this.agreementIndex = index
      this.$refs.agreementContent.$el.scrollTop = 0
    }
  }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <scroll-view
      class="page-content"
      scroll-y="true"
      :scroll-into-view="scrollIntoView"
      scroll-with-animation="true"
      @scrolltolower="scrollIntoView = ''"
    >
      <view class="header">
        <view class="header-title">
          <view class="header-title-text">还差一步，即可获取额度</view>
          <view class="header-title-subtext">最高可借200,000元</view>
        </view>
        <view class="tag">
          <view class="tag-number">1000</view>
          <view class="tag-gap-text">人</view>
          已放款
        </view>
      </view>

      <view class="city">
        <view class="city-item">
          <view class="city-label">所在城市</view>
          <view class="city-value">
            <picker
              range-key="name"
              mode="multiSelector"
              :value="form.city"
              :range="areaData"
              @change="regionChange"
              @columnchange="regionColChange"
            >
              <view style="display: flex; align-items: center">
                <image
                  class="city-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/b1eb7edc30a94075bcbcbb5f279a0306.png"
                />
                <view v-if="form.cityName" class="city-name">{{ form.cityName }}</view>
                <view v-else class="placeholder">请选择城市</view>
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </picker>
          </view>
        </view>
        <view class="city-tips">*请务必选择您当前长期居住的城市，否则将影响借款结果</view>
      </view>

      <view
        class="identity card"
        :class="{ activeForm: activeForm === 'identity' }"
        @click="setActiveForm('identity')"
      >
        <view class="card-header">
          <view class="card-header-label">
            <image
              class="card-header-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/e305ec388af146448d68a62f514dd8c4.png"
            ></image>
            <view>身份信息</view>
          </view>
          <uni-icons
            :type="activeForm === 'identity' ? 'top' : 'bottom'"
            size="40rpx"
            color="#999"
          ></uni-icons>
        </view>

        <view class="identity-form" v-if="activeForm === 'identity'">
          <view class="identity-form-item form-item">
            <view class="form-item-label">姓名</view>
            <view class="form-item-value">
              <input
                placeholder="请输入姓名"
                maxlength="10"
                placeholder-class="placeholder"
                v-model="form.name"
                @blur="nameBlur"
              />
            </view>
          </view>

          <view class="form-item-line"></view>

          <view class="identity-form-item form-item">
            <view class="form-item-label">年龄</view>
            <view class="form-item-value">
              <input
                placeholder="请填写年龄"
                maxlength="3"
                placeholder-class="placeholder"
                v-model="form.age"
                @blur="ageBlur"
              />
            </view>
          </view>

          <view class="form-item-line"></view>

          <view class="identity-form-item form-item">
            <view class="form-item-label">性别</view>
            <view class="form-item-value">
              <view class="radio">
                <view class="radio-item" :class="{ selected: form.sex === 0 }" @click="clickSex(0)"
                  >男</view
                >
                <view class="radio-item" :class="{ selected: form.sex === 1 }" @click="clickSex(1)"
                  >女</view
                >
              </view>
            </view>
          </view>
        </view>
      </view>

      <view
        class="qualification card"
        :class="{ activeForm: activeForm === 'qualification' }"
        @click="setActiveForm('qualification')"
      >
        <view class="card-header">
          <view class="card-header-label">
            <image
              class="card-header-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/50a831e454784a72b51e0d650f3e3b0b.png"
            ></image>
            <view>资质信息</view>
          </view>
          <uni-icons
            :type="activeForm === 'qualification' ? 'top' : 'bottom'"
            size="40rpx"
            color="#999"
          ></uni-icons>
        </view>

        <view class="qualification-form" v-if="activeForm === 'qualification'">
          <view class="qualification-form-item form-item" @click="activeFormItem = 'sesameScore'">
            <view class="form-item-label">芝麻分</view>
            <view class="form-item-value">
              <input
                disabled
                style="pointer-events: none"
                :value="
                  sesameScoreOptions[form.sesameScoreIndex]
                    ? sesameScoreOptions[form.sesameScoreIndex].label
                    : ''
                "
                placeholder="请选择芝麻分"
                placeholder-class="placeholder"
              />
              <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
            </view>
          </view>

          <view class="form-item-line"></view>

          <view class="radio-group" v-if="activeFormItem === 'sesameScore'">
            <view
              class="radio"
              v-for="(item, index) in sesameScoreOptions"
              :key="index"
              :class="{ selected: index === form.sesameScoreIndex }"
              @click="clickSesameScore(index)"
            >
              {{ item.label }}

              <image
                v-if="index === form.sesameScoreIndex"
                class="radio-selected-icon"
                src="https://cdn.oss-unos.hmctec.cn/common/path/96725269326245cc99eae1702700ee05.png"
              ></image>
            </view>
          </view>

          <view class="qualification-form-item form-item" @click="activeFormItem = 'credit'">
            <view class="form-item-label">信用情况</view>
            <view class="form-item-value">
              <input
                disabled
                style="pointer-events: none"
                :value="
                  creditOptions[form.creditIndex] ? creditOptions[form.creditIndex].label : ''
                "
                placeholder="请选择信用情况"
                placeholder-class="placeholder"
              />
              <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
            </view>
          </view>

          <view class="form-item-line"></view>

          <view class="radio-group" v-if="activeFormItem === 'credit'">
            <view
              class="radio"
              v-for="(item, index) in creditOptions"
              :key="index"
              :class="{ selected: index === form.creditIndex }"
              @click="clickCredit(index)"
            >
              {{ item.label }}

              <image
                v-if="index === form.creditIndex"
                class="radio-selected-icon"
                src="https://cdn.oss-unos.hmctec.cn/common/path/96725269326245cc99eae1702700ee05.png"
              ></image>
            </view>
          </view>

          <view class="qualification-form-item form-item" @click="activeFormItem = 'other'">
            <view class="form-item-label">
              其他资产
              <text class="form-item-desc">*至少选择一项</text>
            </view>
            <view class="form-item-value">
              <input
                style="pointer-events: none"
                disabled
                placeholder="选择额度可提额"
                placeholder-class="placeholder"
              />
              <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
            </view>
          </view>

          <template v-if="activeFormItem === 'other'">
            <view class="form-item-line"></view>

            <view class="radio-group" id="other-form-item">
              <view
                class="radio"
                v-for="(item, index) in otherOptions"
                :key="index"
                :class="{ selected: form[item.key] }"
                @click="clickOther(item)"
              >
                {{ item.label }}
                <image
                  v-if="form[item.key]"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/96725269326245cc99eae1702700ee05.png"
                ></image>

                <image
                  v-if="item.highQuality"
                  class="radio-high-quality-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/625eaa5e5b534bf79de4c2d518408a19.png"
                ></image>
              </view>
            </view>
          </template>
        </view>
      </view>
    </scroll-view>
    <view class="page-footer">
      <view class="submit-btn" @click="clickSubmit">提交申请</view>
    </view>

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/5df47460281845d0963eb45c925611b0.png"
          class="agreement-popup-bg"
        ></image>

        <view class="agreement-popup-header">
          <view class="agreement-popup-title">请勿接听任何境外电话</view>
          <view class="agreement-popup-desc">
            任何放款前以缴纳
            <text class="highlight">保证金、利息等</text>
            名义的行为均为
            <text class="highlight">诈骗</text>
            ，任何要求您提供
            <text class="highlight">银行卡密码、验证码等</text>
            个人信息均为
            <text class="highlight">诈骗</text>
            。
          </view>
        </view>

        <view class="agreement-popup-body" v-if="agreementList.length > 0">
          <view class="agreement-name-container">
            <view
              class="agreement-name"
              v-for="(item, index) in agreementList"
              :key="index"
              @click="toggleAgreement(index)"
              :class="{ selected: index === agreementIndex }"
            >
              <view>{{ item.name }}</view>
              <view v-if="index !== agreementList.length - 1" class="agreement-name-line"></view>
            </view>
          </view>
          <view class="agreement-content" ref="agreementContent">
            <div v-html="agreementList[agreementIndex].content"></div>
          </view>
        </view>

        <view class="agreement-agree-container">
          <view
            class="agreement-agree-btn"
            @click="clickAgreeAndContinue"
            :class="{ disabled: !!agreeCountdownNumber }"
          >
            同意协议，继续申请
            <text v-if="agreeCountdownNumber">（{{ agreeCountdownNumber }}S）</text>
          </view>
          <view class="agreement-agree-tips"
            >若当前暂无匹配机构，请允许稍后持续为您匹配合适机构</view
          >
        </view>
      </view>
    </uni-popup>

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<style scoped lang="scss">
$color-primary: #2737A8;

.color-primary {
  color: $color-primary;
}

.page-container {
  height: 100vh;
  background: #f6f6f8;
  // display: flex;
  // flex-direction: column;

  .page-content {
    flex: 1;
    overflow: hidden auto;
    position: relative;
    padding-bottom: 400rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/ceb876686a4e479fb37f608ead249083.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .agreement {
      display: flex;
      gap: 5rpx;

      .agree-icon {
        flex-shrink: 0;
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;

        .name {
          color: $color-primary;
        }
      }
    }

    .submit-btn {
      //margin-top: 20rpx;
      padding: 21rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 56rpx;
      text-align: center;
    }
  }
}

.header {
  position: relative;
  padding: 45rpx 0 45rpx 30rpx;

  .header-title {
    .header-title-text {
      margin-bottom: 16rpx;
      font-weight: 700;
      font-size: 38rpx;
      color: #333333;
      line-height: 50rpx;
    }

    .header-title-subtext {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;
    }
  }

  .tag {
    position: absolute;
    top: 95rpx;
    right: 0;
    padding: 6rpx 7rpx 6rpx 10rpx;
    background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
    border-radius: 20rpx 0rpx 0rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #5a352f;
    line-height: 16rpx;
    display: flex;
    align-items: center;

    .tag-number {
      font-weight: 400;
      font-size: 24rpx;
      color: #5a352f;
      line-height: 28rpx;
    }

    .tag-gap-text {
      margin-left: 3rpx;
      margin-right: 6rpx;
    }
  }
}

.city {
  position: relative;
  margin: 0 30rpx;
  background: #ffffff;
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  padding: 29rpx 32rpx;

  .city-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .city-label {
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 52rpx;
    }

    .city-value {
      display: flex;
      align-items: center;

      .city-icon {
        margin-right: 10rpx;
        width: 40rpx;
        height: 40rpx;
      }

      .city-name {
        max-width: 8em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 30rpx;
        font-weight: 400;
        font-size: 36rpx;
        color: #333333;
        line-height: 52rpx;
      }
    }
  }

  .city-tips {
    margin-top: 20rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #a2a3a5;
    line-height: 32rpx;
  }
}

.identity {
  .identity-form {
    .identity-form-item {
      .form-item-value {
        font-weight: 400;
        font-size: 32rpx;
        color: #999999;
        line-height: 46rpx;
        text-align: right;

        .radio {
          display: flex;
          align-items: center;
          gap: 12rpx;

          .radio-item {
            background: #f6f6f8;
            border-radius: 10rpx 10rpx 10rpx 10rpx;
            font-weight: 400;
            font-size: 26rpx;
            color: #999999;
            line-height: 34rpx;
            padding: 7rpx 36rpx;
            border: 1rpx solid #f6f6f8;

            &.selected {
              background: #EEF0FF;
              border-color: $color-primary;
              color: $color-primary;
            }
          }
        }
      }
    }
  }
}

.card {
  position: relative;
  margin: 40rpx 30rpx 0;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  padding-bottom: 20rpx;
  border: 1rpx solid #ffffff;

  &.activeForm {
    border: 1rpx solid $color-primary;
  }

  .card-header {
    padding: 30rpx 32rpx 10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-header-label {
    display: flex;
    gap: 12rpx;
    align-items: center;
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 50rpx;
  }

  .card-header-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.form-item-line {
  margin-left: 32rpx;
  height: 1rpx;
  background: #e2e2e2;
}

.form-item {
  padding: 25rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item-label {
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
}

.qualification {
  .qualification-form {
    .qualification-form-item {
      .form-item-label {
        width: 380rpx;

        .form-item-desc {
          margin-left: 10rpx;
          color: #f01515;
          font-size: 24rpx;
        }
      }

      .form-item-value {
        display: flex;
        align-items: center;
        gap: 22rpx;
        font-weight: 400;
        font-size: 32rpx;
        color: #999999;
        line-height: 46rpx;
        text-align: right;
      }
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15rpx;
      padding: 20rpx 30rpx;

      .radio {
        overflow: hidden;
        position: relative;
        width: 198rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f6f6f8;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;
        text-align: center;
        border: 1rpx solid #f6f6f8;

        &.selected {
          border-color: $color-primary;
          background-color: #ebf3ff;
          color: $color-primary;
        }

        .radio-selected-icon {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 34rpx;
          height: 34rpx;
        }

        .radio-high-quality-icon {
          position: absolute;
          right: 0;
          top: 0;
          width: 52rpx;
          height: 23rpx;
        }
      }
    }
  }
}

.placeholder {
  font-weight: normal;
  font-size: 32rpx;
  color: #a2a3a5;
  line-height: 40rpx;
  font-style: normal;
  text-transform: none;
}

.agreement-popup {
  padding: 40rpx 30rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0e5ec5a88f59400293ab9a9df2638efd.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-popup-header {
    position: relative;
  }

  .agreement-popup-bg {
    position: absolute;
    top: -10rpx;
    right: 40rpx;
    width: 166rpx;
    height: 166rpx;
  }

  .agreement-popup-title {
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 46rpx;
  }

  .agreement-popup-desc {
    margin-top: 23rpx;
    padding: 25rpx 30rpx;
    background: rgba(39,55,168,0.12);
    border-radius: 14rpx 14rpx 14rpx 14rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    line-height: 40rpx;

    .highlight {
      color: #333;
    }
  }

  .agreement-popup-body {
    padding: 25rpx 25rpx 0;
    margin-top: 25rpx;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    border: 1rpx solid $color-primary;
    border-bottom: none;

    .agreement-name-container {
      margin-bottom: 28rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 46rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .selected {
        color: #333333;
      }

      .agreement-name {
        display: flex;
        align-items: center;

        .agreement-name-line {
          margin: 0 20rpx;
          width: 2rpx;
          height: 22rpx;
          background: #d8d8d8;
        }

        &:last-child {
          .agreement-name-line {
            display: none;
          }
        }
      }
    }

    .agreement-content {
      overflow: auto;
      height: 400rpx;
      //font-weight: 400;
      //font-size: 28rpx;
      //color: #333333;
      //line-height: 46rpx;
    }
  }

  .agreement-agree-container {
    position: relative;
    padding: 10rpx 0 0;

    &::after {
      content: '';
      position: absolute;
      left: -2rpx;
      right: -2rpx;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      padding: 24rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;

      &.disabled {
        opacity: 0.7;
      }
    }

    .agreement-agree-tips {
      margin-top: 10rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #a3898a;
      line-height: 32rpx;
      text-align: center;
    }
  }
}
</style>
