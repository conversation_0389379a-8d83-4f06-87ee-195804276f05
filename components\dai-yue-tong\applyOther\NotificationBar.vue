<template>
  <view class="notification-bar">
    <!-- icon -->
    <image
      class="notification-icon"
      src="https://cdn.oss-unos.hmctec.cn/common/path/ea8d42a1742042e8bfd894b1358e94a3.png"
      mode="scaleToFill"
    />
    <text class="notification-text">{{
      message || '183****4565已申请四款产品，成功获得额度'
    }}</text>
  </view>
</template>

<script>
export default {
  name: 'NotificationBar',
  props: {
    message: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.notification-bar {
  margin: 32rpx auto 0;
  width: 686rpx;
  height: 82rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 24rpx;

  .notification-icon {
    width: 36rpx;
    height: 36rpx;
  }

  .notification-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 34rpx;
  }
}
</style>
