<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="32"
    height="32"
    viewBox="0 0 32 32"
  >
    <defs>
      <clipPath id="master_svg0_559_13411">
        <rect x="0" y="0" width="32" height="32" rx="0" />
      </clipPath>
    </defs>
    <g>
      <g clip-path="url(#master_svg0_559_13411)">
        <g>
          <path
            d="M16,0L24,0Q24.1964,0,24.3925,0.00963635Q24.5887,0.0192727,24.7841,0.0385222Q24.9796,0.0577717,25.1738,0.0865879Q25.3681,0.115404,25.5607,0.153718Q25.7533,0.192031,25.9438,0.23975Q26.1343,0.287469,26.3223,0.344477Q26.5102,0.401486,26.6951,0.467647Q26.88,0.533809,27.0615,0.608964Q27.2429,0.684118,27.4204,0.768086Q27.598,0.852053,27.7712,0.94463Q27.9444,1.03721,28.1128,1.13817Q28.2813,1.23914,28.4446,1.34824Q28.6079,1.45735,28.7656,1.57434Q28.9233,1.69133,29.0751,1.81592Q29.227,1.9405,29.3725,2.07239Q29.518,2.20428,29.6569,2.34315Q29.7957,2.48201,29.9276,2.62753Q30.0595,2.77304,30.1841,2.92485Q30.3087,3.07666,30.4257,3.23441Q30.5426,3.39215,30.6518,3.55544Q30.7609,3.71873,30.8618,3.88718Q30.9628,4.05563,31.0554,4.22883Q31.1479,4.40203,31.2319,4.57956Q31.3159,4.75709,31.391,4.93853Q31.4662,5.11997,31.5323,5.30488Q31.5985,5.48979,31.6555,5.67772Q31.7125,5.86565,31.7602,6.05616Q31.808,6.24666,31.8463,6.43928Q31.8846,6.63189,31.9134,6.82616Q31.9422,7.02042,31.9615,7.21586Q31.9807,7.41131,31.9904,7.60746Q32,7.80361,32,8L32,24Q32,24.1964,31.9904,24.3925Q31.9807,24.5887,31.9615,24.7841Q31.9422,24.9796,31.9134,25.1738Q31.8846,25.3681,31.8463,25.5607Q31.808,25.7533,31.7602,25.9438Q31.7125,26.1343,31.6555,26.3223Q31.5985,26.5102,31.5323,26.6951Q31.4662,26.88,31.391,27.0615Q31.3159,27.2429,31.2319,27.4204Q31.1479,27.598,31.0554,27.7712Q30.9628,27.9444,30.8618,28.1128Q30.7609,28.2813,30.6518,28.4446Q30.5426,28.6079,30.4257,28.7656Q30.3087,28.9233,30.1841,29.0751Q30.0595,29.227,29.9276,29.3725Q29.7957,29.518,29.6569,29.6569Q29.518,29.7957,29.3725,29.9276Q29.227,30.0595,29.0751,30.1841Q28.9233,30.3087,28.7656,30.4257Q28.6079,30.5426,28.4446,30.6518Q28.2813,30.7609,28.1128,30.8618Q27.9444,30.9628,27.7712,31.0554Q27.598,31.1479,27.4204,31.2319Q27.2429,31.3159,27.0615,31.391Q26.88,31.4662,26.6951,31.5323Q26.5102,31.5985,26.3223,31.6555Q26.1343,31.7125,25.9438,31.7602Q25.7533,31.808,25.5607,31.8463Q25.3681,31.8846,25.1738,31.9134Q24.9796,31.9422,24.7841,31.9615Q24.5887,31.9807,24.3925,31.9904Q24.1964,32,24,32L8,32Q7.80361,32,7.60746,31.9904Q7.41131,31.9807,7.21586,31.9615Q7.02042,31.9422,6.82616,31.9134Q6.63189,31.8846,6.43928,31.8463Q6.24666,31.808,6.05616,31.7602Q5.86565,31.7125,5.67772,31.6555Q5.48979,31.5985,5.30488,31.5323Q5.11997,31.4662,4.93853,31.391Q4.75709,31.3159,4.57956,31.2319Q4.40203,31.1479,4.22883,31.0554Q4.05563,30.9628,3.88718,30.8618Q3.71873,30.7609,3.55544,30.6518Q3.39215,30.5426,3.23441,30.4257Q3.07666,30.3087,2.92485,30.1841Q2.77304,30.0595,2.62753,29.9276Q2.48201,29.7957,2.34315,29.6569Q2.20428,29.518,2.07239,29.3725Q1.9405,29.227,1.81592,29.0751Q1.69133,28.9233,1.57434,28.7656Q1.45735,28.6079,1.34824,28.4446Q1.23914,28.2813,1.13817,28.1128Q1.03721,27.9444,0.94463,27.7712Q0.852053,27.598,0.768086,27.4204Q0.684118,27.2429,0.608964,27.0615Q0.533809,26.88,0.467647,26.6951Q0.401486,26.5102,0.344477,26.3223Q0.287469,26.1343,0.23975,25.9438Q0.192031,25.7533,0.153718,25.5607Q0.115404,25.3681,0.0865879,25.1738Q0.0577717,24.9796,0.0385222,24.7841Q0.0192727,24.5887,0.00963635,24.3925Q0,24.1964,0,24L0,8Q0,7.80361,0.00963635,7.60746Q0.0192727,7.41131,0.0385222,7.21586Q0.0577717,7.02042,0.0865879,6.82616Q0.115404,6.63189,0.153718,6.43928Q0.192031,6.24666,0.23975,6.05616Q0.287469,5.86565,0.344477,5.67772Q0.401486,5.48979,0.467647,5.30488Q0.533809,5.11997,0.608964,4.93853Q0.684118,4.75709,0.768086,4.57956Q0.852053,4.40203,0.94463,4.22883Q1.03721,4.05563,1.13817,3.88718Q1.23914,3.71873,1.34824,3.55544Q1.45735,3.39215,1.57434,3.23441Q1.69133,3.07666,1.81592,2.92485Q1.9405,2.77304,2.07239,2.62753Q2.20428,2.48201,2.34315,2.34315Q2.48201,2.20428,2.62753,2.07239Q2.77304,1.9405,2.92485,1.81592Q3.07666,1.69133,3.23441,1.57434Q3.39215,1.45735,3.55544,1.34824Q3.71873,1.23914,3.88718,1.13817Q4.05563,1.03721,4.22883,0.94463Q4.40203,0.852053,4.57956,0.768086Q4.75709,0.684118,4.93853,0.608964Q5.11997,0.533809,5.30488,0.467647Q5.48979,0.401486,5.67772,0.344477Q5.86565,0.287469,6.05616,0.23975Q6.24666,0.192031,6.43928,0.153718Q6.63189,0.115404,6.82616,0.0865879Q7.02042,0.0577717,7.21586,0.0385222Q7.41131,0.0192727,7.60746,0.00963635Q7.80361,0,8,0L16,0ZM12.7902,22.4898L6.98756,16.7889L8.01244,15.288L12.7902,18.7324L24.0124,9.51022L25.0124,10.288L12.7902,22.4898Z"
            fill-rule="evenodd"
            :fill="color"
            fill-opacity="1"
          />
        </g>
      </g>
    </g>
  </svg>
</template>

<script>
export default {
  name: 'checked',
  props: {
    color: {
      type: String,
      default: '#013DDF'
    }
  }
}
</script>

<style lang="scss" scoped></style>
