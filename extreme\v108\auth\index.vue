<script>
import {
  consumerAssetsUpdate,
  getOnlineLowProduct,
  getProtocolDetail,
  getProtocolList
} from '@/apis/common-2'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { formatRichText } from '@/utils/utils'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hrh.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { nextFlowNode } from '@/utils/processFlowFunctions'

export default {
  name: 'AuthIndex',
  components: {
    Header,
    FullScreenMatchLoading
  },

  beforeDestroy() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
      this.codeTimer = null
    }
  },

  data() {
    return {
      form: {
        templateVersion: 'v108',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false,
        consumerId: '',
        /**
         * directMatchFlag
         * 1 有用户资质信息，无需填写资料，直接匹配企微、半流程和贷超
         * 2 无用户资质信息，填写资料匹配企微和贷超
         */
        directMatchFlag: '',
        channelId: '',
        demandAmount: '50000'
      },
      // 默认展示资质信息
      activeForm: 'qualification',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 115,
          label: '700分以上'
        },
        {
          value: 113,
          label: '650-700分'
        },
        {
          value: 112,
          label: '600-650分'
        },
        {
          value: 110,
          label: '600分以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      agreementList: [],
      agreementIndex: 0,
      scrollIntoView: '',
      productList: [],
      showPage: false
    }
  },

  async onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.fetchProtocol()
  },

  methods: {
    validateSesameScore() {
      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateCredit() {
      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateQualificationForm() {
      return this.validateSesameScore() && this.validateCredit()
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index

      this.activeFormItem = 'credit'
    },

    async fetchProtocol() {
      const agreementListRes = await getProtocolList({
        protocolSig: 'hrh-hmc-auth'
      })
      this.agreementList = agreementListRes.data || []
      this.agreementList.forEach((agreement) => {
        getProtocolDetail({ protocolId: agreement.protocolId }).then((res) => {
          agreement.content = formatRichText(res.data.content, {})
        })
      })
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (!this.validateQualificationForm()) {
        return
      }

      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    getUserParams() {
      const params = {}
      params.smsId = this.vuex_smsId
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      params.isOverdue = this.form.isOverdue

      return params
    },

    clickAgreeAndContinue() {
      throttle(async () => {
        if (!this.validateQualificationForm()) {
          return
        }

        const res = await consumerAssetsUpdate(this.getUserParams())
        if (res.code == 200) {
          this.form.consumerId = res.data
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)

        this.$refs.loading.open()
        this.navigateToNextFlow()
      })
    },

    async navigateToNextFlow() {
      const routeMap = {
        offline: '/extreme/v108/apply/index',
        wechat: '/extreme/v108/qw/index',
        overloan: '/extreme/v108/applyOther/index',
        end: '/extreme/v108/download/index'
      }

      const customFetch = {
        wechat: getOnlineLowProduct,
        overloan: getOnlineLowProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )

      if (nextFlow.flow === 'halfapi') {
        this.$refs.loading.close()
        window.location.href = nextFlow.url
        return
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    clickAgreement(item, index) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
      this.$nextTick(() => {
        this.toggleAgreement(index)
      })
    },

    toggleAgreement(index) {
      this.agreementIndex = index
      this.$refs.agreementContent.$el.scrollTop = 0
    }
  }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <template v-if="showPage">
      <scroll-view
        class="page-content"
        scroll-y="true"
        :scroll-into-view="scrollIntoView"
        scroll-with-animation="true"
        @scrolltolower="scrollIntoView = ''"
      >
        <Header />

        <view class="header">
          <view class="header-title">
            <view class="header-title-text">还差一步，即可获取额度</view>
            <view class="header-title-subtext">最高可借200,000元</view>
          </view>
          <view class="tag">
            <view class="tag-number">1000</view>
            <view class="tag-gap-text">人</view>
            已放款
          </view>
        </view>

        <view
          class="qualification card"
          :class="{ activeForm: activeForm === 'qualification' }"
          @click="setActiveForm('qualification')"
        >
          <view class="card-header">
            <view class="card-header-label">
              <image
                class="card-header-icon"
                src="https://cdn.oss-unos.hmctec.cn/common/path/d54a601be2f643d2a552cda92bf6c120.png"
              ></image>
              <view>资质信息</view>
            </view>
            <!-- <uni-icons :type="activeForm === 'qualification' ? 'top' : 'bottom'" size="40rpx" color="#999"></uni-icons> -->
          </view>

          <view class="qualification-form" v-if="activeForm === 'qualification'">
            <view class="qualification-form-item form-item" @click="activeFormItem = 'sesameScore'">
              <view class="form-item-label">芝麻分</view>
              <view class="form-item-value">
                <input
                  disabled
                  style="pointer-events: none"
                  :value="
                    sesameScoreOptions[form.sesameScoreIndex]
                      ? sesameScoreOptions[form.sesameScoreIndex].label
                      : ''
                  "
                  placeholder="请选择芝麻分"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>

            <view class="form-item-line"></view>

            <view class="radio-group" v-if="activeFormItem === 'sesameScore'">
              <view
                class="radio"
                v-for="(item, index) in sesameScoreOptions"
                :key="index"
                :class="{ selected: index === form.sesameScoreIndex }"
                @click="clickSesameScore(index)"
              >
                {{ item.label }}

                <image
                  v-if="index === form.sesameScoreIndex"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/a5fe63a751d543b986182e93ea8d16d8.png"
                ></image>
              </view>
            </view>

            <view class="qualification-form-item form-item" @click="activeFormItem = 'credit'">
              <view class="form-item-label">信用情况</view>
              <view class="form-item-value">
                <input
                  disabled
                  style="pointer-events: none"
                  :value="
                    creditOptions[form.creditIndex] ? creditOptions[form.creditIndex].label : ''
                  "
                  placeholder="请选择信用情况"
                  placeholder-class="placeholder"
                />
                <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
              </view>
            </view>

            <!-- <view class="form-item-line"></view> -->

            <view class="radio-group" v-if="activeFormItem === 'credit'">
              <view
                class="radio"
                v-for="(item, index) in creditOptions"
                :key="index"
                :class="{ selected: index === form.creditIndex }"
                @click="clickCredit(index)"
              >
                {{ item.label }}

                <image
                  v-if="index === form.creditIndex"
                  class="radio-selected-icon"
                  src="https://cdn.oss-unos.hmctec.cn/common/path/a5fe63a751d543b986182e93ea8d16d8.png"
                ></image>
              </view>
            </view>
          </view>
        </view>
      </scroll-view>
      <view class="page-footer">
        <view class="agreement">
          <view class="agreement-text">
            我已阅读并同意
            <text
              class="name"
              v-for="(item, index) in agreementList"
              :key="item.protocolId"
              @click="clickAgreement(item, index)"
            >
              《{{ item.name }}》
            </text>
          </view>
        </view>
        <view class="submit-btn" @click="clickSubmit">提交申请</view>
      </view>

      <uni-popup
        background-color="#fff"
        ref="agreementPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="agreement-popup">
          <image
            src="https://cdn.oss-unos.hmctec.cn/common/path/148142372cca4f9ba688a388447b518e.png"
            class="agreement-popup-bg"
          ></image>

          <view class="agreement-popup-header">
            <view class="agreement-popup-title">请勿接听任何境外电话</view>
            <view class="agreement-popup-desc">
              任何放款前以缴纳
              <text class="highlight">保证金、利息等</text>
              名义的行为均为
              <text class="highlight">诈骗</text>
              ，任何要求您提供
              <text class="highlight">银行卡密码、验证码等</text>
              个人信息均为
              <text class="highlight">诈骗</text>
              。
            </view>
          </view>

          <view class="agreement-popup-body" v-if="agreementList.length > 0">
            <view class="agreement-name-container">
              <view
                class="agreement-name"
                v-for="(item, index) in agreementList"
                :key="index"
                @click="toggleAgreement(index)"
                :class="{ selected: index === agreementIndex }"
              >
                <view>{{ item.name }}</view>
                <view v-if="index !== agreementList.length - 1" class="agreement-name-line"></view>
              </view>
            </view>
            <view class="agreement-content" ref="agreementContent">
              <div v-html="agreementList[agreementIndex].content"></div>
            </view>
          </view>

          <view class="agreement-agree-container">
            <view class="agreement-agree-btn" @click="clickAgreeAndContinue">
              同意协议，继续申请
            </view>
            <view class="agreement-agree-tips"
              >若当前暂无匹配机构，请允许稍后持续为您匹配合适机构</view
            >
          </view>
        </view>
      </uni-popup>
    </template>
    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<style scoped lang="scss">
$color-primary: #7546fc;

.color-primary {
  color: $color-primary;
}

.page-container {
  height: 100vh;
  background: #f6f6f8;
  // display: flex;
  // flex-direction: column;

  .page-content {
    flex: 1;
    overflow: hidden auto;
    position: relative;
    padding-bottom: 400rpx;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/fb6f816ed2984d87979f9b671ecdc5e7.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      //transform: scale(3);
    }
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .agreement {
      margin-bottom: 20rpx;
      display: flex;
      gap: 5rpx;

      .agree-icon {
        flex-shrink: 0;
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;

        .name {
          color: $color-primary;
        }
      }
    }

    .submit-btn {
      //margin-top: 20rpx;
      padding: 21rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 40rpx;
      color: #ffffff;
      line-height: 56rpx;
      text-align: center;
    }
  }
}

.header {
  position: relative;
  padding: 0 0 45rpx 30rpx;

  .header-title {
    .header-title-text {
      margin-bottom: 16rpx;
      font-weight: 700;
      font-size: 38rpx;
      color: #333333;
      line-height: 50rpx;
    }

    .header-title-subtext {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;
    }
  }

  .tag {
    position: absolute;
    top: 50rpx;
    right: 0;
    padding: 6rpx 7rpx 6rpx 10rpx;
    background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
    border-radius: 20rpx 0rpx 0rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #5a352f;
    line-height: 16rpx;
    display: flex;
    align-items: center;

    .tag-number {
      font-weight: 400;
      font-size: 24rpx;
      color: #5a352f;
      line-height: 28rpx;
    }

    .tag-gap-text {
      margin-left: 3rpx;
      margin-right: 6rpx;
    }
  }
}

.card {
  position: relative;
  margin: 40rpx 30rpx 0;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 30rpx 30rpx 30rpx 30rpx;
  padding-bottom: 20rpx;
  border: 1rpx solid #ffffff;

  &.activeForm {
    border: 1rpx solid $color-primary;
  }

  .card-header {
    padding: 30rpx 32rpx 10rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .card-header-label {
    display: flex;
    gap: 12rpx;
    align-items: center;
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 50rpx;
  }

  .card-header-icon {
    width: 40rpx;
    height: 40rpx;
  }
}

.form-item-line {
  margin-left: 32rpx;
  height: 1rpx;
  background: #e2e2e2;
}

.form-item {
  padding: 25rpx 32rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.form-item-label {
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
}

.form-item-value {
  display: flex;
  align-items: center;
  gap: 22rpx;
  font-weight: 400;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
  text-align: right;
}

.qualification {
  .qualification-form {
    .qualification-form-item {
      .form-item-label {
        width: 380rpx;

        .form-item-desc {
          margin-left: 10rpx;
          color: #f01515;
          font-size: 24rpx;
        }
      }
    }

    .radio-group {
      display: flex;
      flex-wrap: wrap;
      gap: 15rpx;
      padding: 20rpx 30rpx;

      .radio {
        overflow: hidden;
        position: relative;
        width: 198rpx;
        height: 80rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f6f6f8;
        border-radius: 16rpx 16rpx 16rpx 16rpx;
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;
        text-align: center;
        border: 1rpx solid #f6f6f8;

        &.selected {
          border-color: $color-primary;
          background-color: #f4f0ff;
          color: $color-primary;
        }

        .radio-selected-icon {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 34rpx;
          height: 34rpx;
        }

        .radio-high-quality-icon {
          position: absolute;
          right: 0;
          top: 0;
          width: 52rpx;
          height: 23rpx;
        }
      }
    }
  }
}

.get-code-btn {
  min-width: 60rpx;
  font-weight: 4000;
  font-size: 30rpx;
  color: $color-primary;
  line-height: 40rpx;
}

.placeholder {
  font-weight: normal;
  font-size: 32rpx;
  color: #a2a3a5;
  line-height: 40rpx;
  font-style: normal;
  text-transform: none;
}

.agreement-popup {
  padding: 40rpx 30rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0e5ec5a88f59400293ab9a9df2638efd.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-popup-header {
    position: relative;
  }

  .agreement-popup-bg {
    position: absolute;
    top: 15rpx;
    right: 40rpx;
    width: 185rpx;
    height: 173rpx;
  }

  .agreement-popup-title {
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 46rpx;
  }

  .agreement-popup-desc {
    margin-top: 23rpx;
    padding: 25rpx 30rpx;
    background: #fff7eb;
    border-radius: 14rpx 14rpx 14rpx 14rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    line-height: 40rpx;

    .highlight {
      color: #864508;
    }
  }

  .agreement-popup-body {
    padding: 25rpx 25rpx 0;
    margin-top: 25rpx;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    border: 1rpx solid $color-primary;
    border-bottom: none;

    .agreement-name-container {
      margin-bottom: 28rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 46rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .selected {
        color: #333333;
      }

      .agreement-name {
        display: flex;
        align-items: center;

        .agreement-name-line {
          margin: 0 20rpx;
          width: 2rpx;
          height: 22rpx;
          background: #d8d8d8;
        }

        &:last-child {
          .agreement-name-line {
            display: none;
          }
        }
      }
    }

    .agreement-content {
      overflow: auto;
      height: 400rpx;
      //font-weight: 400;
      //font-size: 28rpx;
      //color: #333333;
      //line-height: 46rpx;
    }
  }

  .agreement-agree-container {
    position: relative;
    padding: 10rpx 0 0;

    &::after {
      content: '';
      position: absolute;
      left: -2rpx;
      right: -2rpx;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      padding: 24rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;

      &.disabled {
        opacity: 0.7;
      }
    }

    .agreement-agree-tips {
      margin-top: 10rpx;
      font-weight: 400;
      font-size: 22rpx;
      color: #a3898a;
      line-height: 32rpx;
      text-align: center;
    }
  }
}
</style>
