<template>
  <uni-popup
    background-color="#fff"
    ref="popup"
    type="bottom"
    border-radius="32rpx 32rpx 0 0"
  >
    <view class="product-popup">
      <view class="institution">机构信息</view>
      <view class="product">
        <view class="product-content">
          <view class="product-info">
            <image :src="productData.logo" class="product-icon" />
            <view class="product-name">{{ productData.productName }}</view>
            <image
              src="https://cdn.oss-unos.hmctec.cn/common/path/f6d8666f3ba241b1ba3386b6e84a2cda.png"
              class="icon-hot"
            />
            <image
              src="https://cdn.oss-unos.hmctec.cn/common/path/1eda8b37a4724dd3a688ae4b11727e8c.png"
              class="icon-diamond-selection"
            />
          </view>
          <view class="product-feature">
            <view class="product-feature-item">
              <view class="product-feature-value highlight">{{
                productData.loanableFundsBig | toThousandFilter
              }}</view>
              <view class="product-feature-label">最高额度(元)</view>
            </view>
            <view class="product-feature-item">
              <view class="product-feature-value">{{ productData.putinPush }}</view>
              <view class="product-feature-label">放款人数</view>
            </view>
          </view>
        </view>
        <view class="product-checkbox">
          <image
            src="https://cdn.oss-unos.hmctec.cn/common/path/19f3b925dcfb42958fb6c588d9bfe752.png"
            class="product-checkbox-item"
            v-if="productData.uiChecked"
            @click.stop="handleCheckboxChange(false)"
          />
          <image
            src="https://cdn.oss-unos.hmctec.cn/common/path/a04bce66fd5840b88ff1760355a656d5.png"
            class="product-checkbox-item"
            v-else
            @click.stop="handleCheckboxChange(true)"
          />
          <view class="speech-bubble" v-if="!productData.uiChecked">
            <image
              class="icon-good"
              src="https://cdn.oss-unos.hmctec.cn/common/path/83c1fe9159764a77b8a0a46017422232.png"
            />
            通过率+3%
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'ProductPopup',

  props: {
    // 产品数据
    productData: {
      type: Object,
      default: () => ({})
    }
  },

  methods: {
    open() {
      this.$refs.popup.open()
    },

    close() {
      this.$refs.popup.close()
    },

    handleCheckboxChange(checked) {
      this.$emit('checkbox-change', { product: this.productData, checked })
    }
  }
}
</script>

<style scoped lang="scss">
.product-popup {
  background: linear-gradient(180deg, #d1e4ff 0%, #fcfcfc 31%);
  border-radius: 32rpx 32rpx 0rpx 0rpx;
  padding: 45rpx 25rpx 60rpx;

  .institution {
    margin-bottom: 24rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #1d283a;
    line-height: 52rpx;
  }

  .product {
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 30rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .product-content {
      .product-info {
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;

        .product-icon {
          margin-right: 15rpx;
          width: 48rpx;
          height: 48rpx;
        }

        .product-name {
          margin-right: 20rpx;
          font-weight: 400;
          font-size: 32rpx;
          color: #171a1d;
          line-height: 45rpx;
        }

        .icon-hot {
          margin-right: 15rpx;
          width: 94rpx;
          height: 34rpx;
        }

        .icon-diamond-selection {
          width: 123rpx;
          height: 36rpx;
        }
      }

      .product-feature {
        display: flex;
        align-items: center;
        gap: 70rpx;

        .product-feature-item {
          .product-feature-value {
            margin-bottom: 8rpx;
            font-family: D-DIN, D-DIN;
            font-weight: 700;
            font-size: 38rpx;
            color: #171a1d;
            line-height: 41rpx;
          }

          .product-feature-label {
            font-weight: normal;
            font-size: 24rpx;
            color: #919094;
            line-height: 34rpx;
          }

          .highlight {
            color: #2737A8;
          }
        }
      }
    }

    .product-checkbox {
      position: relative;
      width: 30rpx;
      height: 30rpx;

      .speech-bubble {
        width: 160rpx;
        height: 50rpx;
        display: flex;
        align-items: center;
        gap: 5rpx;
        position: absolute;
        top: -50rpx;
        right: -30rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #ffffff;
        line-height: 29rpx;
        background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/c8872af589644b15999e088c0121158e.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 10rpx 15rpx 20rpx;

        .icon-good {
          width: 20rpx;
          height: 20rpx;
        }
      }

      .product-checkbox-item {
        width: 30rpx;
        height: 30rpx;
      }
    }
  }
}
</style>
