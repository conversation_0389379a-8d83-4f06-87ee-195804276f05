<script>
import {
  consumerAssetsUpdate,
  getOnlineLowProduct,
  getProtocolDetail,
  getProtocolList,
  verifySmsIdV7
} from '@/apis/common-2'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { formatRichText } from '@/utils/utils'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-custom.vue'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { fetchChannelAndTemplate, getTemplateConfig, reportUV } from '@/apis/common'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'

export default {
  name: 'AuthIndex',
  components: {
    Header,
    FullScreenMatchLoading,
    Declaration
  },

  beforeDestroy() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
      this.codeTimer = null
    }
  },

  data() {
    return {
      form: {
        templateVersion: 'v109',
        sesameScoreIndex: '',
        creditIndex: '',
        isOverdue: false,
        consumerId: '',
        /**
         * directMatchFlag
         * 1 有用户资质信息，无需填写资料，直接匹配企微、半流程和贷超
         * 2 无用户资质信息，填写资料匹配企微和贷超
         */
        directMatchFlag: '',
        channelId: '',
        demandAmount: '50000'
      },
      // 默认展示资质信息
      activeForm: 'qualification',
      activeFormItem: 'sesameScore',
      sesameScoreOptions: [
        {
          value: 115,
          label: '700分以上'
        },
        {
          value: 113,
          label: '650-700分'
        },
        {
          value: 112,
          label: '600-650分'
        },
        {
          value: 110,
          label: '600分以下'
        }
      ],
      creditOptions: [
        {
          label: '无逾期',
          value: false,
          key: 'isOverdue'
        },
        {
          label: '有逾期',
          value: true,
          key: 'isOverdue'
        }
      ],
      agreementList: [],
      agreementIndex: 0,
      scrollIntoView: '',
      invite: '',
      smsId: '',
      productList: [],
      showPage: false
    }
  },

  async onLoad({ param, i, s }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (i && s) {
      uni.setStorageSync('invite', i)
      this.invite = i
      this.smsId = s
    }

    this.fetchProtocol()
  },

  onReady() {
    if (this.invite && this.smsId) {
      this.fetchChannelAndTemplate()
    }
  },

  methods: {
    async getTemplateConfig() {
      const res = await getTemplateConfig({
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        this.$u.vuex('vuex_theme', res.data.theme)
        this.$u.vuex('vuex_templateConfig', res.data)
      } else {
        this.$u.vuex('vuex_theme', '')
        this.$u.vuex('vuex_templateConfig', {})
      }
    },

    async fetchChannelAndTemplate() {
      this.$refs.loading.open()
      const res = await fetchChannelAndTemplate({
        invite: this.invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        reportUV({
          channelId: this.form.channelId
        })

        this.fetchUser()
        this.getTemplateConfig()
      }
    },

    async fetchUser() {
      const res = await verifySmsIdV7({
        smsId: this.smsId,
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        setFlowNodes(res.data.process.length ? res.data.process : [])
        this.form.consumerId = res.data.consumerId
        this.form.directMatchFlag = res.data.directMatchFlag

        this.$u.vuex('vuex_consumerId', this.form.consumerId)

        if (this.form.directMatchFlag == 1) {
          this.navigateToNextFlow()
        } else if (this.form.directMatchFlag == 2) {
          this.showPage = true
          this.$refs.loading.close()
        }
      }
    },

    validateSesameScore() {
      if (!this.form.sesameScoreIndex && typeof this.form.sesameScoreIndex !== 'number') {
        uni.showToast({
          title: '请选择芝麻分',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateCredit() {
      if (!this.form.creditIndex && typeof this.form.creditIndex !== 'number') {
        uni.showToast({
          title: '请选择信用情况',
          icon: 'none'
        })

        return false
      }

      return true
    },

    validateQualificationForm() {
      return this.validateSesameScore() && this.validateCredit()
    },

    setActiveForm(formName) {
      this.activeForm = formName
    },

    clickSesameScore(index) {
      this.form.sesameScoreIndex = index

      this.activeFormItem = 'credit'
    },

    async fetchProtocol() {
      const agreementListRes = await getProtocolList({
        protocolSig: this.vuex_templateConfig.agreementKeyAuth
      })
      this.agreementList = agreementListRes.data || []
      this.agreementList.forEach((agreement) => {
        getProtocolDetail({ protocolId: agreement.protocolId }).then((res) => {
          agreement.content = formatRichText(res.data.content, {})
        })
      })
    },

    clickCredit(index) {
      this.form.creditIndex = index
      this.form.isOverdue = this.creditOptions[index].value
    },

    async clickSubmit() {
      throttle(this.submitHandler)
    },

    async submitHandler() {
      if (!this.validateQualificationForm()) {
        return
      }

      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    getUserParams() {
      const params = {}
      params.smsId = this.smsId
      params.channelId = this.form.channelId
      params.consumerId = this.form.consumerId
      params.sesameId = this.sesameScoreOptions[this.form.sesameScoreIndex].value
      params.isOverdue = this.form.isOverdue

      return params
    },

    clickAgreeAndContinue() {
      throttle(async () => {
        if (!this.validateQualificationForm()) {
          return
        }

        const res = await consumerAssetsUpdate(this.getUserParams())
        if (res.code == 200) {
          this.form.consumerId = res.data
          this.$u.vuex('vuex_consumerId', this.form.consumerId)
        }

        this.$refs.loading.open()
        this.navigateToNextFlow()
      })
    },

    async navigateToNextFlow() {
      const routeMap = {
        offline: '/extreme/v109/apply/index',
        wechat: '/extreme/v109/qw/index',
        overloan: '/extreme/v109/applyOther/index',
        end: '/extreme/v109/download/index'
      }

      const customFetch = {
        wechat: getOnlineLowProduct,
        overloan: getOnlineLowProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )

      if (nextFlow.flow === 'halfapi') {
        this.$refs.loading.close()
        window.location.href = nextFlow.url
        return
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    clickAgreement(item, index) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
      this.$nextTick(() => {
        this.toggleAgreement(index)
      })
    },

    toggleAgreement(index) {
      this.agreementIndex = index
      this.$refs.agreementContent.$el.scrollTop = 0
    }
  }
}
</script>

<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <template v-if="showPage">
        <scroll-view
          class="page-content"
          scroll-y="true"
          :scroll-into-view="scrollIntoView"
          scroll-with-animation="true"
          @scrolltolower="scrollIntoView = ''"
        >
          <Header />

          <view class="header">
            <view class="header-title">
              <view class="header-title-text">还差一步，即可获取额度</view>
              <view class="header-title-subtext">最高可借200,000元</view>
            </view>
            <view class="tag">
              <view class="tag-number">1000</view>
              <view class="tag-gap-text">人</view>
              已放款
            </view>
          </view>

          <view
            class="qualification card"
            :class="{ activeForm: activeForm === 'qualification' }"
            @click="setActiveForm('qualification')"
          >
            <view class="card-header">
              <view class="card-header-label">
                <view class="card-header-icon"></view>
                <view>资质信息</view>
              </view>
              <!-- <uni-icons :type="activeForm === 'qualification' ? 'top' : 'bottom'" size="40rpx" color="#999"></uni-icons> -->
            </view>

            <view class="qualification-form" v-if="activeForm === 'qualification'">
              <view
                class="qualification-form-item form-item"
                @click="activeFormItem = 'sesameScore'"
              >
                <view class="form-item-label">芝麻分</view>
                <view class="form-item-value">
                  <input
                    disabled
                    style="pointer-events: none"
                    :value="
                      sesameScoreOptions[form.sesameScoreIndex]
                        ? sesameScoreOptions[form.sesameScoreIndex].label
                        : ''
                    "
                    placeholder="请选择芝麻分"
                    placeholder-class="placeholder"
                  />
                  <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
                </view>
              </view>

              <view class="form-item-line"></view>

              <view class="radio-group" v-if="activeFormItem === 'sesameScore'">
                <view
                  class="radio"
                  v-for="(item, index) in sesameScoreOptions"
                  :key="index"
                  :class="{ selected: index === form.sesameScoreIndex }"
                  @click="clickSesameScore(index)"
                >
                  {{ item.label }}

                  <view v-if="index === form.sesameScoreIndex" class="radio-selected-icon"></view>
                </view>
              </view>

              <view class="qualification-form-item form-item" @click="activeFormItem = 'credit'">
                <view class="form-item-label">信用情况</view>
                <view class="form-item-value">
                  <input
                    disabled
                    style="pointer-events: none"
                    :value="
                      creditOptions[form.creditIndex] ? creditOptions[form.creditIndex].label : ''
                    "
                    placeholder="请选择信用情况"
                    placeholder-class="placeholder"
                  />
                  <uni-icons type="right" size="40rpx" color="#999"></uni-icons>
                </view>
              </view>

              <!-- <view class="form-item-line"></view> -->

              <view class="radio-group" v-if="activeFormItem === 'credit'">
                <view
                  class="radio"
                  v-for="(item, index) in creditOptions"
                  :key="index"
                  :class="{ selected: index === form.creditIndex }"
                  @click="clickCredit(index)"
                >
                  {{ item.label }}

                  <view v-if="index === form.creditIndex" class="radio-selected-icon"></view>
                </view>
              </view>
            </view>
          </view>

          <Declaration />
        </scroll-view>
        <view class="page-footer">
          <view class="agreement">
            <view class="agreement-text">
              我已阅读并同意
              <text
                class="name"
                v-for="(item, index) in agreementList"
                :key="item.protocolId"
                @click="clickAgreement(item, index)"
              >
                《{{ item.name }}》
              </text>
            </view>
          </view>
          <view class="submit-btn" @click="clickSubmit">提交申请</view>
        </view>

        <uni-popup
          background-color="#fff"
          ref="agreementPopup"
          type="bottom"
          border-radius="32rpx 32rpx 0 0"
        >
          <view class="agreement-popup">
            <view class="agreement-popup-bg"></view>

            <view class="agreement-popup-header">
              <view class="agreement-popup-title">请勿接听任何境外电话</view>
              <view class="agreement-popup-desc">
                任何放款前以缴纳
                <text class="highlight">保证金、利息等</text>
                名义的行为均为
                <text class="highlight">诈骗</text>
                ，任何要求您提供
                <text class="highlight">银行卡密码、验证码等</text>
                个人信息均为
                <text class="highlight">诈骗</text>
                。
              </view>
            </view>

            <view class="agreement-popup-body" v-if="agreementList.length > 0">
              <view class="agreement-name-container">
                <view
                  class="agreement-name"
                  v-for="(item, index) in agreementList"
                  :key="index"
                  @click="toggleAgreement(index)"
                  :class="{ selected: index === agreementIndex }"
                >
                  <view>{{ item.name }}</view>
                  <view
                    v-if="index !== agreementList.length - 1"
                    class="agreement-name-line"
                  ></view>
                </view>
              </view>
              <view class="agreement-content" ref="agreementContent">
                <div v-html="agreementList[agreementIndex].content"></div>
              </view>
            </view>

            <view class="agreement-agree-container">
              <view class="agreement-agree-btn" @click="clickAgreeAndContinue">
                同意协议，继续申请
              </view>
              <view class="agreement-agree-tips"
                >若当前暂无匹配机构，请允许稍后持续为您匹配合适机构</view
              >
            </view>
          </view>
        </uni-popup>
      </template>
      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '@/theme/v109/export/auth.scss';
</style>
