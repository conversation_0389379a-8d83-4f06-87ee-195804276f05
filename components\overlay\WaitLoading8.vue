<script>
export default {
  name: 'FullScreenMatchLoading',
  data() {
    return {
      progress: 0,
      progressTimer: null,
      // 默认 10 秒
      duration: 10 * 1000,
      // 进度条完成时，是否自动关闭
      autoClose: false
    }
  },

  methods: {
    open(duration, options = {}) {
      this.duration = duration || this.duration // 如果没有指定时间，使用默认值
      this.$refs.popup.open()
      this.startProgressAnimation()
    },
    close() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
      this.$refs.popup.close()
    },
    startProgressAnimation() {
      this.progress = 0
      const interval = this.duration / 100 // 计算每次增加进度的时间间隔
      this.progressTimer = setInterval(() => {
        if (this.progress < 100) {
          this.progress++
        } else {
          clearInterval(this.progressTimer)
          this.progressTimer = null
          if (this.autoClose) {
            this.close()
          }
        }
      }, interval)
    }
  },

  beforeDestroy() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  }
}
</script>

<template>
  <uni-popup ref="popup" type="center" :mask-click="false" mask-background-color="rgba(0,0,0,0.75)">
    <view class="loading-container">
      <view class="image-container">
        <image
          class="inner-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/ea4596dbb67f4573bc401fff3c0e4716.png"
        />
        <image
          class="outer-image rotating"
          src="https://cdn.oss-unos.hmctec.cn/common/path/c2b7229841834f5d9fae000c2b7047b7.png"
        />
      </view>
      <view class="text">信息安全保护中</view>
      <view class="subtext">正在为您匹配最佳产品，请耐心等待...</view>
      <view class="progress">({{ progress }}/100)</view>
    </view>
  </uni-popup>
</template>

<style scoped lang="scss">
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.image-container {
  position: relative;
  width: 330rpx;
  height: 330rpx;
}

.inner-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 145rpx;
  height: 145rpx;
}

.outer-image {
  position: absolute;
  width: 330rpx;
  height: 330rpx;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.text {
  margin-top: 10rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  line-height: 42rpx;
  text-align: center;
}

.subtext {
  margin-top: 10rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 42rpx;
  text-align: center;
}

.progress {
  font-weight: 400;
  font-size: 24rpx;
  color: #ffffff;
  line-height: 42rpx;
  text-align: center;
}
</style>
