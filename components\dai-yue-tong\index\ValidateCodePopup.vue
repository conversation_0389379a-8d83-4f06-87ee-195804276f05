<template>
  <uni-popup
    type="bottom"
    ref="popup"
    :is-mask-click="false"
    border-radius="16rpx 16rpx 0 0"
    background-color="#FFFFFF"
  >
    <view class="validate-code-popup">
      <uni-icons type="close" size="26" class="close-icon" color="#999" @click="close"></uni-icons>
      <view class="validate-code-title">短信验证</view>
      <view class="validate-code-text">
        为保障您的信息安全，该手机号需短信验证，验证码已发送至{{ formattedPhone }}
      </view>
      <view class="validate-code-input-wrapper">
        <input
          type="text"
          class="validate-code-input"
          maxlength="6"
          :value="validateCode"
          @input="onInputCode"
        />
        <view
          class="validate-code-input-btn"
          @click="$emit('resend-code')"
          :style="{ opacity: countDown > 0 ? '0.5' : '1' }"
        >
          {{ countDown > 0 ? `${countDown}s` : '重新获取' }}
        </view>
      </view>
      <view
        class="validate-code-submit-btn"
        :class="{ disabled: !validateCode }"
        @click="$emit('submit-code')"
        >提交申请</view
      >
    </view>
  </uni-popup>
</template>

<script>
export default {
  props: {
    phoneNumber: {
      type: String,
      default: ''
    },
    validateCode: {
      type: String,
      default: ''
    },
    countDown: {
      type: Number,
      default: 0
    }
  },
  computed: {
    formattedPhone() {
      if (!this.phoneNumber || this.phoneNumber.length !== 11) return ''
      return this.phoneNumber.substring(0, 3) + '****' + this.phoneNumber.substring(7)
    }
  },
  methods: {
    open() {
      this.$refs.popup.open()
    },
    close() {
      this.$refs.popup.close()
    },
    onInputCode(e) {
      this.$emit('update:validate-code', e.target.value)
    }
  }
}
</script>

<style lang="scss" scoped>
.validate-code-popup {
  position: relative;
  padding: 32rpx 60rpx;

  .close-icon {
    position: absolute;
    top: 15rpx;
    right: 15rpx;
  }

  // 验证码弹窗样式
  .validate-code-title {
    font-weight: 400;
    font-size: 32rpx;
    color: #1a1a1a;
    line-height: 46rpx;
    text-align: center;
  }

  .validate-code-text {
    margin-top: 24rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #4d4d4d;
    line-height: 34rpx;
  }

  .validate-code-input-wrapper {
    margin-top: 32rpx;
    margin-bottom: 24rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;

    .validate-code-input {
      width: 440rpx;
      height: 76rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 2rpx solid #d5d5d5;
      font-weight: 400;
      font-size: 28rpx;
      line-height: 34rpx;
      padding: 20rpx 28rpx;
    }

    .validate-code-input-btn {
      width: 168rpx;
      height: 76rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 2rpx solid #44bfbd;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 28rpx;
      color: #40bdb7;
      line-height: 34rpx;
    }
  }

  .validate-code-submit-btn {
    width: 638rpx;
    height: 100rpx;
    background: linear-gradient(179deg, #40bdb7 9%, #49c2c4 30%);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #ffffff;
    line-height: 64rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    &.disabled {
      opacity: 0.4;
    }
  }
}
</style>
