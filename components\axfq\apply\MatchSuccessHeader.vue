<template>
  <view class="match-success">
    恭喜您，资方匹配
    <text class="success-text">成功</text>
  </view>
</template>

<script>
export default {
  name: 'MatchSuccessHeader'
}
</script>

<style scoped lang="scss">
$color-primary: #2737A8;

.match-success {
  padding: 27rpx 0 10rpx;
  font-weight: 700;
  font-size: 42rpx;
  color: #333333;
  line-height: 63rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 14rpx;

  &::before {
    display: block;
    content: '';
    width: 48rpx;
    height: 20rpx;
    background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/e002e01342884c96a29e95e21cf7b7d1.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &::after {
    display: block;
    content: '';
    width: 48rpx;
    height: 20rpx;
    background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/45dc42e09e6d4aa2890a5b9248e8b4bd.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .success-text {
    color: $color-primary;
  }
}
</style>
