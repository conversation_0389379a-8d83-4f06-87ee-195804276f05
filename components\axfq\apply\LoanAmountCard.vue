<template>
  <view class="loan-card">
    <view class="loan-header">最高可借(元）</view>
    <view class="quota-info">
      仅剩
      <text class="quota-number">{{ remainingQuota }}</text>
      个名额
    </view>

    <view class="amount-input">
      <view class="currency-symbol">￥</view>
      <input
        class="amount-field"
        type="text"
        :value="displayAmount"
        @blur="handleAmountBlur"
        @focus="handleAmountFocus"
        ref="amountInput"
      />
      <view class="amount-tip">(金额可修改)</view>
      <image
        class="close-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/960496971f6147ce9b11ce149a08e7b6.png"
        @click="handleEditAmount"
      />
    </view>

    <view class="features">
      <view class="feature-item" v-for="feature in features" :key="feature.text">
        <image
          class="check-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/c1be389cb21d4bb7b0afdcde82824724.png"
        />
        <text class="feature-text">{{ feature.text }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoanAmountCard',

  props: {
    // 当前金额
    amount: {
      type: [String, Number],
      default: '50000'
    },
    // 剩余名额
    remainingQuota: {
      type: Number,
      default: 8
    }
  },

  data() {
    return {
      displayAmount: this.amount,
      // 固定的特性列表
      features: [
        { text: '参考年利率12%起' },
        { text: '随借随还' },
        { text: '息费透明' }
      ]
    }
  },

  watch: {
    amount(newVal) {
      this.displayAmount = newVal
    }
  },

  methods: {
    handleAmountBlur(e) {
      this.$emit('amount-blur', e.detail.value)
    },

    handleAmountFocus(e) {
      this.$emit('amount-focus', e.detail.value)
    },

    handleEditAmount() {
      this.$emit('edit-amount')
    }
  }
}
</script>

<style scoped lang="scss">
.loan-card {
  margin: 30rpx;
  padding: 20rpx 20rpx 50rpx;
  background: #ffffff;
  border-radius: 8rpx;
  position: relative;

  .loan-header {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 35rpx;
  }

  .quota-info {
    position: absolute;
    top: 23rpx;
    right: 0;
    padding: 10rpx 5rpx 10rpx 10rpx;
    background: linear-gradient(90deg, #fa7d23 0%, #ffab45 100%);
    border-radius: 24rpx 0rpx 0rpx 24rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 19rpx;

    .quota-number {
      color: #f00000;
    }
  }

  .amount-input {
    padding: 30rpx 0;
    display: flex;
    align-items: flex-end;
    border-bottom: 1rpx solid #cdcdcd;

    .currency-symbol {
      font-weight: 700;
      font-size: 48rpx;
      color: #333333;
    }

    .amount-field {
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      flex: 1;
      height: 80rpx;
      min-height: 80rpx;
    }

    .amount-tip {
      font-weight: normal;
      font-size: 24rpx;
      color: #999999;
      flex-shrink: 0;
      line-height: 30rpx;
    }

    .close-icon {
      margin-left: 15rpx;
      flex-shrink: 0;
      width: 30rpx;
      height: 30rpx;
    }
  }

  .features {
    padding-top: 30rpx;
    display: flex;
    align-items: center;
    gap: 34rpx;

    .feature-item {
      display: flex;
      align-items: center;
      gap: 6rpx;

      .check-icon {
        width: 26rpx;
        height: 26rpx;
      }

      .feature-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #798087;
        line-height: 35rpx;
      }
    }
  }
}
</style>
