<template>
  <view class="container">
    <uni-nav-bar
      title=""
      :border="false"
      left-icon="back"
      left-text="返回"
      @clickLeft="handleBack"
    />
    <view class="cover" v-if="show">
      <uni-icons type="info-filled" size="28" color="#FFFFFF"></uni-icons>
      <text>当前为第三方网页，请注意核实信息谨防上当受骗</text>
    </view>
    <web-view :src="url" :fullscreen="false"></web-view>
    <LoadingPopup ref="loading" />
    <RetainPopup ref="retainPopup" @continue="continueApply" @exit="exitApply" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import LoadingPopup from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import RetainPopup from '@/components/dai-yue-tong/index/retain-popup.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { backApplyOnline } from '@/apis/common-2'

export default {
  name: 'overloanWebview',

  components: {
    LoadingPopup,
    RetainPopup
  },

  data() {
    return {
      show: true,
      url: '',
      form: {},
      lockBack: null,
      urlUpdateTime: 0,
      urlUpdateTimeout: 5000 // 设置弹窗显示的时间窗口为5秒
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (this.vuex_overloanWebview.url) {
        this.url = this.vuex_overloanWebview.url
        this.urlUpdateTime = Date.now()
      }
    }

    setTimeout(() => {
      this.show = false
    }, 3000)

    // 初始化锁定返回功能
    this.initLockBack()
  },

  onUnload() {
    // 页面卸载时解锁
    if (this.lockBack) {
      this.lockBack.unLock()
    }
  },

  watch: {
    url(newVal) {
      if (newVal) {
        this.urlUpdateTime = Date.now()
      }
    }
  },

  methods: {
    // 初始化锁定返回功能
    initLockBack() {
      this.lockBack = new LockNativeBack({
        onPopState: this.handleBack
      })
      // 锁定返回按钮
      this.lockBack.lock()
    },

    async handleBack() {
      // 判断是否在URL更新后的5秒内触发返回
      if (Date.now() - this.urlUpdateTime < this.urlUpdateTimeout) {
        this.$refs.retainPopup.open()
        return
      }

      // 超过5秒则执行原有逻辑
      this.processBackLogic()
    },

    // 继续申请，关闭弹窗
    continueApply() {
      // 弹窗组件内部会自动关闭
    },

    // 确认离开，执行原有返回逻辑
    exitApply() {
      this.processBackLogic()
    },

    // 原有返回逻辑
    async processBackLogic() {
      this.$refs.loading.open()
      const response = await backApplyOnline({ consumerId: this.form.consumerId })
      this.$refs.loading.close()
      if (response.code == 200) {
        this.url = response.data
        this.$u.vuex('vuex_overloanWebview.url', response.data)
      } else {
        this.navigateToNextFlow()
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v133/apply/index',
        wechat: '/extreme/v133/qw/index',
        overloan: '/extreme/v133/applyOther/index',
        end: '/extreme/v133/download/index',
        halfapi: '/extreme/v133/webviewDown/index',
        wechat_official_account: '/extreme/v133/wechatOfficialAccount/index'
      }

      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      this.$refs.loading.close()

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.container {
  .cover {
    width: 100vw;
    position: fixed;
    z-index: 999;
    padding: 16rpx 32rpx;
    left: 0;
    top: 44px;
    background: linear-gradient(90deg, #44c5ad 0%, #42d794 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;

    text {
      font-size: 26rpx;
      color: #ffffff;
      font-weight: 400;
      line-height: 38rpx;
    }
  }
}

::v-deep web-view {
  iframe {
    width: 100vw !important;
    height: calc(100vh - 44px) !important;
    border: none;
  }
}
</style>
