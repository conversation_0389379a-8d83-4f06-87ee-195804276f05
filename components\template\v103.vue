<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <view class="header-container">
        <Header />
        <view class="header-img"></view>
      </view>

      <view class="feature-img"></view>

      <view class="input-container">
        <input
          class="input-container-input"
          type="text"
          placeholder="请输入手机号"
          v-model="form.phone"
          maxlength="11"
        />
        <view class="input-container-btn" @click="clickSubmit">立即领取</view>
      </view>

      <view class="feature-img-bottom"></view>

      <Declaration />

      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>
<script>
import { fetchFlow, verifySmsIdV7, verifySmsIdV7V2 } from '@/apis/common-2'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { fetchChannelAndTemplate, getTemplateConfig, reportUV } from '@/apis/common'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import Header from '@/components/header/header-custom.vue'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'

export default {
  name: 'v103',
  components: {
    FullScreenMatchLoading,
    Header,
    Declaration
  },

  data() {
    return {
      form: {
        templateVersion: 'v103',
        consumerId: '',
        /**
         * directMatchFlag
         * 1 有用户资质信息，无需填写资料，直接匹配企微、半流程和贷超
         * 2 无用户资质信息，填写资料匹配企微和贷超
         */
        directMatchFlag: '',
        channelId: '',
        phone: ''
      }
    }
  },

  mounted() {
    // 从路由参数获取数据
    const query = this.$route.query

    this.$u.vuex('vuex_invite', query.i)
    this.$u.vuex('vuex_smsId', query.s)

    if (query.param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(query.param)))
    }

    // 如果有邀请码和短信ID，则获取渠道和模板信息
    if (this.vuex_invite && this.vuex_smsId) {
      this.fetchChannelAndTemplate()
    }
  },

  methods: {
    async getTemplateConfig() {
      const res = await getTemplateConfig({
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        this.$u.vuex('vuex_theme', res.data.theme)
        this.$u.vuex('vuex_templateConfig', res.data)
      } else {
        this.$u.vuex('vuex_theme', '')
        this.$u.vuex('vuex_templateConfig', {})
      }
    },

    async fetchChannelAndTemplate() {
      const res = await fetchChannelAndTemplate({
        invite: this.vuex_invite
      })

      if (res.code == 200) {
        this.form.channelId = res.data.channelId

        reportUV({
          channelId: this.form.channelId
        })
        await this.getTemplateConfig()
        await this.fetchFlow()
        this.fetchUser()
      }
    },

    async fetchFlow() {
      const res = await fetchFlow({
        channelId: this.form.channelId
      })
      setFlowNodes(res.data.length ? res.data : null)
    },

    async fetchUser() {
      const isActivated = this.vuex_templateConfig.isActivated
      const apiFun = isActivated ? verifySmsIdV7V2 : verifySmsIdV7
      const res = await apiFun({
        smsId: this.vuex_smsId,
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        this.form.consumerId = res.data.consumerId
        this.$u.vuex('vuex_consumerId', res.data.consumerId)
        this.form.directMatchFlag = res.data.directMatchFlag
      }
    },

    clickSubmit() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      this.$u.vuex('vuex_phone', this.form.phone)

      if (this.form.directMatchFlag == 1) {
        // 有用户资质信息，无需填写资料，直接匹配
        this.navigateToNextFlow()
      } else if (this.form.directMatchFlag == 2) {
        // 无用户资质信息，需要跳转到填写资料页面
        this.navigateToAuthPage()
      }
    },

    navigateToAuthPage() {
      // 跳转到填写资料页面
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v103/auth/index?param=${urlParamString}`
      })
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v103/apply/index',
        wechat: '/extreme/v103/qw/index',
        overloan: '/extreme/v103/applyOther/index',
        end: '/extreme/v103/download/index',
        wechat_official_account: '/extreme/v103/wechatOfficialAccount/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        window.location.href = nextFlow.url
        return
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/theme/v103/export/index.scss';
</style>
