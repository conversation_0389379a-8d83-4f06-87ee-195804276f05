<template>
  <view class="loan-amount-section">
    <view class="loan-title">最高可借20,0000元</view>
    <view class="loan-input-wrapper">
      <view class="currency-symbol">￥</view>
      <input
        type="text"
        maxlength="6"
        :value="loanAmount"
        @focus="$emit('input-focus')"
        @blur="$emit('input-blur')"
        @input="onInput"
        class="loan-input"
      />
      <view class="borrow-all-btn" @click="$emit('set-amount', 200000)">全部借出</view>
    </view>
    <view class="quick-amount-btns">
      <view class="quick-amount-btn" @click="$emit('set-amount', 150000)">借150000</view>
      <view class="quick-amount-btn" @click="$emit('set-amount', 100000)">借100000</view>
      <view class="quick-amount-btn" @click="$emit('set-amount', 50000)">借50000</view>
    </view>
    <view class="interest-info">
      <!-- icon -->
      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/657db77bcd5445d687cdcfb1a348a7df.png"
        class="interest-icon"
      />
      <view class="interest-text">
        年利率低至
        <!-- 高亮 -->
        <view class="interest-highlight">7.2%</view>
        （<text class="line-through">9%</text>）起
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    loanAmount: {
      type: String,
      default: '48,000'
    }
  },
  methods: {
    onInput(e) {
      // 只保留数字字符
      const numericValue = e.target.value.replace(/\D/g, '')
      this.$emit('update:loan-amount', numericValue)
    }
  }
}
</script>

<style lang="scss" scoped>
.loan-amount-section {
  // 贷款金额区域样式
  width: 686rpx;
  height: 336rpx;
  background: #ffffff;
  border-radius: 20rpx;
  margin: 96rpx auto 0;
  padding: 24rpx;

  .loan-title {
    // 贷款标题样式
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 34rpx;
  }

  .loan-input-wrapper {
    // 贷款输入框包装器样式
    display: flex;
    margin-top: 10rpx;

    .currency-symbol {
      // 货币符号样式
      color: #1a1a1a;
      font-weight: 500;
      font-size: 36rpx;
      line-height: 48rpx;
      margin-top: 40rpx;
    }

    .loan-input {
      // 贷款输入框样式
      font-weight: 500;
      color: #1a1a1a;
      font-size: 72rpx;
    }

    .borrow-all-btn {
      // 全部借出按钮样式
      margin-top: 44rpx;
      flex-shrink: 0;
      font-weight: 400;
      font-size: 28rpx;
      color: #3fc1bc;
      line-height: 34rpx;
    }
  }

  .quick-amount-btns {
    // 快速金额按钮组样式
    margin-top: 20rpx;
    display: flex;
    gap: 24rpx;

    .quick-amount-btn {
      // 快速金额按钮样式
      padding: 0 12rpx;
      height: 46rpx;
      background: #ffffff;
      border-radius: 4rpx 4rpx 4rpx 4rpx;
      border: 1rpx solid #3fc1bc;
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 28rpx;
      color: #3fc1bc;
      line-height: 34rpx;
    }
  }

  .interest-info {
    // 利率信息样式
    margin-top: 24rpx;
    padding-top: 24rpx;
    display: flex;
    align-items: center;
    gap: 10rpx;
    border-top: 2rpx dashed #d8d8d8;

    .interest-icon {
      // 利率图标样式
      width: 98rpx;
      height: 34rpx;
    }

    .interest-text {
      // 利率文本样式
      display: flex;

      font-weight: 400;
      font-size: 28rpx;
      color: #4d4d4d;
      // line-height: 34rpx;

      .interest-highlight {
        // 利率高亮样式
        color: #f04836;
      }

      .line-through {
        // 划线样式
        text-decoration: line-through;
      }
    }
  }
}
</style>
