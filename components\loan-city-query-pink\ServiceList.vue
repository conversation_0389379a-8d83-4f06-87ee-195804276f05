<template>
  <view class="service-list-wrapper">
    <view class="result-title">您的申请预计由以下服务方提供</view>
    <view class="service-list">
      <view class="service-item" v-for="(item, index) in institutionList" :key="index">
        {{ item }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    institutionList: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style lang="scss" scoped>
.service-list-wrapper {
  .result-title {
    padding: 0 55rpx;
    margin-bottom: 20rpx;
    font-weight: 700;
    font-size: 40rpx;
    color: #333333;
    line-height: 58rpx;
  }

  .service-list {
    padding: 0 55rpx;

    .service-item {
      padding: 30rpx 0;
      font-weight: 400;
      font-size: 32rpx;
      color: #666666;
      line-height: 46rpx;
      border-bottom: 2rpx solid #ebebeb;

      &:last-child {
        border-bottom: none; // 移除最后一个元素的底部边框
      }
    }
  }
}
</style> 