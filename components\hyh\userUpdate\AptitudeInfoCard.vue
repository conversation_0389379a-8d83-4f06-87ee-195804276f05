<template>
  <view
    class="card-container"
    :class="[isExpanded ? 'expand' : 'collapse']"
    @click="requestExpand"
  >
    <view class="card-header">
      <view class="card-title">{{ title }}</view>
      <view class="card-title-img-container">
        <text class="card-title-img-text" :class="{ ok: certificationStatusText === '已认证' }">{{ certificationStatusText }}</text>
        <image
          class="card-title-img"
          src="https://cdn.oss-unos.hmctec.cn/common/path/0af0fdc0ff5c4660962c5d421a36b778.png"
          mode="aspectFit"
        />
      </view>
    </view>

    <view class="card-body">
      <view
        class="card-body-item"
        :class="{ select: currentValue === item.value }"
        v-for="item in options"
        :key="item.value"
        @click.stop="clickItem(item)"
      >
        {{ item.label }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AptitudeInfoCard',
  props: {
    title: {
      type: String,
      required: true
    },
    currentValue: {
      // type: [String, Number, Boolean], // 允许多种类型，根据实际情况调整
      // default: ''
    },
    options: {
      type: Array,
      default: () => []
    },
    isExpanded: {
      type: Boolean,
      default: false
    },
    identifier: {
      type: String,
      required: true
    },
    certificationStatusText: {
      type: String,
      default: '未认证'
    }
  },
  methods: {
    requestExpand() {
      this.$emit('expand-request', this.identifier);
    },
    clickItem(item) {
      this.$emit('item-click', item, this.identifier);
    }
  }
}
</script>

<style scoped lang="scss">
.card-container {
  margin: 0 auto 32rpx;
  width: 692rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  border: 2rpx solid #ffffff;
  padding: 36rpx 33rpx 50rpx;

  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 27rpx;

    .card-title {
      font-weight: 500;
      font-size: 34rpx;
      color: #333333;
      line-height: 51rpx;
    }
    .card-title-img-container {
      display: flex;
      align-items: center;
      gap: 6rpx;

      .card-title-img-text {
        font-weight: 500;
        font-size: 28rpx;
        color: #999999;
        line-height: 42rpx;

        &.ok {
          color: #28c445;
        }
      }

      .card-title-img {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
  .card-body {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;

    .card-body-item {
      width: 300rpx;
      height: 92rpx;
      background: #f5f5f5;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      border: 1rpx solid #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;

      &.select {
        background: #eaf3ff;
        border: 1rpx solid #2381ff;
        color: #2381ff;
        background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/32dcd9c0b1fc48e4a319239d48810991.png);
        background-size: 34rpx 34rpx;
        background-repeat: no-repeat;
        background-position: right bottom;
      }
    }
  }

  &.expand {
    border-color: #2381ff;
  }

  &.collapse {
    padding: 25rpx 33rpx;

    .card-header {
      margin-bottom: 0;
    }

    .card-body {
      display: none;
    }

    .card-title-img {
      transform: rotate(-90deg);
    }
  }
}
</style>
