<template>
  <view class="container">
    <uni-nav-bar
      title=""
      :border="false"
      left-icon="back"
      left-text="返回"
      @clickLeft="handleBack"
    />
    <view class="cover" v-if="show">当前为第三方网页，请注意核实信息谨防上当受骗</view>
    <web-view :src="url" :fullscreen="false"></web-view>
    <FullScreenMatchLoading ref="loading" />
    <image
      class="float-icon"
      src="https://cdn.oss-unos.hmctec.cn/common/path/10b7fd42647d49a9b01ed16f2f60de07.png"
      @click="handleBack"
    />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import LockNativeBack from '@/utils/lock-native-back'
import routeMap from '../packages/routeMap'

export default {
  name: 'formLoginWebview',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      show: true,
      url: '',
      form: {},
      lockBack: null
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (this.vuex_formLoginWebview.url) {
        this.url = this.vuex_formLoginWebview.url
      }
    }

    setTimeout(() => {
      this.show = false
    }, 3000)

    // 初始化锁定返回功能
    this.initLockBack()
  },

  onUnload() {
    // 页面卸载时解锁
    if (this.lockBack) {
      this.lockBack.unLock()
    }
  },

  methods: {
    // 初始化锁定返回功能
    initLockBack() {
      this.lockBack = new LockNativeBack({
        onPopState: this.handleBack
      })
      // 锁定返回按钮
      this.lockBack.lock()
    },

    async handleBack() {
      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const customParams = {
        halfapi: {
          matchType: 1
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        {},
        customParams
      )

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    navigateToEnd() {
      const path = '/extreme/v101/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  .cover {
    width: 100vw;
    position: fixed;
    z-index: 999;
    padding: 14rpx 32rpx;
    left: 0;
    top: 44px;
    background: #ffedea;
    font-size: 24rpx;
    color: #ff5449;
    text-align: center;
  }

  .float-icon {
    position: fixed;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 80rpx;
    height: 80rpx;
    z-index: 999;
  }
}

::v-deep web-view {
  iframe {
    width: 100vw !important;
    height: calc(100vh - 44px) !important;
    border: none;
  }
}
</style>
