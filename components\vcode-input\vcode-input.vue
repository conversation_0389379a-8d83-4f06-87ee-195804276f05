<template>
	<view class="vcode-input-container" @tap="handleContainerTap">
		<view class="vcode-input-body">
			<view
				class="vcode-input-item"
				:class="getItemClass(index)"
				v-for="index in sum"
				:key="index"
			>
				<text class="vcode-input-text">{{ getDisplayValue(index - 1) }}</text>
				<view
					v-if="showCursor && currentIndex === (index - 1)"
					class="vcode-input-cursor"
				></view>
			</view>
		</view>
		<input
			ref="hiddenInput"
			class="vcode-hidden-input"
			type="tel"
			:value="inputValue"
			:maxlength="sum"
			:focus="isFocused"
			@input="handleInput"
			@focus="handleFocus"
			@blur="handleBlur"
			@keydown="handleKeydown"
			autocomplete="one-time-code"
		/>
	</view>
</template>

<script>
export default {
	name: 'VcodeInput',
	props: {
		sum: {
			type: Number,
			default: 6
		},
		isAutoComplete: {
			type: <PERSON>olean,
			default: true
		},
		isPassword: {
			type: Boolean,
			default: false
		}
	},
	data() {
		return {
			inputValue: '',
			isFocused: false,
			showCursor: false,
			cursorTimer: null,
			isCompleted: false // 是否已完成输入
		}
	},
	computed: {
		currentIndex() {
			return this.inputValue.length
		}
	},
	mounted() {
		this.$nextTick(() => {
			// 延迟聚焦，确保组件完全渲染
			setTimeout(() => {
				this.focusInput()
			}, 300)
		})
	},
	beforeDestroy() {
		this.clearCursorTimer()
	},
	methods: {
		// 获取输入框样式类
		getItemClass(index) {
			const classes = []
			const realIndex = index - 1 // 因为v-for从1开始，需要转换为0开始的索引

			if (this.inputValue.length > realIndex) {
				classes.push('vcode-input-filled')
			}
			if (this.isFocused && this.currentIndex === realIndex) {
				classes.push('vcode-input-active')
			}

			return classes
		},

		// 获取显示值
		getDisplayValue(index) {
			if (index >= this.inputValue.length) return ''

			if (this.isPassword) {
				return '●'
			}
			return this.inputValue[index] || ''
		},

		// 处理容器点击
		handleContainerTap() {
			// 如果已完成输入，则不允许聚焦
			if (this.isCompleted) {
				return
			}
			this.focusInput()
		},

		// 聚焦输入框
		focusInput() {
			// 如果已完成输入，则不允许聚焦
			if (this.isCompleted) {
				return
			}
			this.isFocused = true
			this.$nextTick(() => {
				if (this.$refs.hiddenInput) {
					this.$refs.hiddenInput.focus()
				}
			})
		},

		// 处理输入
		handleInput(e) {
			// 如果已完成输入，则不允许继续输入
			if (this.isCompleted) {
				return
			}

			let value = e.detail ? e.detail.value : e.target.value

			// 只允许数字输入
			value = value.replace(/[^\d]/g, '')

			// 限制长度
			if (value.length > this.sum) {
				value = value.slice(0, this.sum)
			}

			this.inputValue = value
			this.$emit('vcodeInput', value)

			// 自动完成检查
			if (this.isAutoComplete && value.length >= this.sum) {
				this.isCompleted = true // 标记为已完成
				this.isFocused = false
				this.stopCursor()
			}
		},

		// 处理聚焦
		handleFocus() {
			// 如果已完成输入，则不允许聚焦
			if (this.isCompleted) {
				this.$refs.hiddenInput.blur()
				return
			}
			this.isFocused = true
			this.startCursor()
		},

		// 处理失焦
		handleBlur() {
			this.isFocused = false
			this.stopCursor()
		},

		// 处理键盘事件
		handleKeydown(e) {
			// 如果已完成输入，则不允许删除
			if (this.isCompleted) {
				e.preventDefault()
				return
			}

			// 处理删除键
			if (e.keyCode === 8 || e.key === 'Backspace') {
				if (this.inputValue.length > 0) {
					this.inputValue = this.inputValue.slice(0, -1)
					this.$emit('vcodeInput', this.inputValue)
				}
			}
		},

		// 开始光标闪烁
		startCursor() {
			this.showCursor = true
			this.clearCursorTimer()
			this.cursorTimer = setInterval(() => {
				this.showCursor = !this.showCursor
			}, 500)
		},

		// 停止光标闪烁
		stopCursor() {
			this.showCursor = false
			this.clearCursorTimer()
		},

		// 清除光标定时器
		clearCursorTimer() {
			if (this.cursorTimer) {
				clearInterval(this.cursorTimer)
				this.cursorTimer = null
			}
		},

		// 清除验证码
		clearVcode() {
			this.inputValue = ''
			this.isCompleted = false // 重置完成状态，允许重新输入
			this.$emit('vcodeInput', '')
			this.focusInput()
		}
	}
}
</script>

<style lang="scss" scoped>
.vcode-input-container {
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	user-select: none;
	-webkit-user-select: none;
	-webkit-touch-callout: none;
}

.vcode-input-body {
	display: flex;
	justify-content: center;
	align-items: center;
	gap: 24rpx;
}

.vcode-input-item {
	position: relative;
	width: 76rpx;
	height: 76rpx;
	background: #F4F4F4;
	border-radius: 8rpx;
	border: 2rpx solid #DADADA;
	display: flex;
	justify-content: center;
	align-items: center;
	transition: all 0.2s ease;
	box-sizing: border-box;

	&.vcode-input-filled {
		background: #FFF;
		border-color: #F52140;
		transform: scale(1.02);
	}

	&.vcode-input-active {
		background: #FFF;
		border-color: #F52140;
		box-shadow: 0 0 0 2rpx rgba(245, 33, 64, 0.2);
		transform: scale(1.05);
	}
}

.vcode-input-text {
	font-family: DIN, DIN-Bold, Arial, sans-serif;
	font-weight: 700;
	font-size: 42rpx;
	color: #F52140;
	line-height: 1;
	display: block;
}

.vcode-input-cursor {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 2rpx;
	height: 32rpx;
	background-color: #F52140;
	animation: cursor-blink 1s infinite;
}

@keyframes cursor-blink {
	0%, 50% {
		opacity: 1;
	}
	51%, 100% {
		opacity: 0;
	}
}

.vcode-hidden-input {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	opacity: 0;
	z-index: -1;
	pointer-events: none;
	font-size: 16px; /* 防止iOS缩放 */

	/* 移动端优化 */
	-webkit-user-select: text;
	-webkit-touch-callout: default;
	-webkit-appearance: none;
	border: none;
	outline: none;
	background: transparent;

	/* 确保在移动设备上正确显示键盘 */
	&:focus {
		opacity: 0;
	}
}

/* 移动端适配 */
@media screen and (max-width: 750px) {
	.vcode-input-item {
		width: 60rpx;
		height: 60rpx;
	}

	.vcode-input-text {
		font-size: 36rpx;
	}

	.vcode-input-cursor {
		height: 28rpx;
	}
}

/* 高分辨率屏幕适配 */
@media screen and (-webkit-min-device-pixel-ratio: 2) {
	.vcode-input-item {
		border-width: 1rpx;
	}
}
</style>
