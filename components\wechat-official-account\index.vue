<template>
  <view class="page-container">
    <view class="title"></view>
    <view class="qr-code-container">
      <view v-if="qrCodeImageUrl" class="qrcode-main">
        <qrcode-vue
          class="qrcode-code"
          :value="qrCodeImageUrl"
          level="H"
          :size="qrCodeSize"
        ></qrcode-vue>
        <view v-if="logoUrl" class="logo shadowed">
          <image :src="logoUrl" mode="aspectFit" />
        </view>
      </view>
      <view v-else class="loading-text">正在加载二维码...</view>
    </view>
    <view class="name-container" @click="clickCopy">
      <view class="name">{{ officialAccountName }}</view>
      <view class="copy-btn">点击复制</view>
    </view>
    <a class="follow-btn" :href="wechatMiniProgramLink" target="_blank">立即关注</a>
    <view class="steps"></view>
  </view>
</template>
<script>
import QrcodeVue from 'qrcode.vue'
import { getWechatMiniProgramLink, reportBurialPoint, getWechatPubUrl } from '@/apis/common'

export default {
  name: 'WechatOfficialAccount',
  components: {
    QrcodeVue
  },
  data() {
    return {
      wechatMiniProgramLink: '',
      qrCodeImageUrl: '',
      logoUrl: '',
      officialAccountName: '',
      form: {
        consumerId: ''
      }
    }
  },

  computed: {
    qrCodeSize() {
      // 将379rpx转换为px，供qrcode-vue组件使用
      return uni.upx2px(379)
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decodeUrlParam(param))
    }
  },

  created() {
    reportBurialPoint({
      scene: 'wechatOfficialAccount',
      type: 'show',
      channelId: this.vuex_channelId
    })
    this.getWechatMiniProgramLink()
    this.getWechatPubInfo()
  },

  methods: {
    async getWechatMiniProgramLink() {
      const res = await getWechatMiniProgramLink({
        // 环境版本 正式版为 "release"，体验版为"trial"，开发版为"develop"
        envVersion: 'release',
        // 页面路径
        path: '/pages/wxOfficialAccountQrCode/index'
      })

      const consumerId = this.vuex_consumerId || this.form.consumerId

      const queryParams = {
        channelId: this.vuex_channelId,
        url: 'https://shtf.zxcbs.cn/fullScreen/image/index?consumerId=' + consumerId
      }

      const query = Object.keys(queryParams)
        .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
        .join('&')

      this.wechatMiniProgramLink = `${res.data}?cq=${encodeURIComponent(query)}`
    },

    async getWechatPubInfo() {
      try {
        // 获取消费者ID，可以从路由参数或全局状态获取
        const consumerId = this.vuex_consumerId || ''
        
        if (!consumerId) {
          console.warn('缺少消费者ID')
          return
        }

        const res = await getWechatPubUrl(consumerId)
        
        if (res.code === 200 && res.data) {
          // 根据接口返回的数据结构更新二维码、logo和公众号名称
          if (res.data.url) {
            this.qrCodeImageUrl = res.data.url
          }
          if (res.data.logo) {
            this.logoUrl = res.data.logo
          }
          if (res.data.title) {
            this.officialAccountName = res.data.title
          }
        } else {
          console.warn('获取公众号信息失败:', res.msg || '获取失败')
        }
      } catch (error) {
        console.error('获取公众号信息接口调用失败:', error)
      }
    },

    clickCopy() {
      uni.setClipboardData({
        data: this.officialAccountName
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  padding-top: 60rpx;
  padding-bottom: 40rpx;
  position: relative;
  min-height: 100vh;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/edaf6f3e670f40f4a3d5649af15141e6.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.title {
  width: 576rpx;
  height: 70rpx;
  margin: 0 auto 0;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/a8e8d86742ca43c0b1d288d1da090d7c.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.qr-code-container {
  transform: translateY(-30rpx);
  width: 720rpx;
  height: 748rpx;
  margin: 0 auto;
  padding-top: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/4bb0c480398b43189912ad159eed7df7.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .qrcode-main {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .qrcode-code {
      width: 379rpx;
      height: 379rpx;
    }

    .logo {
      width: 80rpx;
      height: 80rpx;
      overflow: hidden;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: #fff;
      border: 4rpx solid #fff;
      border-radius: 12rpx;

      image {
        width: 100%;
        height: 100%;
        border-radius: 8rpx;
        overflow: hidden;
      }
    }

    .shadowed {
      box-shadow: 0px 0px 6rpx 6rpx rgba(0, 0, 0, 0.1);
    }
  }

  .loading-text {
    font-size: 32rpx;
    color: #666666;
    text-align: center;
  }
}

.name-container {
  margin: 0 auto;
  padding: 0 24rpx 0 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  width: 346rpx;
  height: 78rpx;
  border-radius: 12rpx;
  background: linear-gradient(90deg, #44c5ad 0%, #42d794 100%);

  .name {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    min-width: 182rpx;
    min-height: 60rpx;
    background: #ffffff;
    color: #232323;
    border-radius: 12rpx 12rpx 12rpx 12rpx;
    font-weight: 500;
    font-size: 32rpx;
    line-height: 48rpx;
  }

  .copy-btn {
    min-width: 112rpx;
    min-height: 42rpx;
    font-weight: 400;
    font-size: 28rpx;
    line-height: 42rpx;
    color: #ffffff;
  }
}

.follow-btn {
  display: block;
  text-decoration: none;
  margin: 35rpx auto 40rpx;
  width: 658rpx;
  height: 98rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  font-size: 40rpx;
  line-height: 58rpx;
  border-radius: 98rpx;
  animation: scale 1s infinite linear;
  background: #44c5ac;
  color: #ffffff;
}

.steps {
  margin: 0 auto;
  width: 674rpx;
  height: 518rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/d851aa0194d841f7b482f3b7f19d5314.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

// 缩放动画，放大-缩小-放大
@keyframes scale {
  0% {
    transform: scale(0.9);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(0.9);
  }
}
</style>
