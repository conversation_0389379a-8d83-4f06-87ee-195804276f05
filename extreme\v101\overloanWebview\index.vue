<template>
  <view class="container">
    <uni-nav-bar
      title=""
      :border="false"
      left-icon="back"
      left-text="返回"
      @clickLeft="handleBack"
    />
    <view class="cover" v-if="show">当前为第三方网页，请注意核实信息谨防上当受骗</view>
    <web-view :src="url" :fullscreen="false"></web-view>
    <FullScreenMatchLoading ref="loading" />

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="remindPopup"
      type="center"
      border-radius="56rpx 56rpx 56rpx 56rpx"
    >
      <view class="remind-popup">
        <view class="remind-popup-title">确定要放弃以下限时特权吗？</view>
        <view class="remind-popup-desc">额度仅限今日领取</view>
        <view class="remind-popup-feature">
          <view class="remind-popup-feature-item">
            <image
              class="remind-popup-feature-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/d2196ac9fdb94ed28f61479de3fe821c.png"
            ></image>
            <view class="remind-popup-feature-text">
              已匹配到产品的
              <text class="highlight">申请机会</text>
            </view>
          </view>
          <view class="remind-popup-feature-item">
            <image
              class="remind-popup-feature-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/d2196ac9fdb94ed28f61479de3fe821c.png"
            ></image>
            <view class="remind-popup-feature-text">
              初审已通过，确认额度
              <text class="highlight">立即领取</text>
            </view>
          </view>
        </view>
        <view class="remind-popup-tips"
          >信息加密储存仅用于资质申请，平台承诺不会泄露您的任何信息</view
        >
        <view class="remind-popup-confirm" @click="continueApply">继续申请</view>
        <view class="remind-popup-cancel" @click="exitApply">狠心离开</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { backApplyOnline } from '@/apis/common-2'
import routeMap from '../packages/routeMap'

export default {
  name: 'overloanWebview',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      show: true,
      url: '',
      form: {},
      lockBack: null,
      urlUpdateTime: 0,
      urlUpdateTimeout: 5000 // 设置弹窗显示的时间窗口为5秒
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (this.vuex_overloanWebview.url) {
        this.url = this.vuex_overloanWebview.url
        this.urlUpdateTime = Date.now()
      }
    }

    setTimeout(() => {
      this.show = false
    }, 3000)

    // 初始化锁定返回功能
    this.initLockBack()
  },

  onUnload() {
    // 页面卸载时解锁
    if (this.lockBack) {
      this.lockBack.unLock()
    }
  },

  watch: {
    url(newVal) {
      if (newVal) {
        this.urlUpdateTime = Date.now()
      }
    }
  },

  methods: {
    // 初始化锁定返回功能
    initLockBack() {
      this.lockBack = new LockNativeBack({
        onPopState: this.handleBack
      })
      // 锁定返回按钮
      this.lockBack.lock()
    },

    async handleBack() {
      // 判断是否在URL更新后的5秒内触发返回
      if (Date.now() - this.urlUpdateTime < this.urlUpdateTimeout) {
        this.$refs.remindPopup.open()
        return
      }

      // 超过5秒则执行原有逻辑
      this.processBackLogic()
    },

    // 继续申请，关闭弹窗
    continueApply() {
      this.$refs.remindPopup.close()
    },

    // 确认离开，执行原有返回逻辑
    exitApply() {
      this.$refs.remindPopup.close()
      this.processBackLogic()
    },

    // 原有返回逻辑
    async processBackLogic() {
      this.$refs.loading.open()
      const response = await backApplyOnline({ consumerId: this.form.consumerId })
      this.$refs.loading.close()
      if (response.code == 200) {
        this.url = response.data
        this.$u.vuex('vuex_overloanWebview.url', response.data)
      } else if (response.code == 500) {
        this.navigateToNextFlow()
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const customParams = {
        halfapi: {
          matchType: 1
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        {},
        customParams
      )

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    navigateToEnd() {
      const path = '/extreme/v101/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.container {
  .cover {
    width: 100vw;
    position: fixed;
    z-index: 999;
    padding: 14rpx 32rpx;
    left: 0;
    top: 44px;
    background: #ffedea;
    font-size: 24rpx;
    color: #ff5449;
    text-align: center;
  }
}

.remind-popup {
  width: 650rpx;
  padding: 88rpx 66rpx 72rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/47517249d74c409daf1ccfee58dfb4eb.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .remind-popup-title {
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-desc {
    margin-top: 14rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-feature {
    margin-top: 50rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;

    .remind-popup-feature-item {
      display: flex;
      align-items: center;
      gap: 5rpx;

      .remind-popup-feature-icon {
        width: 36rpx;
        height: 30rpx;
      }

      .remind-popup-feature-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #3d3d3d;
        line-height: 37rpx;
        letter-spacing: 2px;

        .highlight {
          color: $color-primary;
        }
      }
    }
  }

  .remind-popup-tips {
    margin-top: 43rpx;
    background: #fff7eb;
    font-weight: 400;
    font-size: 24rpx;
    color: #864508;
    line-height: 32rpx;
    padding: 16rpx 22rpx;
  }

  .remind-popup-confirm {
    margin-top: 48rpx;
    background: $color-primary;
    border-radius: 38rpx 38rpx 38rpx 38rpx;
    font-weight: normal;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 45rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding: 15rpx;
  }

  .remind-popup-cancel {
    margin-top: 22rpx;
    font-weight: normal;
    font-size: 26rpx;
    color: #919094;
    line-height: 36rpx;
    text-align: center;
  }
}

::v-deep web-view {
  iframe {
    width: 100vw !important;
    height: calc(100vh - 44px) !important;
    border: none;
  }
}
</style>
