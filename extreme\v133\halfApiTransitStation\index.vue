<template>
  <view class="page-container">
    <half-api-transit-station />
  </view>
</template>

<script>
import HalfApiTransitStation from '@/components/dai-yue-tong/halfApiTransitStation/index.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import { reportUV } from '@/apis/common'
import { isInWechatMiniProgram } from '@/utils/user-agent'
import { saveAppletOpenRecord } from '@/apis/common-3'
import { fetchFlow, fetchJumpUrl, register } from '@/apis/common-2'

export default {
  name: 'HalfApiTransitStationPage',
  components: {
    HalfApiTransitStation
  },
  data() {
    return {
      form: {
        consumerId: '',
        firstFlag: '',
        templateVersion: 'v133'
      },
      isBusy: false
    }
  },

  async onShow() {
    if (this.isBusy) {
      return
    }
    this.isBusy = true

    const query = this.$route.query
    const { param, u, i } = query
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (u) {
      this.$u.vuex('vuex_uid', u)
      await this.register(u, i)
      await this.fetchJumpUrl()
      await this.fetchFlow()
    }

    if (this.form.consumerId && this.form.channelId) {
      this.navigateToNextPage()
    } else {
      this.navigateToEnd()
    }
  },

  methods: {
    async fetchJumpUrl() {
      const res = await fetchJumpUrl({
        uid: this.vuex_uid,
        channelId: this.form.channelId
      })
      if (res.code == 200 && res.data) {
        this.$u.vuex('vuex_halfJumpUrl', res.data)
      } else {
        this.$u.vuex('vuex_halfJumpUrl', '')
      }
    },

    async register(uid, invite) {
      const res = await register({ uid, invite })
      if (res.code == 200) {
        this.form.consumerId = res.data.consumerId
        this.form.channelId = res.data.channelId

        this.$u.vuex('vuex_consumerId', res.data.consumerId)
        this.$u.vuex('vuex_channelId', res.data.channelId)

        await reportUV({
          channelId: this.form.channelId
        })

        if (isInWechatMiniProgram()) {
          await saveAppletOpenRecord({
            channelId: this.form.channelId,
            appUserId: this.form.consumerId
          })
        }
      }
    },

    async fetchFlow() {
      const res = await fetchFlow(this.form)
      setFlowNodes(res.data.length ? res.data : null)
    },

    async navigateToNextPage() {
      try {
        const routeMap = {
          offline: '/extreme/v133/apply/index',
          wechat: '/extreme/v133/qw/index',
          overloan: '/extreme/v133/applyOther/index',
          end: '/extreme/v133/download/index',
          halfapi: '/extreme/v133/webviewDown/index',
          wechat_official_account: '/extreme/v133/wechatOfficialAccount/index'
        }

        const nextFlow = await nextFlowNode({
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        })

        this.isBusy = false

        if (nextFlow.flow === 'halfapi') {
          this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
        }

        if (nextFlow.flow === 'end') {
          if (this.vuex_halfJumpUrl) {
            window.location.href = this.vuex_halfJumpUrl
            return
          }
        }

        const path = routeMap[nextFlow.flow]
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `${path}?param=${urlParamString}`
        })
      } catch (e) {
        this.navigateToEnd()
      }
    },
    navigateToEnd() {
      uni.navigateTo({
        url: `/extreme/v133/download/index?param=${encodeURIComponent(encryptByDES(JSON.stringify(this.form)))}&result=2`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
}
</style>
