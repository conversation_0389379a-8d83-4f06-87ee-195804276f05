<template>
  <view class="phone-container">
    <input
      type="tel"
      maxlength="11"
      class="phone-input"
      placeholder="输入手机号获取额度(已加密)"
      @blur="phoneBlur"
      v-model="phone"
      placeholder-style="color: #A2A3A5"
    />
    <view class="get-my-quota" @click="clickGetMyQuota">领取我的额度</view>
    <view class="agreement" @click="toggleAgree">
      <image
        v-if="isAgree"
        class="agree-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/734e69daa38f4e0bb51a27bd51a1709f.png"
      ></image>
      <image
        v-else
        class="agree-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/ca29794ca1564662ad1a6be4cd760903.png"
      ></image>

      <view class="agreement-text">
        我已阅读并同意
        <text
          class="name"
          v-for="item in agreementList"
          :key="item.protocolId"
          @click.stop="clickAgreement(item)"
        >
          《{{ item.name }}》
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import { validateMobile } from '@/utils/validators'
import throttle from '@/utils/throttle'

export default {
  name: 'PhoneContainer',

  props: {
    value: {
      type: String,
      default: ''
    },
    agreementList: {
      type: Array,
      default: () => []
    },
    agreeStatus: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      phone: this.value,
      isAgree: this.agreeStatus
    }
  },

  watch: {
    value(val) {
      this.phone = val
    },
    phone(val) {
      this.$emit('input', val)
    },
    agreeStatus(val) {
      this.isAgree = val
    },
    isAgree(val) {
      this.$emit('update:agreeStatus', val)
    }
  },

  methods: {
    toggleAgree() {
      this.isAgree = !this.isAgree
    },

    clickGetMyQuota() {
      throttle(this.getMyQuotaHandler)
    },

    getMyQuotaHandler() {
      if (!this.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (!this.isAgree) {
        this.$emit('open-agreement')
        return
      }

      this.$emit('get-quota', this.phone)
    },

    clickAgreement(agreement) {
      this.$emit('open-agreement', agreement)
    },

    phoneBlur() {
      if (this.phone && !this.$u.test.mobile(this.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.phone-container {
  margin: 0 30rpx;
  padding: 40rpx 20rpx 36rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .phone-input {
    height: 96rpx;
    padding: 30rpx 40rpx;
    background: #f6f6f8;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
    font-size: 28rpx;
  }

  .get-my-quota {
    margin: 20rpx 0;
    padding: 22rpx;
    text-align: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    background: #ff3154;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
  }

  .agreement {
    display: flex;
    gap: 5rpx;

    .agree-icon {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 32rpx;

      .name {
        color: #ff3154;
      }
    }
  }
}
</style>
