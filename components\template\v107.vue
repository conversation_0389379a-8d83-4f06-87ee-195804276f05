<template>
  <view class="page-container">
    <Header />

    <view class="demand-amount">
      <view class="amount-title">需求金额</view>
      <view class="amount-options">
        <view
          class="amount-option"
          :class="{ selected: selectedAmount === '5万以内' }"
          @click="selectAmount('5万以内')"
        >
          <text>5万以内</text>
        </view>
        <view
          class="amount-option"
          :class="{ selected: selectedAmount === '5-20万' }"
          @click="selectAmount('5-20万')"
        >
          <text>5-20万</text>
        </view>
        <view
          class="amount-option"
          :class="{ selected: selectedAmount === '20万以上' }"
          @click="selectAmount('20万以上')"
        >
          <text>20万以上</text>
        </view>
      </view>
      <view class="amount-tip">
        <image
          class="amount-tip-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/a787ef75d25d4253b1656aa6c99d2634.svg"
          mode="scaleToFill"
        />
        <text class="amount-tip-text"> 参考年利率5.40%, 实际以下款为准 </text>
      </view>
    </view>

    <view class="gap-block"></view>

    <view class="demand-period">
      <view class="period-header">
        <view class="period-header-title">需求周期</view>
        <view class="period-header-tip">
          <view class="period-header-tip-item">
            <svg
              class="period-header-tip-icon"
              data-v-4d89ec0e=""
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                id="&amp;#229;&amp;#174;&amp;#137;&amp;#229;&amp;#133;&amp;#168;&amp;#230;&amp;#143;&amp;#144;&amp;#233;&amp;#134;&amp;#146;"
                clip-path="url(#clip0_208_492)"
              >
                <g
                  id="&amp;#229;&amp;#177;&amp;#149;&amp;#231;&amp;#164;&amp;#186;&amp;#229;&amp;#155;&amp;#190;&amp;#230;&amp;#160;&amp;#135;/&amp;#233;&amp;#152;&amp;#178;&amp;#232;&amp;#175;&amp;#136;&amp;#230;&amp;#143;&amp;#144;&amp;#233;&amp;#134;&amp;#146;"
                >
                  <rect
                    id="&amp;#231;&amp;#159;&amp;#169;&amp;#229;&amp;#189;&amp;#162;"
                    opacity="0.01"
                    width="16"
                    height="16"
                    fill="black"
                  ></rect>
                  <path
                    id="&amp;#231;&amp;#159;&amp;#169;&amp;#229;&amp;#189;&amp;#162;_2"
                    d="M2.5 8V3.36187L8.07735 1.52705L13.5 3.35885V8C13.5 11.0376 11.0376 13.5 8 13.5C4.96243 13.5 2.5 11.0376 2.5 8Z"
                    stroke="#007aff"
                  ></path>
                </g>
                <path
                  id="Vector"
                  d="M5.30005 7.32857L7.28108 9.15714L10.7858 5.5"
                  stroke="#007aff"
                ></path>
              </g>
              <defs>
                <clipPath id="clip0_208_492">
                  <rect width="16" height="16" fill="white"></rect>
                </clipPath>
              </defs>
            </svg>
            <text class="period-header-tip-text">坚守合规经营</text>
          </view>
          <view class="period-header-tip-item">
            <svg
              class="period-header-tip-icon"
              data-v-4d89ec0e=""
              width="16"
              height="16"
              viewBox="0 0 16 16"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g
                id="&amp;#229;&amp;#174;&amp;#137;&amp;#229;&amp;#133;&amp;#168;&amp;#230;&amp;#143;&amp;#144;&amp;#233;&amp;#134;&amp;#146;"
                clip-path="url(#clip0_208_492)"
              >
                <g
                  id="&amp;#229;&amp;#177;&amp;#149;&amp;#231;&amp;#164;&amp;#186;&amp;#229;&amp;#155;&amp;#190;&amp;#230;&amp;#160;&amp;#135;/&amp;#233;&amp;#152;&amp;#178;&amp;#232;&amp;#175;&amp;#136;&amp;#230;&amp;#143;&amp;#144;&amp;#233;&amp;#134;&amp;#146;"
                >
                  <rect
                    id="&amp;#231;&amp;#159;&amp;#169;&amp;#229;&amp;#189;&amp;#162;"
                    opacity="0.01"
                    width="16"
                    height="16"
                    fill="black"
                  ></rect>
                  <path
                    id="&amp;#231;&amp;#159;&amp;#169;&amp;#229;&amp;#189;&amp;#162;_2"
                    d="M2.5 8V3.36187L8.07735 1.52705L13.5 3.35885V8C13.5 11.0376 11.0376 13.5 8 13.5C4.96243 13.5 2.5 11.0376 2.5 8Z"
                    stroke="#007aff"
                  ></path>
                </g>
                <path
                  id="Vector"
                  d="M5.30005 7.32857L7.28108 9.15714L10.7858 5.5"
                  stroke="#007aff"
                ></path>
              </g>
              <defs>
                <clipPath id="clip0_208_492">
                  <rect width="16" height="16" fill="white"></rect>
                </clipPath>
              </defs>
            </svg>
            <text class="period-header-tip-text">严格保护信息</text>
          </view>
        </view>
      </view>
      <view class="period-tip">*借{{ this.calculateAmount / 10000 }}万为例，预计还款计划如下:</view>

      <scroll-view
        scroll-x
        class="period-scroll-view"
        @scroll="handleScroll"
        :scroll-left="scrollLeft"
        :scroll-with-animation="scrollWithAnimation"
      >
        <view class="period-options">
          <view
            v-for="(month, index) in monthRange"
            :key="index"
            class="period-option"
            :class="{ selected: form.monthIndex == index, recommended: month.recommended }"
            @click="selectMonth(index)"
          >
            <text class="period-option-title">{{ month.label }}</text>
            <text class="period-option-first-month">首月还¥{{ month.firstMonthPay }}</text>
            <text class="period-option-month-interest">月利息¥{{ month.monthInterest }}</text>
          </view>
        </view>
      </scroll-view>

      <view class="period-slider">
        <view
          class="period-slider-line"
          :style="{
            width: sliderWidth + 'rpx',
            marginLeft: sliderPosition + 'rpx'
          }"
        ></view>
      </view>
    </view>

    <view class="gap-block"></view>

    <view class="mobile-input-container">
      <view class="mobile-input-item">
        <view class="mobile-input-label">手机号</view>
        <input
          class="mobile-input"
          type="text"
          placeholder="请输入手机号"
          v-model.trim="form.phone"
          maxlength="11"
        />
      </view>
    </view>

    <view class="submit-button-container" @click="clickGetMyQuota">
      <view class="submit-button">立即申请</view>
    </view>

    <view class="agreement-checkbox-container" @click="isAgree = !isAgree">
      <view class="agreement-checkbox">
        <uni-icons
          class="agreement-checkbox-icon checked"
          type="checkbox-filled"
          size="16"
          v-if="isAgree"
        ></uni-icons>
        <uni-icons class="agreement-checkbox-icon" type="circle" size="16" v-else></uni-icons>

        <view class="agreement-checkbox-text"
          >我已阅读并同意
          <text
            v-for="agreement in agreementList"
            class="agreement-checkbox-text-item"
            :key="agreement.protocolId"
            @click.stop="clickAgreement(agreement)"
            >《{{ agreement.name }}》</text
          >
        </view>
      </view>
    </view>

    <view class="download-app-container">
      <image
        class="download-app-image"
        src="https://cdn.oss-unos.hmctec.cn/common/path/0b3fd1b8eebb40bdad73c372ce6789ab.png"
        mode="scaleToFill"
      />
    </view>

    <view class="steps-container">
      <text class="steps-title">3步轻松借款</text>
      <view class="steps">
        <view class="step">
          <svg
            class="icon"
            data-v-4d89ec0e=""
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="01. &amp;#230;&amp;#143;&amp;#144;&amp;#228;&amp;#186;&amp;#164;&amp;#231;&amp;#148;&amp;#179;&amp;#232;&amp;#175;&amp;#183;"
            >
              <g id="Group 768">
                <rect
                  id="Rectangle 1443"
                  x="3"
                  y="8"
                  width="21"
                  height="24"
                  fill="url(#paint0_linear_185_2569)"
                ></rect>
                <path
                  id="Rectangle 1442"
                  d="M7 3H29V20C29 24.4183 25.4183 28 21 28H7V3Z"
                  fill="#007aff"
                ></path>
                <line
                  id="Line 89"
                  x1="13"
                  y1="11.5"
                  x2="23"
                  y2="11.5"
                  stroke="white"
                  stroke-width="3"
                ></line>
                <line
                  id="Line 91"
                  x1="13"
                  y1="17.5"
                  x2="19"
                  y2="17.5"
                  stroke="white"
                  stroke-width="3"
                ></line>
              </g>
            </g>
            <defs>
              <linearGradient
                id="paint0_linear_185_2569"
                x1="14"
                y1="8"
                x2="14"
                y2="32"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#007aff22"></stop>
                <stop offset="0.834535" stop-color="#F6F7FA"></stop>
              </linearGradient>
            </defs>
          </svg>
          <text class="step-description">01.提交申请</text>
        </view>
        <svg
          class="step-icon"
          data-v-b73dfa6a=""
          data-v-4d89ec0e=""
          width="26"
          height="26"
          viewBox="0 0 26 26"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g data-v-b73dfa6a="" id="&amp;#231;&amp;#174;&amp;#173;&amp;#229;&amp;#164;&amp;#180;">
            <path
              data-v-b73dfa6a=""
              id="Rectangle 1456"
              d="M1 14H18V10L25 16H1V14Z"
              fill="url(#paint0_linear_185_2587)"
            ></path>
          </g>
          <defs data-v-b73dfa6a="">
            <linearGradient
              data-v-b73dfa6a=""
              id="paint0_linear_185_2587"
              x1="25"
              y1="8.49993"
              x2="0.999999"
              y2="8.50004"
              gradientUnits="userSpaceOnUse"
            >
              <stop data-v-b73dfa6a="" stop-color="#007aff" stop-opacity="0.5"></stop>
              <stop data-v-b73dfa6a="" offset="1" stop-color="#007aff" stop-opacity="0"></stop>
            </linearGradient>
          </defs>
        </svg>
        <view class="step">
          <svg
            class="icon"
            data-v-4d89ec0e=""
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="02. &amp;#233;&amp;#156;&amp;#128;&amp;#230;&amp;#177;&amp;#130;&amp;#232;&amp;#175;&amp;#132;&amp;#228;&amp;#188;&amp;#176;"
            >
              <g id="Group 768">
                <path
                  id="Rectangle 1442"
                  d="M2 10L13.5 7L25 10V20.5C25 26.8513 19.8513 32 13.5 32C7.14873 32 2 26.8513 2 20.5V10Z"
                  fill="url(#paint0_linear_185_2596)"
                ></path>
                <path
                  id="Rectangle 1444"
                  d="M6 6L17.5 3L29 6V16.5C29 22.8513 23.8513 28 17.5 28C11.1487 28 6 22.8513 6 16.5V6Z"
                  fill="#007aff"
                ></path>
              </g>
              <g id="Group 836">
                <line
                  id="Line 100"
                  x1="13.5607"
                  y1="14.6566"
                  x2="17.8033"
                  y2="18.8993"
                  stroke="white"
                  stroke-width="3"
                ></line>
                <line
                  id="Line 101"
                  x1="15.682"
                  y1="18.8993"
                  x2="22.3995"
                  y2="12.1818"
                  stroke="white"
                  stroke-width="3"
                ></line>
              </g>
            </g>
            <defs>
              <linearGradient
                id="paint0_linear_185_2596"
                x1="14.0476"
                y1="7"
                x2="14.0476"
                y2="32"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#007aff22"></stop>
                <stop offset="0.834535" stop-color="#F6F7FA"></stop>
              </linearGradient>
            </defs>
          </svg>
          <text class="step-description">02.需求评估</text>
        </view>

        <svg
          class="step-icon"
          data-v-b73dfa6a=""
          data-v-4d89ec0e=""
          width="26"
          height="26"
          viewBox="0 0 26 26"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g data-v-b73dfa6a="" id="&amp;#231;&amp;#174;&amp;#173;&amp;#229;&amp;#164;&amp;#180;">
            <path
              data-v-b73dfa6a=""
              id="Rectangle 1456"
              d="M1 14H18V10L25 16H1V14Z"
              fill="url(#paint0_linear_185_2587)"
            ></path>
          </g>
          <defs data-v-b73dfa6a="">
            <linearGradient
              data-v-b73dfa6a=""
              id="paint0_linear_185_2587"
              x1="25"
              y1="8.49993"
              x2="0.999999"
              y2="8.50004"
              gradientUnits="userSpaceOnUse"
            >
              <stop data-v-b73dfa6a="" stop-color="#007aff" stop-opacity="0.5"></stop>
              <stop data-v-b73dfa6a="" offset="1" stop-color="#007aff" stop-opacity="0"></stop>
            </linearGradient>
          </defs>
        </svg>
        <view class="step">
          <svg
            class="icon"
            data-v-4d89ec0e=""
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <g
              id="03. &amp;#231;&amp;#173;&amp;#190;&amp;#231;&amp;#186;&amp;#166;&amp;#229;&amp;#138;&amp;#158;&amp;#231;&amp;#144;&amp;#134;"
            >
              <g id="Group 768">
                <rect
                  id="Rectangle 1442"
                  x="1"
                  y="6"
                  width="26"
                  height="26"
                  rx="13"
                  fill="url(#paint0_linear_185_2576)"
                ></rect>
                <rect
                  id="Rectangle 1443"
                  x="4"
                  y="2"
                  width="26"
                  height="26"
                  rx="13"
                  fill="#007aff"
                ></rect>
              </g>
              <g id="Group 772">
                <path id="Rectangle 1445" d="M12 12.3H22V15.3H12V12.3Z" fill="white"></path>
                <rect
                  id="Rectangle 1448"
                  x="14.6211"
                  y="8.30005"
                  width="6"
                  height="3"
                  transform="rotate(45 14.6211 8.30005)"
                  fill="white"
                ></rect>
                <rect
                  id="Rectangle 1449"
                  width="6"
                  height="3"
                  transform="matrix(-0.707107 0.707107 0.707107 0.707107 19.7427 8.30005)"
                  fill="white"
                ></rect>
                <path
                  id="Rectangle 1447"
                  d="M18.5 13.0002L18.5 21.5002L15.5 21.5002L15.5 13.0002L18.5 13.0002Z"
                  fill="white"
                ></path>
                <path id="Rectangle 1446" d="M12 16.3H22V19.3H12V16.3Z" fill="white"></path>
              </g>
            </g>
            <defs>
              <linearGradient
                id="paint0_linear_185_2576"
                x1="14.619"
                y1="6"
                x2="14.619"
                y2="32"
                gradientUnits="userSpaceOnUse"
              >
                <stop stop-color="#007aff22"></stop>
                <stop offset="0.834535" stop-color="#F6F7FA"></stop>
              </linearGradient>
            </defs>
          </svg>
          <text class="step-description">03.签约办理</text>
        </view>
      </view>
    </view>

    <view class="declaration-container">
      <Declaration />
    </view>

    <!-- 客服按钮 -->
    <view
      class="customer-service-btn"
      @touchstart="handleTouchStart"
      @touchmove.stop.prevent="handleTouchMove"
      :style="{ top: buttonTop + 'rpx' }"
      @click="$refs.contactUsPopup.open()"
    >
      <svg
        class="customer-service-btn-icon"
        width="46"
        height="46"
        viewBox="0 0 46 46"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          id="&amp;#229;&amp;#174;&amp;#162;&amp;#230;&amp;#156;&amp;#141;"
          clip-path="url(#clip0_1032_1541)"
        >
          <g id="Ellipse 111">
            <circle cx="23" cy="23" r="23" fill="white"></circle>
            <circle cx="23" cy="23" r="23" fill="url(#paint0_linear_1032_1541)"></circle>
            <circle cx="23" cy="23" r="23" fill="url(#paint1_linear_1032_1541)"></circle>
          </g>
          <g id="Frame" clip-path="url(#clip1_1032_1541)">
            <g id="customer-support-1--customer-headset-help-microphone-phone-support">
              <g id="Union" filter="url(#filter0_d_1032_1541)">
                <path
                  fill-rule="evenodd"
                  clip-rule="evenodd"
                  d="M22.9998 12.4993C21.1433 12.4993 19.3628 13.2368 18.0501 14.5496C16.7373 15.8624 15.9998 17.6428 15.9998 19.4993V28.8327H10.1665V19.4993H13.6665C13.6665 17.024 14.6498 14.65 16.4002 12.8997C18.1505 11.1493 20.5245 10.166 22.9998 10.166C25.4752 10.166 27.8492 11.1493 29.5995 12.8997C31.3498 14.65 32.3332 17.024 32.3332 19.4993H35.8332V28.8327H32.3332C32.3332 30.3798 31.7186 31.8635 30.6246 32.9575C29.5307 34.0514 28.0469 34.666 26.4998 34.666V35.8327H19.4998V31.166H26.4998V32.3327C27.4281 32.3327 28.3183 31.9639 28.9747 31.3076C29.6311 30.6512 29.9998 29.7609 29.9998 28.8327V19.4993C29.9998 17.6428 29.2623 15.8624 27.9496 14.5496C26.6368 13.2368 24.8564 12.4993 22.9998 12.4993Z"
                  fill="white"
                ></path>
              </g>
            </g>
          </g>
        </g>
        <defs>
          <filter
            id="filter0_d_1032_1541"
            x="8.1665"
            y="10.166"
            width="29.6665"
            height="29.666"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix"></feFlood>
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            ></feColorMatrix>
            <feOffset dy="2"></feOffset>
            <feGaussianBlur stdDeviation="1"></feGaussianBlur>
            <feComposite in2="hardAlpha" operator="out"></feComposite>
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0.314567 0 0 0 0 0.783333 0 0 0 0.5 0"
            ></feColorMatrix>
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_1032_1541"
            ></feBlend>
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_1032_1541"
              result="shape"
            ></feBlend>
          </filter>
          <linearGradient
            id="paint0_linear_1032_1541"
            x1="46"
            y1="46.6866"
            x2="-9.49581"
            y2="19.1087"
            gradientUnits="userSpaceOnUse"
          >
            <stop stop-color="#3cb6ff"></stop>
            <stop offset="1" stop-color="#007aff"></stop>
          </linearGradient>
          <linearGradient
            id="paint1_linear_1032_1541"
            x1="0"
            y1="46"
            x2="26.964"
            y2="-9.28446"
            gradientUnits="userSpaceOnUse"
          >
            <stop offset="0.0417447" stop-color="#007aff"></stop>
            <stop offset="0.736653" stop-color="#3cb6ff"></stop>
          </linearGradient>
          <clipPath id="clip0_1032_1541">
            <rect width="46" height="46" fill="white"></rect>
          </clipPath>
          <clipPath id="clip1_1032_1541">
            <rect width="28" height="28" fill="white" transform="translate(9 9)"></rect>
          </clipPath>
        </defs>
      </svg>
    </view>

    <!-- 联系我们弹窗 -->
    <uni-popup background-color="#F6F7FA" ref="contactUsPopup" type="center" border-radius="32rpx">
      <view class="contact-us-popup">
        <view class="contact-title">联系我们</view>
        <view class="contact-desc">点击下面联系方式即可复制</view>
        <view class="contact-content-container" @click="clickCopy('400-003-9350')">
          <image
            class="contact-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/04163f81ef2643d78b6b5d0125a52afb.svg"
            mode="scaleToFill"
          />
          <view class="contact-content">
            <view class="contact-content-title">电话客服</view>
            <view class="contact-content-desc">400-003-9350(工作日9:00-18:00)</view>
          </view>
        </view>
        <view class="contact-content-container" @click="clickCopy('<EMAIL>')">
          <image
            class="contact-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/e9f3c06682544124896acc17752a3639.svg"
            mode="scaleToFill"
          />
          <view class="contact-content">
            <view class="contact-content-title">商务合作邮箱</view>
            <view class="contact-content-desc"><EMAIL></view>
          </view>
        </view>

        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/308fb51bf41943c2aec94bc29ef954ae.png"
          class="close-btn"
          @click="$refs.contactUsPopup.close()"
        />
      </view>
    </uni-popup>

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <view class="agreement-container">
          <view v-for="agreement in agreementList" :key="agreement.protocolId">
            <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
            <view class="agreement-content">
              <div v-html="agreement.content"></div>
            </view>
          </view>
        </view>
        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickAgreeAndContinue">同意并继续</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="codePopup"
      type="center"
      border-radius="16rpx"
    >
      <view class="code-popup">
        <view class="code-popup-title">输入验证码</view>
        <view class="code-popup-phone">短信验证码已发送至: {{ form.phone }}</view>
        <view class="code-popup-input-container">
          <input
            class="code-popup-input"
            type="number"
            maxlength="6"
            v-model="form.code"
            placeholder="请输入验证码"
          />
        </view>
        <view class="code-popup-countdown-container">
          <view class="code-popup-countdown" v-if="codeCountdown > 0"> {{ codeCountdown }}s </view>
          <view class="code-popup-get-code" v-else @click="clickGetCode">重新获取</view>
        </view>
        <view class="code-popup-button" @click="clickGetAmount">确认</view>

        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/308fb51bf41943c2aec94bc29ef954ae.png"
          class="close-btn"
          @click="$refs.codePopup.close()"
        />
      </view>
    </uni-popup>
  </view>
</template>
<script>
import { validateMobile } from '@/utils/validators'
import AlertBar from '@/components/alert/AlertBar.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { reportUV } from '@/apis/common'
import { saveLoan, sendSmsCode } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hr.vue'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-hr.vue'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v107',

  components: {
    AlertBar,
    Declaration,
    Header
  },

  data() {
    return {
      selectedAmount: '20万以上',
      form: {
        demandAmount: 200000,
        phone: '',
        channelId: '',
        monthIndex: 3, // 默认选中12个月
        agreement: false
      },
      calculateAmount: 200000, // 默认计算金额
      monthRange: [
        {
          label: '3个月',
          // 分期月数
          value: 3,
          // 首月还款金额
          firstMonthPay: '',
          // 月利息
          monthInterest: ''
        },
        {
          label: '6个月',
          value: 6,
          firstMonthPay: '',
          monthInterest: ''
        },
        {
          label: '9个月',
          value: 9,
          firstMonthPay: '',
          monthInterest: ''
        },
        {
          label: '12个月',
          value: 12,
          firstMonthPay: '',
          monthInterest: '',
          recommended: true
        },
        {
          label: '24个月',
          value: 24,
          firstMonthPay: '',
          monthInterest: ''
        },
        {
          label: '36个月',
          value: 36,
          firstMonthPay: '',
          monthInterest: ''
        }
      ],
      isAgree: false,
      codeTimer: null,
      codeCountdown: 0,
      lockBack: null,
      agreementList: [],
      scrollLeft: 0,
      sliderWidth: 38, // 初始滑块宽度
      scrollViewWidth: 0, // 可视区域宽度
      scrollContentWidth: 0, // 内容总宽度
      sliderPosition: 0, // 滑块左边距
      scrollWithAnimation: false, // 初始不使用滚动动画
      buttonTop: 800, // 按钮初始位置
      startY: 0, // 触摸开始时的Y坐标
      moveY: 0 // 移动的Y坐标
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
      this.codeTimer = null
    }

    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  mounted() {
    uni.setNavigationBarTitle({
      title: '惠生活·融易贷-惠融钱包'
    })

    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
    }
    this.fetchAgreement()
    // 初始计算还款金额
    this.calculatePayments()
    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()

    setTimeout(() => {
      this.calculateSliderWidth()
      this.centerSelectedMonth()

      // 初始化完成后，开启滚动动画
      setTimeout(() => {
        this.scrollWithAnimation = true
      }, 500)
    }, 0)
  },
  methods: {
    selectAmount(amount) {
      this.selectedAmount = amount

      // 设置相应的金额值
      if (amount === '5万以内') {
        this.form.demandAmount = 50000
        this.calculateAmount = 50000
      } else if (amount === '5-20万') {
        this.form.demandAmount = 150000
        this.calculateAmount = 100000
      } else if (amount === '20万以上') {
        this.form.demandAmount = 200000
        this.calculateAmount = 200000
      }

      this.calculatePayments() // 更新计算
    },

    async fetchAgreement() {
      // 注册页协议
      this.agreementList = await getAgreements('hrqb-register')
    },

    // 计算首月还款和月利息
    calculatePayments() {
      // 使用固定的计算金额
      const amount = this.calculateAmount
      // 参考年利率5.4%
      const annualRate = 5.4 / 100
      // 月利率 = 年利率 / 12
      const monthlyRate = annualRate / 12

      this.monthRange.forEach((month, index) => {
        const months = month.value // 还款月数

        // 等额本息还款公式：每月还款额 = [贷款本金×月利率×(1+月利率)^还款月数] ÷ [(1+月利率)^还款月数-1]
        const monthlyPayment =
          (amount * monthlyRate * Math.pow(1 + monthlyRate, months)) /
          (Math.pow(1 + monthlyRate, months) - 1)

        // 首月利息 = 贷款本金 × 月利率
        const firstMonthInterest = amount * monthlyRate

        // 更新数据，保留两位小数
        this.monthRange[index].monthInterest = firstMonthInterest.toFixed(2)
        this.monthRange[index].firstMonthPay = monthlyPayment.toFixed(2)
      })
    },

    clickGetMyQuota() {
      throttle(this.getMyQuotaHandler)
    },

    getMyQuotaHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (!this.isAgree) {
        this.$refs.agreementPopup.open()
        this.$forceUpdate()
        return
      }

      this.$refs.codePopup.open()
      // 打开验证码弹窗时，自动获取验证码
      if (!this.codeTimer) {
        this.clickGetCode()
      }
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    clickGetCode() {
      this.getCodeHandler()
    },

    async getCodeHandler() {
      const res = await sendSmsCode({
        phone: this.form.phone,
        channelId: this.form.channelId
      })

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '发送失败',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      this.codeCountdown = 60
      if (this.codeTimer) {
        clearInterval(this.codeTimer)
        this.codeTimer = null
      }
      this.codeTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.codeTimer)
          this.codeTimer = null
        }
      }, 1000)
    },

    clickAgreeAndContinue() {
      this.$refs.agreementPopup.close()
      this.isAgree = true
      this.clickGetMyQuota()
    },

    clickGetAmount() {
      throttle(() => {
        if (!this.form.code) {
          uni.showToast({
            title: '请输入验证码',
            icon: 'none'
          })
          return
        }

        this.login()
      })
    },

    async getVisitorId() {
      const fp = await fpPromise
      const { visitorId } = await fp.get()
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },

    async getLoginParams() {
      const params = {}

      params.phoneBlack = getBlackPhone()
      // params.h5UaUuid = await this.getVisitorId();
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = this.calculateAmount
      params.phone = this.form.phone
      params.code = this.form.code
      params.channelId = this.form.channelId

      return params
    },

    async login() {
      const params = await this.getLoginParams()

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      // 登录成功后，记录手机号
      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)
      this.$u.vuex('vuex_consumerId', res.data)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: this.calculateAmount,
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v107/auth/index?param=${urlParamString}`
      })
    },

    // 处理滚动事件
    handleScroll(e) {
      // 获取当前滚动位置
      this.scrollLeft = e.detail.scrollLeft

      // 如果内容宽度大于可视区域
      if (this.scrollContentWidth > this.scrollViewWidth) {
        // 计算滑块的左边距，根据滚动位置比例
        const leftPercent =
          (this.scrollLeft / (this.scrollContentWidth - this.scrollViewWidth)) * 100
        const maxLeftMargin = 114 - this.sliderWidth // 114rpx是滑块容器的宽度
        this.sliderPosition = Math.min(maxLeftMargin, (leftPercent * maxLeftMargin) / 100)
      }
    },

    // 计算滑块宽度
    calculateSliderWidth() {
      // 获取滚动区域和内容的宽度
      const query = uni.createSelectorQuery().in(this)
      query
        .select('.period-scroll-view')
        .boundingClientRect((data) => {
          if (data) {
            this.scrollViewWidth = data.width
          }
        })
        .exec()

      query
        .select('.period-options')
        .boundingClientRect((data) => {
          if (data) {
            this.scrollContentWidth = data.width

            // 计算滑块宽度：可视区域与总内容的比例
            if (this.scrollContentWidth > 0) {
              const widthPercent = this.scrollViewWidth / this.scrollContentWidth
              this.sliderWidth = Math.max(20, Math.round(114 * widthPercent)) // 最小宽度为20rpx
            }
          }
        })
        .exec()
    },

    // 选择月份
    selectMonth(index) {
      this.form.monthIndex = index
      this.centerSelectedMonth()
    },

    // 居中显示选中的月份
    centerSelectedMonth() {
      // 计算需要滚动到的位置，让选中的选项居中显示
      this.$nextTick(() => {
        // 获取每个选项的宽度和间距
        const optionWidth = 332 // 选项宽度（rpx）
        const optionGap = 20 // 选项间距（rpx）

        // 计算要滚动到的位置
        // 将px和rpx进行转换，需要考虑屏幕宽度
        const screenWidth = uni.getSystemInfoSync().windowWidth
        const rpxToPx = screenWidth / 750 // 1rpx对应的px值

        // 计算选中项的中心位置
        const itemCenterPosition =
          (optionWidth + optionGap) * this.form.monthIndex + optionWidth / 2

        // 计算滚动视图的中心位置
        const scrollViewCenterPx = this.scrollViewWidth / 2
        const scrollViewCenterRpx = scrollViewCenterPx / rpxToPx

        // 计算需要滚动的位置（以选中项为中心）
        let targetScrollLeft = (itemCenterPosition - scrollViewCenterRpx) * rpxToPx

        // 确保滚动位置不超出范围
        targetScrollLeft = Math.max(0, targetScrollLeft)
        targetScrollLeft = Math.min(
          this.scrollContentWidth - this.scrollViewWidth,
          targetScrollLeft
        )

        // 设置滚动位置
        this.scrollLeft = targetScrollLeft
      })
    },

    // 触摸开始事件
    handleTouchStart(e) {
      this.startY = e.touches[0].clientY
    },

    // 触摸移动事件
    handleTouchMove(e) {
      const currentY = e.touches[0].clientY
      const moveY = currentY - this.startY

      // 将px转换为rpx
      const systemInfo = uni.getSystemInfoSync()
      const rpxRatio = 750 / systemInfo.windowWidth
      const moveRpx = moveY * rpxRatio

      // 计算新的位置
      let newTop = this.buttonTop + moveRpx

      // 限制拖动范围，不能超出屏幕
      const maxTop = systemInfo.windowHeight * rpxRatio - 200 // 底部留出200rpx的边距
      newTop = Math.max(100, Math.min(newTop, maxTop)) // 顶部留出100rpx的边距

      this.buttonTop = newTop
      this.startY = currentY
    },

    clickCopy(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          uni.showToast({
            title: '复制成功',
            icon: 'none'
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #2a6af7;

.color-primary {
  color: $color-primary;
}

.page-container {
  min-height: 100vh;
  background-color: #fff;
}

.demand-amount {
  width: 750rpx;
  height: 375rpx;
  padding: 0 30rpx;
  background: #ffffff;
  border-radius: 0rpx 0rpx 0rpx 0rpx;

  .amount-title {
    padding: 0 0 30rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
  }

  .amount-options {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .amount-option {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 210rpx;
      height: 88rpx;
      border-radius: 8rpx 8rpx 8rpx 8rpx;
      border: 1rpx solid #999999;
      font-weight: 700;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;

      &.selected {
        background: #2a6af7;
        border: 1rpx solid #2a6af7;
        color: #ffffff;
      }
    }
  }

  .amount-tip {
    margin-top: 45rpx;
    padding-top: 35rpx;
    border-top: 1rpx solid #d8d8d8;
    display: flex;
    align-items: center;
    gap: 15rpx;

    .amount-tip-icon {
      width: 130rpx;
      height: 40rpx;
    }

    .amount-tip-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #fc480c;
      line-height: 32rpx;
    }
  }
}

.gap-block {
  height: 20rpx;
  background-color: #f6f6f8;
}

.demand-period {
  width: 750rpx;
  height: 444rpx;
  background: #ffffff;
  border-radius: 0rpx 0rpx 0rpx 0rpx;
  padding: 0 30rpx;

  .period-header {
    padding: 30rpx 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #d8d8d8;

    .period-header-title {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }

    .period-header-tip {
      display: flex;
      align-items: center;
      gap: 20rpx;

      .period-header-tip-item {
        display: flex;
        align-items: center;
      }

      .period-header-tip-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .period-header-tip-text {
        font-weight: 400;
        font-size: 24rpx;
        color: #2a6af7;
        line-height: 32rpx;
      }
    }
  }

  .period-tip {
    padding: 34rpx 0;

    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 32rpx;
  }

  .period-scroll-view {
    width: 100%;
    white-space: nowrap;
  }

  .period-options {
    display: inline-flex;
    gap: 20rpx;

    .period-option {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 5rpx;
      width: 332rpx;
      height: 172rpx;
      border-radius: 20rpx;
      border: 4rpx solid #999999;
      flex-shrink: 0;
      overflow: hidden;

      &.selected {
        background: rgba(0, 122, 255, 0.05);
        border: 4rpx solid #007aff;
      }

      &.recommended {
        background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/6b6a49239cb34e478414d5f53b8d6a83.svg');
        background-repeat: no-repeat;
        background-position: 0 0;
        background-size: 64rpx 36rpx;
      }

      .period-option-title {
        font-weight: 500;
        font-size: 28rpx;
        color: #333333;
        line-height: 42rpx;
      }

      .period-option-first-month {
        font-weight: 400;
        font-size: 26rpx;
        color: #333333;
        line-height: 39rpx;
      }

      .period-option-month-interest {
        font-weight: 400;
        font-size: 22rpx;
        color: #999999;
        line-height: 33rpx;
      }
    }
  }

  .period-slider {
    margin: 34rpx auto 0;
    width: 114rpx;
    height: 8rpx;
    background: #f0f2fa;
    border-radius: 462rpx 462rpx 462rpx 462rpx;

    .period-slider-line {
      height: 8rpx;
      background: #2a6af7;
      border-radius: 462rpx 462rpx 462rpx 462rpx;
      transition:
        margin-left 0.3s ease,
        width 0.3s ease;
    }
  }
}

.mobile-input-container {
  padding: 40rpx 30rpx 20rpx;
  background: #ffffff;

  .mobile-input-item {
    width: 685rpx;
    height: 96rpx;
    background: #f5f8fd;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 2rpx solid #c1c5d7;
    padding: 0 36rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .mobile-input-label {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }

    .mobile-input {
      text-align: right;
      font-size: 28rpx;
      line-height: 39rpx;
    }
  }
}

.submit-button-container {
  padding: 0 30rpx;
  width: 748rpx;
  background: #ffffff;
  border-radius: 0rpx 0rpx 0rpx 0rpx;

  .submit-button {
    width: 686rpx;
    height: 100rpx;
    background: #2a6af7;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 60rpx;
  }
}

.agreement-checkbox-container {
  padding: 20rpx 50rpx;
  background-color: #fff;

  .agreement-checkbox {
    display: flex;
    align-items: center;
    gap: 10rpx;

    .agreement-checkbox-icon {
      width: 32rpx;
      height: 32rpx;

      &.checked {
        color: #2a6af7 !important;
      }
    }

    .agreement-checkbox-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 34rpx;

      .agreement-checkbox-text-item {
        color: #2a6af7;
      }
    }
  }
}

.download-app-container {
  padding: 80rpx 30rpx 56rpx;
  width: 100%;
  background-color: #fff;

  .download-app-image {
    width: 684rpx;
    height: 160rpx;
  }
}

.steps-container {
  margin: 0 auto;
  padding: 0 30rpx;
  background-color: #fff;

  .steps-title {
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 54rpx;

    &::before {
      content: '';
      width: 144rpx;
      height: 4rpx;
      background: linear-gradient(270deg, #c1c5d7 0%, rgba(255, 255, 255, 0) 100%);
      border-radius: 0rpx 0rpx 0rpx 0rpx;
    }

    &::after {
      content: '';
      width: 144rpx;
      height: 4rpx;
      background: linear-gradient(-270deg, #c1c5d7 0%, rgba(255, 255, 255, 0) 100%);
      border-radius: 0rpx 0rpx 0rpx 0rpx;
    }
  }

  .steps {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .step {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10rpx;

      .icon {
        width: 54rpx;
        height: 54rpx;
      }

      .step-description {
        font-weight: normal;
        font-size: 24rpx;
        color: #71788b;
        line-height: 34rpx;
      }
    }

    .step-icon {
      flex-shrink: 0;
      width: 60rpx;
      height: 60rpx;
    }
  }
}

.declaration-container {
  padding: 40rpx 30rpx 20rpx;
  background: #ffffff;
}

.customer-service-btn {
  position: fixed;
  right: 20rpx;
  width: 92rpx;
  height: 92rpx;
  transition: none;
  touch-action: none;

  .customer-service-btn-icon {
    width: 100%;
    height: 100%;
  }
}

.contact-us-popup {
  padding: 46rpx 50rpx 70rpx;
  position: relative;

  .close-btn {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .contact-title {
    margin-bottom: 10rpx;

    font-weight: 500;
    font-size: 38rpx;
    color: #333333;
    line-height: 55rpx;
    text-align: center;
  }

  .contact-desc {
    margin-bottom: 44rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #71788b;
    line-height: 41rpx;
    text-align: center;
  }

  .contact-content-container {
    margin-bottom: 20rpx;
    padding: 50rpx 24rpx;
    width: 562rpx;
    height: 170rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    display: flex;
    gap: 28rpx;

    .contact-icon {
      width: 72rpx;
      height: 72rpx;
      border-radius: 50%;
    }

    .contact-content {
      .contact-content-title {
        margin-bottom: 18rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
      }

      .contact-content-desc {
        font-weight: 400;
        font-size: 24rpx;
        color: #71788b;
      }
    }
  }
}

.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05f2447ceb6b45caaf742b8366205959.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}

.code-popup {
  position: relative;
  padding: 30rpx 50rpx 60rpx;

  .close-btn {
    position: absolute;
    right: 10rpx;
    top: 10rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .code-popup-title {
    margin-bottom: 10rpx;
    font-weight: 500;
    font-size: 38rpx;
    color: #333333;
    line-height: 55rpx;
  }

  .code-popup-phone {
    margin-bottom: 40rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #71788b;
    line-height: 35rpx;
  }

  .code-popup-input-container {
    width: 564rpx;
    height: 98rpx;
    background: #f5f6fa;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    padding: 30rpx 40rpx;
    font-weight: 400;
    font-size: 30rpx;
    line-height: 40rpx;
  }

  .code-popup-countdown-container {
    margin: 25rpx 0 35rpx;
    font-weight: 400;
    font-size: 30rpx;
    color: #2a6af7;
    line-height: 40rpx;
    text-align: right;
  }

  .code-popup-button {
    width: 562rpx;
    height: 98rpx;
    background: #2a6af7;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
  }
}
</style>
