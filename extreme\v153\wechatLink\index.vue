<template>
  <view class="page-container">
    <view class="content">
      <view class="title">您的专属领取码</view>

      <view class="code">{{ exclusiveCode }}</view>
    </view>

    <view class="step-container">
      <view class="step-item">
        <image class="step-item-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/90418c122373494cb04b84084adbf019.png" mode="scaleToFill" />
        <text class="step-item-text">复制链接到浏览器打开</text>
      </view>
      <view class="step-item">
        <image class="step-item-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/51770604591f48f18de597c886496ffb.png" mode="scaleToFill" />
        <text class="step-item-text">
          确认身份信息，领取您的
          <text class="step-item-text-bold">专属额度</text>
        </text>
      </view>
    </view>

    <view class="link-container">
      <view class="link-desc">复制链接到【浏览器】打开领取额度</view>
      <view class="link-url">
        <image src="https://cdn.oss-unos.hmctec.cn/common/path/2cd3cdc681324c04becd794779f2fa02.png" class="url-icon" />
        <text class="url-text" selectable="true">{{ shortLink || '链接加载中...' }}</text>

        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/47674047622e479abb6806155059ec5a.png"
          mode="scaleToFill"
          class="copy-tips"
        />
      </view>
      <view class="copy-btn" @click="copyShortLinkHandler">复制链接</view>
    </view>

    <FullScreenMatchLoading ref="loading" />

    <!-- 复制成功弹窗 -->
    <uni-popup
      ref="copySuccessPopup"
      type="center"
      :is-mask-click="false"
      background-color="transparent"
    >
      <view class="copy-success-popup">
        <image
          class="popup-main-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/db835c5700a84f0e95f4f49d43e56db8.png"
          mode="scaleToFill"
        />
        <image
          class="popup-close-btn"
          src="https://cdn.oss-unos.hmctec.cn/common/path/603a5777cf044cabb384d00a16fbfa15.png"
          mode="scaleToFill"
          @click="closeCopySuccessPopup"
        />
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { decryptByDES } from '@/utils/encrypt'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'

import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import { generateShortLink, copyShortLink, fetchOnlineProduct } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'wechatLinkIndex',
  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      productList: [],
      shortLink: '',
      exclusiveCode: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    // 生成随机四位数专属领取码
    this.generateExclusiveCode()
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      try {
        await this.fetchProduct()
      } catch (error) {
        console.error('获取产品数据失败:', error)
        // 如果获取产品失败，直接进入下一步流程
        this.navigateToNextFlow()
        return
      }
    }

    if (this.productList.length > 0) {
      if (isPageNested()) {
        await savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    } else {
      // 如果没有产品数据，自动进入下一步流程
      this.navigateToNextFlow()
    }

    // 获取短链接
    await this.fetchShortLink()
  },

  methods: {
    generateExclusiveCode() {
      // 生成4位随机数字作为专属领取码
      this.exclusiveCode = Math.floor(1000 + Math.random() * 9000).toString()
    },

    async getFlowData() {
      const flowData = getFlowData('wechat_link')
      if (flowData) {
        this.productList = flowData.productList || []

        if (!this.form.consumerId) {
          this.form.consumerId = this.productList[0]?.consumerId
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async fetchShortLink() {
      try {
        const res = await generateShortLink({
          consumerId: this.form.consumerId,
          channelId: this.form.channelId
        })

        if (res.code === 200 && res.data) {
          this.shortLink = res.data
        } else {
          console.error('获取短链接失败:', res.msg || '获取短链接失败')
        }
      } catch (error) {
        console.error('获取短链接接口调用失败:', error)
      }
    },

    copyShortLinkHandler() {
      if (!this.shortLink) {
        uni.showToast({
          title: '链接获取中，请稍后',
          icon: 'none'
        })
        return
      }

      // 复制到剪贴板
      uni.setClipboardData({
        data: this.shortLink,
        showToast: false,
        success: () => {
          // 显示复制成功弹窗
          this.$refs.copySuccessPopup.open()

          // 埋点上报
          this.reportCopyShortLink()
        },
        fail: (error) => {
          console.error('复制链接失败:', error)
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          })
        }
      })
    },

    async reportCopyShortLink() {
      try {
        await copyShortLink({
          consumerId: this.form.consumerId,
          channelId: this.form.channelId
        })
      } catch (error) {
        console.error('复制短链接埋点上报失败:', error)
      }
    },

    closeCopySuccessPopup() {
      this.$refs.copySuccessPopup.close()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #F3F5F9;
}

.content {
  padding: 80rpx 30rpx 60rpx;
  text-align: center;
  background: linear-gradient(180deg, #1678FF 0%, #F3F5F9 100%);

  .title {
    margin-bottom: 40rpx;
    font-weight: 500;
    font-size: 48rpx;
    color: #FFFFFF;
    line-height: 67rpx;
  }

  .code {
    display: inline-block;
    padding: 20rpx 60rpx;
    background: #FFFFFF;
    border-radius: 16rpx;
    font-weight: 700;
    font-size: 72rpx;
    color: #1678FF;
    line-height: 101rpx;
    letter-spacing: 8rpx;
  }
}

.step-container {
  padding: 60rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 40rpx;

  .step-item {
    display: flex;
    align-items: center;
    gap: 20rpx;

    .step-item-icon {
      width: 60rpx;
      height: 60rpx;
    }

    .step-item-text {
      font-weight: 400;
      font-size: 32rpx;
      color: #333333;
      line-height: 45rpx;

      .step-item-text-bold {
        font-weight: 700;
        color: #1678FF;
      }
    }
  }
}

.link-container {
  margin: 0 30rpx;
  padding: 40rpx 30rpx;
  background: #FFFFFF;
  border-radius: 16rpx;

  .link-desc {
    margin-bottom: 30rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 39rpx;
    text-align: center;
  }

  .link-url {
    position: relative;
    margin-bottom: 40rpx;
    padding: 20rpx;
    background: #F8F9FA;
    border-radius: 12rpx;
    border: 2rpx dashed #E5E5E5;
    display: flex;
    align-items: center;
    gap: 15rpx;

    .url-icon {
      width: 40rpx;
      height: 40rpx;
    }

    .url-text {
      flex: 1;
      font-weight: 400;
      font-size: 26rpx;
      color: #333333;
      line-height: 36rpx;
      word-break: break-all;
    }

    .copy-tips {
      position: absolute;
      top: -20rpx;
      right: -10rpx;
      width: 120rpx;
      height: 60rpx;
    }
  }

  .copy-btn {
    padding: 24rpx;
    background: #1678FF;
    border-radius: 12rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #FFFFFF;
    line-height: 45rpx;
    text-align: center;
  }
}

.copy-success-popup {
  position: relative;
  width: 600rpx;
  height: 400rpx;

  .popup-main-image {
    width: 100%;
    height: 100%;
  }

  .popup-close-btn {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    width: 60rpx;
    height: 60rpx;
  }
}
</style>
