<template>
  <view class="page-container">
    <AlertBar theme="orange" />

    <Header />

    <AmountInput :value.sync="form.demandAmount" :sliderValue.sync="form.demandAmountSlider" />

    <BorrowingOptions
      :demandAmount="form.demandAmount"
      :initialMonthIndex="form.monthIndex"
      @month-changed="handleMonthChanged"
    />

    <PhoneContainer
      v-model="form.phone"
      :agreementList="agreementList"
      :agreeStatus.sync="isAgree"
      @open-agreement="openAgreement"
      @get-quota="handleGetQuota"
    />

    <Declaration />

    <AgreementPopup ref="agreementPopup" :agreementList="agreementList" @agree="handleAgree" />

    <CodePopup
      ref="codePopup"
      :phone="form.phone"
      :channelId="form.channelId"
      @send-code="handleSendCode"
      @submit="handleCodeSubmit"
    />

    <RemindPopup ref="remindPopup" />
  </view>
</template>
<script>
import RemindPopup from '@/components/jqgj/index/RemindPopup.vue'
import { validateMobile } from '@/utils/validators'
import AlertBar from '@/components/alert/AlertBar.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { reportUV } from '@/apis/common'
import { saveLoan, sendSmsCode } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-jqgj.vue'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-ycl.vue'
import AmountInput from '@/components/jqgj/index/AmountInput.vue'
import BorrowingOptions from '@/components/jqgj/index/BorrowingOptions.vue'
import PhoneContainer from '@/components/jqgj/index/PhoneContainer.vue'
import CodePopup from '@/components/jqgj/index/CodePopup.vue'
import AgreementPopup from '@/components/jqgj/index/AgreementPopup.vue'
import { getAgreements } from '@/utils/agreement'

export default {
  name: 'template-v42',

  components: {
    AlertBar,
    Declaration,
    Header,
    RemindPopup,
    AmountInput,
    BorrowingOptions,
    PhoneContainer,
    CodePopup,
    AgreementPopup
  },

  data() {
    return {
      form: {
        demandAmount: 50000,
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        channelId: ''
      },
      isAgree: false,
      lockBack: null,
      agreementList: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
    }
    this.fetchAgreement()
    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
    async fetchAgreement() {
      // 获取协议列表
      this.agreementList = await getAgreements('key_h5_register')
    },

    handleMonthChanged(newMonthIndex) {
      this.form.monthIndex = newMonthIndex
    },

    openAgreement() {
      this.$refs.agreementPopup.open()
    },

    // 验证手机号格式
    validatePhoneFormat() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return false
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }

      return true
    },

    handleGetQuota(phone) {
      // 验证手机号
      if (!this.validatePhoneFormat()) {
        return
      }

      this.$refs.codePopup.open()
    },

    handleAgree() {
      this.isAgree = true
      this.handleGetQuota()
    },

    async handleSendCode() {
      // 验证手机号
      if (!this.validatePhoneFormat()) {
        return
      }

      const res = await sendSmsCode({
        phone: this.form.phone,
        channelId: this.form.channelId
      })

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '发送失败',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      // 发送成功后，通知验证码组件开始倒计时
      this.$refs.codePopup.startCountdown()
    },

    handleCodeSubmit(code) {
      throttle(() => {
        this.login(code)
      })
    },

    async getLoginParams(code) {
      const params = {}

      params.phoneBlack = getBlackPhone()
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = this.form.demandAmount
      params.phone = this.form.phone
      params.code = code
      params.channelId = this.form.channelId

      return params
    },

    async login(code) {
      const params = await this.getLoginParams(code)

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      // 登录成功后，记录手机号
      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)
      this.$u.vuex('vuex_consumerId', res.data)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: this.form.demandAmount,
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v42/auth/index?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #ff3154;

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/d016c5ae99db495284ba81601128f348.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}
</style>
