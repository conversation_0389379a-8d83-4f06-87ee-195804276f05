<template>
  <view class="header">
    <view class="header-title">
      <view class="title">还差最后一步 获取额度</view>
      <view class="sub-title">准确填写您的资质信息，获得精准产品推荐</view>
    </view>

    <view class="progress-container">
      <view class="progress-img">
        进度
        <text class="progress-img-text">99%</text>
      </view>
      <view class="progress-bar"></view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'UserUpdateHeader'
}
</script>

<style scoped lang="scss">
.header {
  padding: 40rpx 30rpx 60rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .header-title {
    .title {
      margin-bottom: 7rpx;
      font-weight: 700;
      font-size: 42rpx;
      color: #333333;
      line-height: 63rpx;
    }
    .sub-title {
      font-weight: 400;
      font-size: 24rpx;
      color: #56606a;
      line-height: 35rpx;
    }
  }

  .progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10rpx;

    .progress-img {
      width: 140rpx;
      height: 64.5rpx;
      background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/268046492ad6470aa43ec7e39b7d25fd.png);
      background-size: 100% 100%;
      background-repeat: no-repeat;
      display: flex;
      justify-content: center;
      padding-top: 7rpx;
      gap: 5rpx;

      font-weight: 400;
      font-size: 28rpx;
      color: #3d3d3d;
      line-height: 42rpx;

      .progress-img-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #28c445;
        line-height: 42rpx;
      }
    }
    .progress-bar {
      width: 140rpx;
      height: 24rpx;
      background: #28c445;
      border-radius: 370rpx 370rpx 370rpx 370rpx;
    }
  }
}
</style>
