<template>
  <v-mask
    :show="show"
    @close="bindCloseMask"
    :tabbarHeight="tabbarHeight"
    :navHeight="navHeight"
    :maskCloseAble="maskCloseAble"
  >
    <view class="dialog-wrap" @touchmove.stop.prevent="bindDefault">
      <view
        class="dialog"
        :class="{ 'dialog-bg': backgroundImage }"
        :style="{
          width: width,
          height: height,
          backgroundImage: 'url(' + backgroundImage + ')',
          backgroundColor: backgroundColor,
          borderRadius: radius
        }"
        @tap.stop.prevent="bindDefault"
        @touchmove.stop.prevent="bindDefault"
      >
        <view class="dialog-close" v-if="closeFoot">
          <text class="color_f iconfont icon-a-0 f20" @click="bindCloseDialog"></text>
        </view>
        <slot></slot>
      </view>
    </view>
  </v-mask>
</template>

<script>
import mask from '../mask/mask.vue'

export default {
  name: 'v-dialog',
  data() {
    return {
      show: false
    }
  },
  components: {
    'v-mask': mask
  },
  props: {
    value: {
      type: Object,
      default: () => {}
    },

    maskCloseAble: {
      type: Boolean,
      default: true
    },
    backgroundColor: {
      type: String,
      default: 'rgba(255,255,255,1)'
    },
    backgroundImage: {
      type: String,
      default: ''
    },
    radius: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: 'auto'
    },
    height: {
      type: String,
      default: 'auto'
    },
    closeFoot: {
      type: Boolean,
      default: true
    },
    tabbarHeight: {
      type: Number,
      default: 0
    },
    navHeight: {
      type: Number,
      default: 45
    }
  },
  watch: {
    value: {
      deep: true,
      handler(obj) {
        this.show = obj.show
      }
    }
  },
  created() {
    this.show = this.value.show
    this.init()
  },
  methods: {
    /**
     *  @param { Function } init 初始化
     */
    init() {
      // this.show = this.value
    },

    /**
     *  @param { Function } bindCloseMask 关闭弹窗
     */
    bindCloseMask(e) {
      this.$emit('close', e)
    },

    /**
     *  @param { Function } bindCloseDialog 关闭弹窗
     */
    bindCloseDialog() {
      this.$emit('close', false)
    },

    /**
     *  @param { Function } bindDefault 阻止冒泡 && 默认事件
     */
    bindDefault() {
      return false
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;
}
.dialog-bg {
  background-size: 100% 100%;
  background-repeat: no-repeat;
}
.dialog {
  position: relative;
}
.dialog-close {
  width: 40rpx;
  height: 40rpx;
  margin-top: 40rpx;
  position: absolute;
  top: -100rpx;
  right: -40rpx;
  .err-close {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>
