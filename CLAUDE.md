# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 uni-app 的多端金融H5应用，专为小马信融（xiaoma-xinrong）贷款业务打造。项目支持微信小程序、H5、微信公众号等多端部署，包含完整的贷款申请流程。

## 核心架构

### 技术栈
- **框架**: uni-app (Vue 2)
- **构建**: Vue CLI + Webpack
- **样式**: SCSS
- **状态管理**: Vuex
- **包管理**: npm

### 多版本管理系统
项目采用版本号管理不同业务页面，当前支持 v1-v148 版本，每个版本对应不同的产品或客户需求。新版本页面位于 `extreme/` 分包中。

### 目录结构
```
/components/
  /template/       # 模板首页组件
    v66.vue        # v66版本的首页组件
    v67.vue        # v67版本的首页组件
    ...            # 其他版本首页
  /common/         # 通用组件
  /{businessName}/ # 业务组件 (axfq, cbb, dai-yue-tong, hyh, jqgj等)

/extreme/          # 分包流程页面 (v1-v148)
  /v66/            # v66版本的流程页面
    /auth/         # 认证页面  
    /apply/        # 申请页面
    /applyOther/   # 其他申请页面
    /download/     # 下载页面
    /qw/          # 企微页面
    /webviewDown/  # WebView下载页面
    /wechatOfficialAccount/ # 微信公众号页面
  /v67/            # v67版本的流程页面
    ...
    
/apis/             # API接口层
/utils/            # 工具函数库
/store/            # Vuex状态管理
/static/           # 静态资源
```

## 开发命令

```bash
# 代码格式化
npm run format
```

注意：项目中只配置了格式化命令，开发和构建命令可能通过其他方式执行。

## 业务流程

### 项目沟通术语
在与团队沟通时，需要了解以下常用术语：

- **首页/注册页** → `components/template/v{版本号}.vue`
- **留资页面** → `extreme/v{版本号}/auth/index.vue`
- **线下产品页/表单产品页** → `extreme/v{版本号}/apply/index.vue`
- **贷超产品页/老贷超页面** → `extreme/v{版本号}/applyOther/index.vue`
- **企微页/老企微页/A企微页** → `extreme/v{版本号}/qw/index.vue`
- **新企微/B企微页** → `extreme/v{版本号}/wechatLow/index.vue`
- **结束页/下载页** → `extreme/v{版本号}/download/index.vue`
- **半流程入口页面/中转页** → `extreme/v{版本号}/halfApiTransitStation/index.vue`
- **贷超申请之后页面/贷超内嵌页/贷超嵌套页** → `extreme/v{版本号}/overloanWebview/index.vue`
- **用户补充资质/用户补充信息/用户更新资质页面** → `extreme/v{版本号}/userUpdate/index.vue`
- **半流程产品申请之后的页面/半流程内嵌页/半流程嵌套页** → `extreme/v{版本号}/webviewDown/index.vue`
- **企微链接页/企微复制链接页** → `extreme/v{版本号}/wechatLink/index.vue`
- **smallLoan页/小贷超页面** → `extreme/v{版本号}/smallLoan/index.vue`
- **公众号页面/公众号引导页面/公众号二维码页面** → `extreme/v{版本号}/wechatOfficialAccount/index.vue`
- **微信小程序中内嵌的公众号引导关注页面** → `fullScreen/image/index.vue`
- **短链页面/老的短链页面/旧的短链页面** → `s/index.vue`
- **新短链页面/通用短链页面** → `s2/index.vue`

### 核心业务页面类型
- **auth** - 用户认证页面 (留资页面)
- **apply** - 贷款申请页面 (线下产品页/表单产品页)
- **applyOther** - 其他申请页面 (贷超产品页/老贷超页面)
- **qw** - 企业微信页面 (企微页/老企微页/A企微页)
- **wechatLow** - 微信页面 (新企微/B企微页)
- **download** - APP下载引导页 (结束页/下载页)
- **halfApiTransitStation** - API中转页面 (半流程入口页面/中转页)
- **overloanWebview** - 贷超嵌套页面 (贷超申请之后页面/贷超内嵌页/贷超嵌套页)
- **userUpdate** - 用户信息更新页面 (用户补充资质/用户补充信息/用户更新资质页面)
- **webviewDown** - WebView内下载页面 (半流程产品申请之后的页面/半流程内嵌页/半流程嵌套页)
- **wechatLink** - 微信链接页面 (企微链接页/企微复制链接页)
- **smallLoan** - 小贷超页面 (smallLoan页/小贷超页面)
- **wechatOfficialAccount** - 微信公众号页面 (公众号页面/公众号引导页面/公众号二维码页面)

### 特殊页面
- **短链页面** - `s/index.vue` (老的短链页面/旧的短链页面)
- **新短链页面** - `s2/index.vue` (通用短链页面)
- **公众号引导关注页面** - `fullScreen/image/index.vue` (微信小程序中内嵌)
- **话务系统中转页** - `transit/v1/index.vue` (针对话务系统使用)
- **公众号业务中转页** - `transit/v2/index.vue` (针对公众号业务使用)

### 版本管理注意事项
1. 每个版本的**首页组件**位于 `components/template/v{版本号}.vue`
2. 每个版本的**流程页面**位于 `extreme/v{版本号}/` 目录下
3. 例如v66版本：
   - 首页：`components/template/v66.vue`
   - 认证页：`extreme/v66/auth/index`
   - 申请页：`extreme/v66/apply/index`
4. 新增功能时需要考虑版本兼容性
5. 页面路径格式: `/extreme/v{版本号}/{页面类型}/index`

## 状态管理

### Vuex Store
- **状态持久化配置**: `store/saveStateKeys.js` - 配置自动保存到本地的状态key字段
- **简约API支持**: `store/$u.mixin.js` - 提供Vuex读写的简约API，具体用法查看此文件
- 项目使用Vuex进行状态管理，部分状态会自动保存到本地存储

## 关键依赖和工具

### 核心依赖
- `@fingerprintjs/fingerprintjs` - 设备指纹识别
- `crypto-js` - 加密处理
- `js-md5` - MD5加密
- `dayjs` - 日期处理
- `html2canvas` - 截图功能
- `qrcode.vue` - 二维码生成
- `vconsole` - 调试控制台

### 工具函数 ($u)
项目在Vue原型上挂载了 `$u` 工具集：
- `$u.test` - 测试工具
- `$u.debounce` - 防抖
- `$u.throttle` - 节流
- `$md5` - MD5加密

### 核心工具函数库
项目在 `utils/` 目录下提供了丰富的工具函数：

#### 安全相关工具
- **encrypt.js** - DES加密/解密工具，支持字符串和对象加密
- **fingerprint.js** - 设备指纹识别，获取唯一设备标识
- **iframe.js** - iframe嵌套检测工具，提供多种检测方法
- **black-phone.js** - 黑名单手机号管理，支持加密存储

#### 数据处理工具
- **validators.js** - 表单验证工具（手机号、身份证、姓名、数字验证）
- **amount.js** - 金额格式化工具，支持千分位显示和解析
- **queryParams.js** - URL参数处理，支持加密传输
- **agreement.js** - 协议管理工具，支持动态获取和模板替换

#### 用户体验工具
- **throttle.js** / **debounce.js** - 防抖节流工具
- **lock-native-back.js** - 原生返回键锁定，防止误触返回
- **layout.js** - 响应式单位转换（rpx/px）
- **user-agent.js** - 环境检测（微信小程序、微信浏览器）

#### 业务逻辑工具
- **processFlowFunctions.js** - 流程节点管理核心工具
- **ad-referrer.js** - 广告来源追踪
- **utils.js** - 通用工具函数集合

## 代码规范

### 样式系统
- 使用SCSS进行样式开发
- **动态主题样式**: `theme/` 目录 - 部分模板使用动态样式，样式代码存放在此目录
- 全局使用自定义导航栏 (`"navigationStyle": "custom"`)

### 开发插件
- 开发环境启用 `code-inspector-plugin` 进行代码审查
- 使用 Prettier 进行代码格式化

### 安全防护体系
项目建立了完善的安全防护机制：

#### 数据安全
- **DES加密系统**: 使用 `crypto-js` 实现数据加密传输和存储
- **URL参数加密**: 敏感参数通过 `encodeUrlParam` 加密传输
- **本地存储加密**: 黑名单手机号等敏感数据加密存储
- **MD5签名**: 关键数据使用MD5进行完整性校验

#### 环境安全
- **设备指纹识别**: 使用 `@fingerprintjs/fingerprintjs` 生成唯一设备标识
- **反嵌套检测**: 多重检测机制防止页面被恶意iframe嵌套
  - `checkIframeNestingBySelfTop()` - 通过window.self和window.top比较
  - `checkIframeNestingByParent()` - 通过window和window.parent比较
  - `checkIframeNestingByFrameElement()` - 通过window.frameElement检测
- **环境识别**: 准确识别微信小程序、微信浏览器等运行环境

#### 访问控制
- **黑名单机制**: 自动记录和管理问题手机号
- **访问记录上报**: 小程序访问、页面嵌套等行为自动上报
- **原生返回锁定**: 防止用户误触系统返回键

## 特殊配置

### 分包加载策略
- **主包**: 包含核心页面和基础功能
- **extreme分包**: 包含所有版本业务页面（v1-v152）
- **功能性分包**:
  - `fullScreen` - 全屏展示页面
  - `wx-guide` - 微信引导页面
  - `playground` - 测试和演示页面
  - `transit` - 中转页面（话务系统、公众号业务）

### 多环境配置
项目支持多环境部署和开发：

#### 环境配置 (utils/request.js)
- **生产环境**: `https://api.whxmxr.cn/h5/api`
- **贷悦通生产**: `https://dytapi.helianshengkj.cn/h5/api`
- **测试环境**: `http://test.k8.whxmxr.cn/api`
- **本地开发**: 支持多个开发者局域网IP配置
- **Mock环境**: 支持本地Mock数据调试

#### 测试支持
- **测试API**: `apis/test.js` 提供完整的测试环境接口
- **直接注册**: 测试环境支持无验证码注册
- **产品匹配测试**: 支持半API、线上、出量产品的测试匹配
- **申请记录管理**: 测试环境可管理当天申请的产品记录

## 开发注意事项

1. **版本管理**: 新增页面时确认版本号，避免冲突
2. **组件复用**: 优先使用已有组件，按业务模块组织新组件
3. **多端兼容**: 代码需要考虑H5、微信小程序等多端兼容性
4. **安全规范**: 涉及用户数据时使用相应的加密工具
5. **性能优化**: 大量版本页面使用分包加载策略
6. **表单验证**: 使用 `utils/validators.js` 进行统一的数据验证
7. **用户体验**: 合理使用防抖节流，避免重复操作
8. **环境适配**: 根据运行环境（小程序/H5/微信）调整功能实现

## 核心业务API和流程管理

### 流程节点配置系统
- **流程节点可配置**: 使用 `fetchFlow` 请求接口返回流程节点数组，格式为 `['wechat', 'overloan', 'halfapi', 'wechat_official_account']`
- **流程节点保存**: 使用 `setFlowNodes` 保存流程节点到本地存储供后续使用
- **API位置**: `fetchFlow` 定义在 `apis/common-2.js`，`setFlowNodes` 定义在 `utils/processFlowFunctions.js`

```javascript
// 获取并保存流程节点示例
const response = await fetchFlow({ channelId: this.channelId })
if (response.code === 200) {
  setFlowNodes(response.data)
}
```

### 环境检测和上报系统

#### 小程序上报机制
- **检测函数**: `isInWechatMiniProgram` - 判断是否在微信小程序内嵌环境
- **上报函数**: `saveAppletOpenRecord` - 小程序访问记录上报
- **触发条件**: 检测到小程序环境时自动上报
- **API位置**: 检测函数在 `utils/user-agent.js`，上报函数在 `apis/common-3.js`

```javascript
// 小程序上报示例
if (isInWechatMiniProgram()) {
  await saveAppletOpenRecord({
    channelId: this.form.channelId,
    appUserId: this.form.consumerId
  })
}
```

#### 嵌套页面上报机制  
- **检测函数**: `isPageNested` - 判断网页是否被iframe内嵌
- **上报函数**: `savePageNestingOpenRecord` - 页面嵌套访问记录上报
- **触发条件**: 检测到iframe嵌套时自动上报
- **API位置**: 检测函数在 `utils/iframe.js`，上报函数在 `apis/common-3.js`

```javascript
// 嵌套上报示例
if (isPageNested()) {
  await savePageNestingOpenRecord({
    appUserId: this.form.consumerId,
    channelId: this.form.channelId,
    deviceType: uni.getSystemInfoSync().osName,
    browser: uni.getSystemInfoSync().browserName
  })
}
```

### 企微页面配置规则

#### 企微A面 (qw页面) 特性
- **无取消按钮版本**: 部分版本删除了取消按钮，如v148版本改为"额度够用"按钮
- **a标签跳转**: 领取按钮使用 `<a>` 元素实现外部跳转，而非普通点击事件
- **可配置自动跳转**: 调用 `fetchWechatAutoJump` 获取自动跳转配置，返回值2表示自动跳转
- **可配置自动跳转时间**: 在自动跳转开启后，调用 `fetchWechatAutoTime` 获取倒计时秒数

```javascript
// 企微A面自动跳转实现
const jumpRes = await fetchWechatAutoJump({
  channelId: this.form.channelId,
  consumerId: this.form.consumerId
})
if (jumpRes.data == 2) { // 开启自动跳转
  const timeRes = await fetchWechatAutoTime({
    channelId: this.form.channelId,
    consumerId: this.form.consumerId
  })
  this.confirmCountdownNumber = timeRes.data // 设置倒计时
  this.timerStart() // 启动定时器
}
```

#### 企微B面 (wechatLow页面) 特性
- **a标签跳转**: 同企微A面，使用 `<a>` 元素实现领取按钮外部跳转
- **可配置自动跳转**: 使用相同的 `fetchWechatAutoJump` API，但需传递 `matchLowWechatFlag: 1` 参数
- **可配置自动跳转时间**: 使用相同的 `fetchWechatAutoTime` API，同样需要 `matchLowWechatFlag: 1` 参数

```javascript
// 企微B面自动跳转实现（与A面的区别在于多了matchLowWechatFlag参数）
const jumpRes = await fetchWechatAutoJump({
  channelId: this.form.channelId,
  consumerId: this.form.consumerId,
  matchLowWechatFlag: 1 // B企微页面标识
})
```

#### 企微检测是否结束
- **检测功能**: 在 `qw` 或 `wechatLow` 页面调用 `checkProductSequenceEnd` 接口检测企微产品流程是否结束
- **触发时机**: 页面 `onShow` 生命周期首先执行此检测
- **处理逻辑**: 
  - 如果流程结束 (`isEnd = true`)，直接调用 `navigateToNextFlow()` 进入下一个流程节点
  - 如果流程未结束 (`isEnd = false`)，继续执行当前页面的业务逻辑（获取产品、配置自动跳转等）
- **参数差异**:
  - **企微A面** (`qw`页面): 调用时不需要 `matchLowWechatFlag` 参数
  - **企微B面** (`wechatLow`页面): 调用时需要传递 `matchLowWechatFlag: 1` 参数

```javascript
// 企微A面检测流程是否结束
async checkProductSequenceEnd() {
  const res = await checkProductSequenceEnd({
    channelId: this.form.channelId,
    consumerId: this.form.consumerId
  })
  return res.data
}

// 企微B面检测流程是否结束
async checkProductSequenceEnd() {
  const res = await checkProductSequenceEnd({
    channelId: this.form.channelId,
    consumerId: this.form.consumerId,
    matchLowWechatFlag: 1 // B面标识
  })
  return res.data
}

// 页面onShow中的标准流程
async onShow() {
  // 1. 判断当前流程是否结束
  const isEnd = await this.checkProductSequenceEnd()
  
  if (isEnd) {
    this.navigateToNextFlow()
    return
  }
  // 2. 继续页面业务逻辑...
}
```

#### 企微页面核心API
- **checkProductSequenceEnd** - 位于 `apis/common-2.js`，检测企微产品流程是否结束
- **fetchWechatAutoJump** - 位于 `apis/common-2.js`，获取自动跳转开关配置
- **fetchWechatAutoTime** - 位于 `apis/common-2.js`，获取自动跳转倒计时时间
- **getApplyOnlineProductLink** - 生成外部跳转链接，用于a标签href属性

### 业务术语对应关系
- **表单申请页**: 指代 `apply` 页面类型 (`extreme/v{版本号}/apply/index.vue`)
- **企微A面**: 指代 `qw` 页面类型 (`extreme/v{版本号}/qw/index.vue`)
- **企微B面**: 指代 `wechatLow` 页面类型 (`extreme/v{版本号}/wechatLow/index.vue`)
- **流程节点类型**: `wechat`、`overloan`、`halfapi`、`wechat_official_account`、`wechat_link` 等
- **上报类型**: 小程序上报、嵌套上报、设备指纹上报等多种数据采集机制

### 贷超页面配置规则 (applyOther页面)

#### 贷超自动申请配置
- **贷超可配置自动跳**: 在 `applyOther` 页面中调用 `getDcAutoFlag` 请求来判断是否自动申请产品
- **API位置**: `getDcAutoFlag` 定义在相关API文件中
- **功能说明**: 当开启自动申请时，页面会自动触发产品申请流程，无需用户手动点击

#### 进贷超-返回继续跳功能
- **页面路径**: 在 `applyOther` 页面申请产品后，进入 `overloanWebview` 页面
- **功能实现**: `overloanWebview` 页面顶部导航栏左上角有按钮（非a标签），点击会获取下一个产品并打开链接
- **交互方式**: 普通按钮点击事件，通过JavaScript获取产品信息并进行页面跳转

#### 进贷超-返回继续跳-a标签版本
- **基于功能**: 基于"进贷超-返回继续跳"功能的a标签版本
- **区别**: 左上角按钮使用 `<a>` 标签实现，而非JavaScript点击事件
- **应用场景**: 适用于需要外部链接跳转的场景

#### 公众号页面说明
- **公众号指代**: 专指公众号页面，对应 `wechatOfficialAccount` 页面类型
- **页面路径**: `extreme/v{版本号}/wechatOfficialAccount/index.vue`
- **功能**: 引导用户关注微信公众号，显示二维码等相关信息

### 协议管理系统
项目提供了完善的协议管理功能：

#### 协议组件规则
- **贷超协议**: 贷超产品页 (applyOther页面) 使用 `AgreementFooter` 组件显示协议，通过 `agreementKey` 属性获取对应协议内容
  - 示例：`<AgreementFooter agreementKey="hyh-dc" @agree="handleAgreementAgree" />`
  - 组件位置：底部固定定位显示

#### 协议动态管理 (utils/agreement.js)
- **动态获取**: 通过 `getAgreements(agreementKeys, replacements)` 获取协议内容
- **模板替换**: 支持占位符替换，如用户手机号、UUID、授权日期等
- **多协议支持**: 可同时处理多个协议文档
- **默认替换数据**: 自动从本地存储获取用户信息进行替换

```javascript
// 协议使用示例
const agreements = await getAgreements(['user_agreement', 'privacy_policy'], {
  userName: '张三',
  phone: '***********'
})
```

## 调试和维护

### 开发调试工具
- **VConsole**: 开发环境可启用移动端调试控制台
- **代码审查**: 集成 `code-inspector-plugin` 进行代码质量检查
- **代码格式化**: 使用 `npm run format` 保持代码格式一致性
- **多环境切换**: 支持生产、测试、本地等多环境快速切换

### 代码组织原则
- **API集中管理**: 所有接口调用集中在 `apis/` 目录
- **工具函数模块化**: 按功能分类组织在 `utils/` 目录
- **组件业务分离**: 通用组件和业务组件分别管理
- **版本隔离**: 不同版本的页面和样式完全隔离

### 性能监控和优化
- **分包加载**: 合理的分包策略减少首屏加载时间
- **状态持久化**: 关键状态自动保存到本地存储
- **防抖节流**: 防止用户重复操作和频繁请求
- **资源优化**: 图片和静态资源使用CDN加速

### 数据监控体系
- **用户行为追踪**: 记录用户访问路径和操作行为
- **环境信息收集**: 自动收集设备、浏览器、网络等环境信息
- **异常情况上报**: 嵌套访问、异常操作等情况自动上报
- **业务数据统计**: 申请转化率、流程完成率等业务指标监控

## 短链系统和特殊页面

### 短链跳转系统
项目提供了两套短链系统：

#### 老版短链系统 (s/index.vue)
- **功能**: 处理旧版短链跳转逻辑
- **使用场景**: 兼容历史短链访问

#### 新版通用短链系统 (s2/index.vue)
- **功能**: 通过 `getShortRealLink` API获取真实链接并跳转
- **参数**: 接收 `u` 参数作为短链标识
- **实现**: 自动解析短链并重定向到目标页面

```javascript
// 短链跳转实现
async getUrl(u) {
  const res = await getShortRealLink({ u })
  if (res.code == 200 && res.data) {
    window.location.href = res.data
  }
}
```

### 中转页面系统
项目提供了专门的中转页面处理不同业务场景：

#### 话务系统中转页 (transit/v1/index.vue)
- **功能**: 专门为话务系统设计的验证码输入页面
- **特性**: 4位验证码输入，自动完成验证
- **组件**: 使用 `vcode-input` 组件实现验证码输入
- **API**: 通过 `fetchRegisterLink` 验证邀请码

#### 公众号业务中转页 (transit/v2/index.vue)
- **功能**: 处理公众号相关业务跳转
- **使用场景**: 公众号菜单点击、消息回复等场景

### 全屏展示系统
#### 公众号引导关注页面 (fullScreen/image/index.vue)
- **功能**: 在微信小程序中内嵌显示公众号引导关注页面
- **使用场景**: 引导小程序用户关注公众号
- **展示方式**: 全屏图片展示模式

### 微信引导系统 (wx-guide/)
- **official-account.vue**: 公众号关注引导页面
- **open-in-browser.vue**: 引导用户在浏览器中打开页面

这些特殊页面和系统共同构成了完整的用户引导和跳转体系，确保用户能够在不同场景下顺畅地完成业务流程。