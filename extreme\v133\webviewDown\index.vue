<template>
  <view class="container">
    <view class="cover" v-if="show">
      <uni-icons type="info-filled" size="28" color="#FFFFFF"></uni-icons>
      <text>当前为第三方网页，请注意核实信息谨防上当受骗</text>
    </view>
    <web-view :src="url" class="wb-view"></web-view>
    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { nextFlowNode } from '@/utils/processFlowFunctions'
import { getHalfApiProgress } from '@/apis/common'

export default {
  name: 'webviewDown',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      show: true,
      url: '',
      form: {},
      timer: null
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (this.vuex_webviewDown.url) {
        this.url = this.vuex_webviewDown.url
      }

      this.startPolling()
    } else {
      this.navigateToEnd()
    }

    setTimeout(() => {
      this.show = false
    }, 3000)
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
  },

  methods: {
    startPolling() {
      this.checkProgress()
      this.timer = setInterval(() => {
        this.checkProgress()
      }, 2000)
    },

    async checkProgress() {
      const res = await getHalfApiProgress({
        consumerId: this.form.consumerId
      })
      // 0 待回调 1 已回调
      if (res.data == 1) {
        clearInterval(this.timer)
        this.timer = null
        this.navigateToNextFlow()
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v133/apply/index',
        wechat: '/extreme/v133/qw/index',
        overloan: '/extreme/v133/applyOther/index',
        end: '/extreme/v133/download/index',
        halfapi: '/extreme/v133/webviewDown/index',
        wechat_official_account: '/extreme/v133/wechatOfficialAccount/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      if (nextFlow.flow === 'end') {
        if (this.vuex_halfJumpUrl) {
          window.location.href = this.vuex_halfJumpUrl
          return
        }
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    navigateToEnd() {
      const path = '/extreme/v133/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss">
.container {
  .cover {
    width: 100vw;
    position: fixed;
    z-index: 999;
    padding: 16rpx 32rpx;
    left: 0;
    top: 0;
    background: linear-gradient(90deg, #44c5ad 0%, #42d794 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12rpx;

    text {
      font-size: 26rpx;
      color: #ffffff;
      font-weight: 400;
      line-height: 38rpx;
    }
  }
}
</style>
