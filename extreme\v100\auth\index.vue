<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { nextFlowNode, setFlowIndex, setFlowNodes } from '@/utils/processFlowFunctions'

export default {
  name: 'AuthIndex',

  data() {
    return {
      form: {
        templateVersion: 'v100',
        consumerId: '',
        channelId: '',
        identity: ''
      },
      navigateTimer: null
    }
  },

  onUnload() {
    if (this.navigateTimer) {
      clearInterval(this.navigateTimer)
    }
  },

  async onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.form.consumerId = this.$route.query.consumerId
    this.form.channelId = this.$route.query.channelId
    this.form.identity = this.$route.query.identity

    this.$u.vuex('vuex_consumerId', this.form.consumerId)
    this.$u.vuex('vuex_channelId', this.form.channelId)

    console.log('form', this.form)

    setFlowNodes(['wechat'])
    this.clickSubmit()
  },

  methods: {
    async clickSubmit() {
      setFlowIndex(0)
      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      const routeMap = {
        offline: '/extreme/v100/apply/index',
        wechat: '/extreme/v100/qw/index',
        overloan: '/extreme/v100/applyOther/index',
        end: '/extreme/v100/download/index'
      }

      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      console.log('nextFlow', nextFlow)

      if (nextFlow.flow === 'halfapi') {
        window.location.href = nextFlow.url
        return
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = {
        consumerId: this.form.consumerId,
        channelId: this.form.channelId,
        identity: this.form.identity
      }

      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<template>
  <view class="page-container">
    <view>留资</view>
    <view class="page-footer">
      <view class="submit-btn" @click="clickSubmit">开始匹配</view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
