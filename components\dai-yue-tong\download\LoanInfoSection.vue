<template>
  <view class="loan-info-section">
    <view class="loan-call-label">请注意接听来电</view>
    <view class="loan-label">预计最高可借（元）</view>
    <view class="loan-amount">200,000</view>
    <view class="loan-desc">专业顾问为您服务 最快1分钟审核完成</view>
    <!-- 分隔线 -->
    <view class="divider"></view>
  </view>
</template>

<style lang="scss" scoped>
.loan-info-section {
  position: relative;
  margin: 40rpx auto 20rpx;
  padding: 116rpx 0 50rpx;
  width: 686rpx;
  background: #ffffff;
  box-shadow: 0rpx 8rpx 20rpx 0rpx rgba(0, 0, 0, 0.08);
  border-radius: 8rpx 8rpx 8rpx 8rpx;

  .loan-call-label {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 432rpx;
    height: 74rpx;
    background: linear-gradient(359deg, #40bdb7 9%, #6ed3d4 100%);
    box-shadow: inset 0rpx -4rpx 12rpx 0rpx rgba(0, 0, 0, 0.09);
    font-weight: 700;
    color: #ffffff;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0 0 40rpx 40rpx;
  }

  .loan-label {
    text-align: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #1a1a1a;
    line-height: 40rpx;
  }

  .loan-amount {
    margin-top: 6rpx;
    margin-bottom: 28rpx;
    text-align: center;
    font-family: DIN, DIN;
    font-weight: 500;
    font-size: 108rpx;
    color: #1a1a1a;
    line-height: 130rpx;
  }

  .loan-desc {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 28rpx;
    color: #c97f00;
    line-height: 40rpx;
    width: 512rpx;
    height: 50rpx;
    background: #fff9ea;
    border-radius: 904rpx 904rpx 904rpx 904rpx;
  }
}
</style>
