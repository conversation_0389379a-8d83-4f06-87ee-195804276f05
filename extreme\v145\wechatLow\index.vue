<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="main-title">用微信借钱花，随借随还</view>

    <view class="feature-tags">
      <text class="feature-tag">消费可用</text>
      <text class="feature-tag">按日计息</text>
      <text class="feature-tag">随时可还</text>
    </view>

    <view class="credit-label">我的额度</view>
    <view class="credit-amount">
      <text class="currency-symbol">￥</text>
      <text class="amount-number">200000</text>
    </view>

    <view class="promotion-text">限时享3张7天免息券</view>
    <a class="apply-btn" :href="applyUrl" target="_blank" v-if="applyUrl">
      立即领取
    </a>

    <view class="security-notice">
      <text>防诈骗提醒</text>
      <image src="https://cdn.oss-unos.hmctec.cn/common/path/4fcf0fb9c51d4fef98da21f10e1bab2f.png" class="arrow-icon" />
    </view>
    <view class="service-provider">由蚂蚁集团及合作机构提供服务</view>

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyOnlineProduct, fetchOnlineProduct, fetchWechatAutoJump, checkProductSequenceEnd } from '@/apis/common-2'

import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getApplyOnlineProductLink, matchingOnlineProduct } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'qwIndex',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      productList: [],
      // 企微自动跳转: 1 为不自动跳转 2为自动跳转
      wechatAutoJump: '',
      // 跳转链接
      applyUrl: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (isPageNested()) {
        savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    }
  },

  async onShow() {
    const isEnd = await this.checkProductSequenceEnd()

    if (isEnd) {
      this.navigateToNextFlow()
      return
    }

    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      this.applyUrl = getApplyOnlineProductLink({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId,
        redirectUrl: encodeURIComponent(window.location.href)
      })
    } else {
      this.navigateToNextFlow()
    }
  },

  methods: {
    async checkProductSequenceEnd() {
      const res = await checkProductSequenceEnd({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchLowWechatFlag: 1
      })

      return res.data
    },

    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 1 不自动跳转 2自动跳转
      this.wechatAutoJump = res.data || 1
    },



    async getFlowData() {
      const flowData = getFlowData('wechat_low')
      if (flowData) {
        this.productList = flowData.productList

        if (!this.form.consumerId) {
          this.form.consumerId = this.productList[0].consumerId
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1,
        matchLowWechatFlag: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      clearFlowData('wechat_low')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },



    clickConfirm() {
      throttle(this.confirmHandler)
    },

    confirmHandler() {
      this.applyProduct()
    },

    clickCancel() {
      throttle(this.cancelHandler)
    },

    cancelHandler() {
      this.navigateToNextFlow()
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: url("https://cdn.oss-unos.hmctec.cn/common/path/4941984ca6b74aac9d05626ec859b259.png") no-repeat;
  background-size: 100% 100%;
  padding-top: 187rpx;
  padding-bottom: 100rpx;
}

.main-title {
  margin-bottom: 20rpx;
  font-weight: 500;
  font-size: 52rpx;
  color: #FFFFFF;
  line-height: 78rpx;
  text-align: center;

}

.feature-tags {
  margin-bottom: 175rpx;
  text-align: center;
}

.feature-tag {
  font-weight: 400;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 45rpx;
}

.feature-tag:not(:last-child)::after {
  content: '';
  display: inline-block;
  width: 6rpx;
  height: 6rpx;
  background-color: #FFFFFF;
  border-radius: 50%;
  margin-left: 16rpx;
  margin-right: 16rpx;
  vertical-align: middle;
}

.credit-label {
  margin-bottom: 15rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 48rpx;
  text-align: center;

}

.credit-amount {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.currency-symbol {
  font-weight: 700;
  font-size: 98rpx;
  color: #FFFFFF;
}

.amount-number {
  font-family: Arial, Helvetica, sans-serif;
  font-weight: 700;
  font-size: 128rpx;
  color: #FFFFFF;
}

.promotion-text {
  margin-top: 314rpx;
  margin-bottom: 43rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFFFFF;
  line-height: 48rpx;
  text-align: center;
}

.apply-btn {
  margin: 0 auto 66rpx;
  width: 428rpx;
  height: 103rpx;
  background: #FFFFFF;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 40rpx;
  color: #06B058;
  line-height: 60rpx;
  text-decoration: none;
}



.security-notice {
  margin-bottom: 25rpx;
  font-weight: 500;
  font-size: 28rpx;
  color: #FFFFFF;
  line-height: 35rpx;
  display: flex;
  align-items: center;
  justify-content: center;

  .arrow-icon {
    width: 8rpx;
    height: 16rpx;
    margin-left: 12rpx;
  }
}

.service-provider {

  font-weight: 400;
  font-size: 20rpx;
  color: #8FE4B2;
  line-height: 25rpx;
  letter-spacing: 3px;
  text-align: center;
}
</style>
