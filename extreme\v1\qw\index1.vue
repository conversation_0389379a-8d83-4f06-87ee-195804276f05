<template>
  <view class="content">
    <view class="footer">
      <view class="flex-row-cc flex-wrap color_6 f12">
        <view>本人已知晓</view>
        <view
          class="color-link"
          v-for="item in authProtocol"
          :key="item.protocolId"
          @click="openProtocol(item)"
          >《{{ item.name }}》</view
        >
        <view class="">的</view>
      </view>
      <view class="flex-row-cc flex-wrap color_6 f12">
        所有内容，同意并授权平台推荐/匹配多个产品</view
      >
      <view class="btn flex-row-cc color_f f20 mt-12" @click="hanldeqw"
        >立即领取 <span v-if="this.timers">({{ this.timer }}S)</span></view
      >
      <view class="btn-cancale" @click="handleCancel"> 取消 </view>
      <view class="line flex-row-cc color_6 f13 mt-12">
        <image
          class="f-image mr-4"
          src="https://cdn.oss-unos.hmctec.cn/app/statis/app/202402280002.png"
          mode=""
        ></image>
        <view>使用您本人微信验证后才可领取额度</view>
      </view>
      <!-- <view class="f12 color_9 flex-row-cc mt-17">具体放款结果和额度由第三方服务公司提供</view>
			<view class="f12 color_9 flex-row-cc">本平台仅提供推荐服务</view> -->
    </view>

    <v-drawer
      v-model="protocolVisible"
      @close="protocolVisible = false"
      direction="bottom"
      width="100vw"
      height="auto"
      backgroundImage="orange"
      radius="32rpx 32rpx 0 0"
      class="protocol-detail-drawer"
    >
      <view class="protocol-detail-drawer-container">
        <protocol-detail
          class="protocol-detail"
          :protocol-data="protocolObj"
          :replace-data="protocolInfo"
        ></protocol-detail>
      </view>
      <template #drawer-btn>
        <view class="confirm">
          <view class="confirm-button" @click="protocolVisible = false">同意并继续</view>
        </view>
      </template>
    </v-drawer>

    <WaitLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyOnlineProductV2, fetchOnlineProduct, getProtocolList } from '@/apis/common.js'
import drawer from '@/components/drawer/drawer.vue'
import ProtocolDetail from '../components/protocol/ProtocolDetail.vue'
import WaitLoading from '@/components/overlay/WaitLoading.vue'

export default {
  name: 'qw',
  components: {
    ProtocolDetail,
    'v-drawer': drawer,
    WaitLoading
  },
  data() {
    return {
      form: {},
      timer: 3,
      element: [],
      timers: null,
      authProtocol: [],
      protocolVisible: false,
      protocolObj: {},
      protocolInfo: {},
      isProductFetched: false // 新增: 标记产品是否已获取
    }
  },

  computed: {
    hasProduct() {
      return this.element.length > 0
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getProtocolList()
  },
  onShow() {
    this.main()
  },
  onUnload() {
    this.stopTimer()
  },
  methods: {
    stopTimer() {
      if (this.timers) {
        clearInterval(this.timers)
        this.timers = null
        this.timer = 3
      }
    },

    openProtocol(item) {
      this.protocolVisible = true
      this.protocolObj = item
      this.protocolInfo = { ...this.form, appUserId: this.form.consumerId }
    },

    handleCancel() {
      this.stopTimer()

      this.navigateToNextPage()
    },

    async main() {
      this.stopTimer()
      await this.matchQwProduct()
      if (!this.hasProduct) {
        this.navigateToNextPage()
        return
      }
      // this.timerAuto()
    },

    async getProtocolList() {
      const res = await getProtocolList({ protocolSig: 'xmxr_sms_wechat' })
      this.authProtocol = res.data || []
    },

    async matchQwProduct() {
      this.isProductFetched = false // 开始获取产品时设置为 false
      const res = await fetchOnlineProduct({
        ...this.form,
        matchType: 1
      })
      this.element = res.data || []
      this.isProductFetched = true // 获取完成后设置为 true
    },

    async applyProduct() {
      const product = this.element[0]
      if (!product) {
        return
      }
      const res = await applyOnlineProductV2({
        ...this.form,
        productId: product.id,
        consumerId: product.consumerId
      })

      if (res.code == 200 && res.data) {
        location.href = res.data
      } else {
        this.navigateToNextPage()
      }
    },

    async fetchOverLoanProduct() {
      const res = await fetchOnlineProduct({
        matchType: 2,
        channelId: this.form.channelId,
        smsId: this.form.smsId
      })
      return res.data || []
    },

    async navigateToNextPage() {
      const param = encodeURIComponent(encryptByDES(JSON.stringify(this.form)))

      // const overLoanProduct = await this.fetchOverLoanProduct();
      // if (overLoanProduct.length > 0) {
      //   uni.navigateTo({url: `/extreme/v1/applyOther/index?param=${param}`})
      //   return
      // }

      uni.navigateTo({
        url: `/extreme/v1/download/index?param=${param}&result=2`
      })
    },

    async hanldeqw() {
      this.stopTimer()

      if (!this.isProductFetched) {
        // 如果产品还未获取完成,显示加载中
        this.$refs.loading.open()
        while (!this.isProductFetched) {
          await new Promise((resolve) => setTimeout(resolve, 100)) // 等待100ms再检查
        }
      }

      if (!this.hasProduct) {
        this.navigateToNextPage()
        return
      }

      this.applyProduct()
    },

    async timerAuto() {
      if (this.timers) {
        clearInterval(this.timers)
      }
      this.timers = setInterval(async () => {
        this.timer--
        if (!this.hasProduct) {
          this.stopTimer()
          this.navigateToNextPage()
          return
        }
        if (this.timer <= 0) {
          this.stopTimer()
          this.applyProduct()
        }
      }, 1300)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 100vh;
  position: relative;
  background: url('https://cdn.oss-unos.hmctec.cn/common/path/e864e73682cd45d1849f3452ae82acfc.png');
  background-size: 100%;
  background-repeat: no-repeat;

  .footer {
    position: absolute;
    width: 100%;
    left: 0;
    bottom: 0;
    box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.3);
    padding: 36rpx 30rpx 70rpx;
    z-index: 99;
    background-color: #fff;

    .line {
      .f-image {
        width: 40rpx;
        height: 40rpx;
      }
    }

    .btn {
      height: 98rpx;
      background: #62d5ab;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      line-height: 1;
      font-size: 40rpx;
    }

    .btn-cancale {
      color: #999;
      font-size: 40rpx;
      text-align: center;
      height: 98rpx;
      line-height: 98rpx;
      margin-top: 18rpx;
      border: 1rpx solid #ccc;
      border-radius: 62rpx;
    }
  }
}

::v-deep .protocol-detail-drawer {
  .drawer {
    padding: 40rpx !important;
  }
}

.protocol-detail-drawer-container {
  height: 500rpx;
  display: flex;
  flex-direction: column;

  .protocol-detail {
    flex: 1;
  }
}

.confirm {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -80rpx;
    left: 0;
    width: 100%;
    height: 80rpx;
    background: linear-gradient(360deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
  }

  .confirm-button {
    background: #3882e7;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 24rpx;
    text-align: center;
    font-weight: normal;
    font-size: 36rpx;
    color: #ffffff;
  }
}

.color-link {
  color: #ff3154;
}
</style>
