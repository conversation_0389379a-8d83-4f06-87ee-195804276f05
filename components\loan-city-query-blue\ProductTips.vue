<template>
  <view class="product-tips">
    <view class="product-title">
      <image class="product-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/5d24d3bce6a94ba6bc591a499d4021cd.png" mode="scaleToFill" />
      <text class="product-name">大额好借产品简介</text>
    </view>
    <view class="product-action" @click="handleShowPopup">
      <text class="action-text">查看</text>
      <image class="action-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/4ab54fd5feff4e8d9fe503aa1e401d30.png" mode="scaleToFill" />
    </view>
  </view>
</template>

<script>
export default {
  methods: {
    handleShowPopup() {
      this.$emit('showPopup');
    }
  }
}
</script>

<style lang="scss" scoped>
.product-tips {
  padding: 0 20rpx;
  margin: 0 auto;
  height: 68rpx;
  background: #F4FCFF;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .product-title {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .product-icon {
      width: 26rpx;
      height: 25rpx;
    }

    .product-name {
      font-weight: 400;
      font-size: 24rpx;
      color: #A4B4D8;
      line-height: 32rpx;
    }
  }

  .product-action {
    display: flex;
    align-items: center;
    gap: 6rpx;

    .action-text {
      font-weight: 400;
      font-size: 28rpx;
      color: #3974F6;
      line-height: 41rpx;
    }

    .action-icon {
      width: 20rpx;
      height: 20rpx;
    }
  }
}
</style> 