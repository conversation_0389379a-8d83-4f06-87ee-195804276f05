<template>
  <uni-popup ref="popup" type="center" :mask-click="false">
    <view class="wait-loading-container">
      <view class="loading-outer-container">
        <view class="progress-ring">
          <svg width="400" height="400" viewBox="0 0 400 400">
            <circle
              cx="200"
              cy="200"
              r="196"
              fill="transparent"
              stroke="#1A77F3"
              stroke-width="8"
              stroke-dasharray="1231.5"
              :stroke-dashoffset="progressOffset"
              transform="rotate(-90, 200, 200)"
              stroke-linecap="round"
            />
          </svg>
        </view>
        <view class="loading-container">
          <image
            class="loading-image"
            src="https://cdn.oss-unos.hmctec.cn/common/path/cb01d8788cee452e923adf28ad0bcd29.png"
            mode="aspectFit|aspectFill|widthFix"
          >
          </image>

          <text class="loading-text">{{ progressText }}</text>
        </view>
      </view>

      <view class="loading-text">尊敬的用户，您已完成认证</view>
      <view class="loading-desc">正在为您匹配服务机构...</view>
      <view class="loading-buttons">
        <view
          v-for="(item, index) in checkSteps"
          :key="index"
          class="loading-button"
          :class="{
            'loading-button-primary': item.complete,
            'loading-button-secondary': !item.complete
          }"
        >
          <text>{{ item.text }}</text>
          <view class="status-indicator">
            <view v-if="!item.complete" class="loading-dots">
              <view class="dot"></view>
              <view class="dot"></view>
              <view class="dot"></view>
            </view>
            <view v-else class="check-mark">
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M9.98438 1.94336C5.50781 1.94336 1.87891 5.55664 1.87891 10.0137C1.87891 14.4707 5.50781 18.084 9.98438 18.084C14.4609 18.084 18.0898 14.4707 18.0898 10.0137C18.0898 5.55664 14.4609 1.94336 9.98438 1.94336ZM9.98438 16.9629C6.13086 16.9629 3.00586 13.8516 3.00586 10.0156C3.00586 6.17773 6.13086 3.06836 9.98438 3.06836C13.8379 3.06836 16.9629 6.17969 16.9629 10.0156C16.9609 13.8516 13.8379 16.9629 9.98438 16.9629Z"
                  fill="#1A77F3"
                />
                <path
                  d="M5.73044 10.5743C5.57224 10.4297 5.55856 10.1817 5.7031 10.0235L5.88474 9.8223C6.02927 9.66409 6.27732 9.65042 6.43747 9.79495L8.5488 11.7129C8.707 11.8575 8.957 11.8457 9.09958 11.6856L13.4863 6.8555C13.6308 6.6973 13.8789 6.68363 14.0371 6.82816L14.2363 7.0098C14.3945 7.15433 14.4082 7.40238 14.2636 7.56253L9.1738 13.1739C9.02927 13.3321 8.78122 13.3457 8.62302 13.2012L5.73044 10.5743Z"
                  fill="#1A77F3"
                />
              </svg>
            </view>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'WaitLoading7',
  data() {
    return {
      duration: 10 * 1000, // 默认 10 秒
      autoClose: false,
      progress: 0,
      progressTimer: null,
      checkSteps: [
        { text: '身份信息检测', loading: false, complete: false },
        { text: '基本信息检测', loading: false, complete: false },
        { text: '信用信息检测', loading: false, complete: false }
      ]
    }
  },

  computed: {
    progressPercentage() {
      return this.progress * 100
    },
    progressText() {
      return Math.floor(this.progressPercentage) + '%'
    },
    progressOffset() {
      // 圆的周长约为 2 * PI * r = 2 * 3.14159 * 196 ≈ 1231.5
      const circumference = 1231.5
      // 计算偏移量，从完整圆开始，随着进度减少偏移
      // 当进度为1时，确保偏移量为0，完全闭合
      return this.progress >= 1 ? 0 : circumference * (1 - this.progress)
    }
  },

  methods: {
    open(duration, options = {}) {
      this.duration = duration || this.duration
      this.autoClose = options.autoClose || this.autoClose
      this.resetSteps()
      this.resetProgress()
      this.$refs.popup.open()
      this.startStepsAnimation()
      this.startProgressAnimation()
    },
    resetSteps() {
      this.checkSteps.forEach((step) => {
        step.loading = false
        step.complete = false
      })
    },
    resetProgress() {
      this.progress = 0
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
    },
    startProgressAnimation() {
      const updateInterval = 100 // 更新频率（毫秒）
      const progressStep = updateInterval / this.duration

      this.progressTimer = setInterval(() => {
        this.progress += progressStep
        if (this.progress >= 1) {
          this.progress = 1
          clearInterval(this.progressTimer)
        }
      }, updateInterval)
    },
    startStepsAnimation() {
      const stepsCount = this.checkSteps.length
      const stepDuration = this.duration / stepsCount // 根据步骤数量平均分配时间

      const runStep = (index) => {
        if (index >= stepsCount) {
          if (this.autoClose) {
            this.close()
          }
          return
        }

        this.checkSteps[index].loading = true
        setTimeout(() => {
          this.checkSteps[index].complete = true
          runStep(index + 1)
        }, stepDuration)
      }

      // 开始执行第一步
      runStep(0)
    },
    close() {
      this.resetProgress()
      this.$refs.popup.close()
    }
  },

  beforeDestroy() {
    this.resetProgress()
  }
}
</script>

<style lang="scss" scoped>
.wait-loading-container {
  padding-top: 130rpx;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(180deg, #e3efff 0%, #ffffff 100%);
}

.loading-outer-container {
  position: relative;
  width: 400rpx;
  height: 400rpx;
  margin: 0 auto 80rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-container {
  width: 384rpx;
  height: 384rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #ffffff;
  gap: 25rpx;
  z-index: 1;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.05);

  .loading-image {
    width: 213rpx;
    height: 213rpx;
  }

  .loading-text {
    font-weight: 400;
    font-size: 32rpx;
    color: #1a77f3;
    line-height: 42rpx;
    text-align: center;
  }
}

.progress-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;

  svg {
    width: 100%;
    height: 100%;

    circle {
      transition: stroke-dashoffset 0.1s linear;
    }
  }
}

@keyframes orbit {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(40rpx, -30rpx);
  }
  50% {
    transform: translate(80rpx, 0);
  }
  75% {
    transform: translate(40rpx, 30rpx);
  }
  100% {
    transform: translate(0, 0);
  }
}
.loading-text {
  margin-top: 22rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: center;
}

.loading-desc {
  margin-bottom: 100rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #666666;
  line-height: 36rpx;
  text-align: center;
}

.loading-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;

  .loading-button {
    width: 400rpx;
    height: 72rpx;
    padding: 20rpx 30rpx;
    border-radius: 16rpx;
    font-weight: 500;
    font-size: 28rpx;
    line-height: 33rpx;
    text-align: left;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20rpx;
    transition: background-color 0.3s ease-in-out;

    .status-indicator {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .loading-dots {
      display: flex;
      gap: 6rpx;

      .dot {
        width: 8rpx;
        height: 8rpx;
        background-color: #999999;
        border-radius: 50%;
        animation: dotBounce 1s infinite;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }

    .check-mark {
      display: flex;
      justify-content: center;
      align-items: center;
      animation: checkAppear 0.3s ease-in-out;

      svg {
        width: 40rpx;
        height: 40rpx;
      }
    }

    &-primary {
      background: #e3efff;
      color: #1a77f3;
    }
    &-secondary {
      background: #eeeeee;
      color: #999999;
    }
  }
}

@keyframes dotBounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6rpx);
  }
}

@keyframes checkAppear {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
