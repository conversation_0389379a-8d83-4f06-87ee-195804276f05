<template>
  <view class="header">
    <view class="header-title">
      <view class="header-title-text">
        <image
          class="header-title-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/2226f425a9ae44c7b9084b082c69afc7.png"
        ></image>
        <view>资方匹配成功</view>
      </view>
      <view class="header-title-subtext">请选择借款期数/借款金额</view>
    </view>
    <view class="tag">
      <view class="tag-number">1000</view>
      <view class="tag-gap-text">人</view>
      已放款
    </view>
  </view>
</template>

<script>
export default {
  name: 'ApplyHeader'
}
</script>

<style scoped lang="scss">
.header {
  position: relative;
  padding: 45rpx 0 45rpx 30rpx;

  .header-title {
    .header-title-text {
      margin-bottom: 16rpx;
      font-weight: 700;
      font-size: 38rpx;
      color: #333333;
      line-height: 50rpx;
      display: flex;
      align-items: center;
      gap: 10rpx;

      .header-title-icon {
        width: 38rpx;
        height: 38rpx;
      }
    }

    .header-title-subtext {
      font-weight: 400;
      font-size: 28rpx;
      color: #666666;
      line-height: 37rpx;
    }
  }

  .tag {
    position: absolute;
    top: 50rpx;
    right: 0;
    padding: 6rpx 7rpx 6rpx 10rpx;
    background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
    border-radius: 20rpx 0rpx 0rpx 20rpx;
    font-weight: 400;
    font-size: 20rpx;
    color: #5a352f;
    line-height: 16rpx;
    display: flex;
    align-items: center;

    .tag-number {
      font-weight: 400;
      font-size: 24rpx;
      color: #5a352f;
      line-height: 28rpx;
    }

    .tag-gap-text {
      margin-left: 3rpx;
      margin-right: 6rpx;
    }
  }
}
</style> 