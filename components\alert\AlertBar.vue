<template>
  <view
    class="alert"
    :class="[`alert--${theme}`]"
    :style="{ backgroundColor: THEME_CONFIG[theme].bgColor }"
  >
    <image :class="[`alert-icon_${theme}`]" class="alert-icon" :src="computedIconSrc"></image>
    <uni-notice-bar
      background-color="transparent"
      :color="computedTextColor"
      single
      scrollable
      class="alert-notice"
      :text="noticeText"
    />
  </view>
</template>

<script>
const THEME_CONFIG = {
  red: {
    iconSrc: 'https://cdn.oss-unos.hmctec.cn/common/path/c15afb8c4761407fb2568107b6ebbf30.png',
    textColor: '#6E2131',
    bgColor: '#fff1f4'
  },
  red_1: {
    iconSrc: 'https://cdn.oss-unos.hmctec.cn/common/path/7b1b021a2b9b4d3c8e2e311dc1a65b56.png',
    textColor: '#FA4D48',
    bgColor: '#FFF8F8'
  },
  orange: {
    iconSrc: 'https://cdn.oss-unos.hmctec.cn/common/path/93f66ba291e747e2bf48fb564ebb8c41.png',
    textColor: '#864508',
    bgColor: '#FFF7ED'
  },
  yellow: {
    iconSrc: 'https://cdn.oss-unos.hmctec.cn/common/path/6d592e9d6af14e20a7c99bf96f169696.png',
    textColor: '#382107',
    bgColor: 'rgba(252, 223, 41, 0.30)'
  },
  beige: {
    iconSrc: 'https://cdn.oss-unos.hmctec.cn/common/path/d78ea28c52e94464b54ed94516db0f1b.png',
    textColor: '#333333',
    bgColor: '#F8DCB5'
  },
  blue: {
    iconSrc: 'https://cdn.oss-unos.hmctec.cn/common/path/93f66ba291e747e2bf48fb564ebb8c41.png',
    textColor: '#333333',
    bgColor: '#F3F8FE'
  }
}

export default {
  name: 'AlertBar',

  props: {
    noticeText: {
      type: String,
      default: '选择正规金融机构，警惕虚假广告和诈骗。按时还款，保护个人信用。'
    },
    iconSrc: {
      type: String,
      default: ''
    },
    textColor: {
      type: String,
      default: ''
    },
    theme: {
      type: String,
      default: 'red',
      validator: (value) => ['red', 'orange', 'yellow', 'beige', 'blue'].includes(value)
    }
  },

  computed: {
    computedIconSrc() {
      return this.iconSrc || THEME_CONFIG[this.theme].iconSrc
    },
    computedTextColor() {
      return this.textColor || THEME_CONFIG[this.theme].textColor
    },
    THEME_CONFIG() {
      return THEME_CONFIG
    }
  }
}
</script>

<style lang="scss" scoped>
.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }
  .alert-icon_red_1 {
    width: 40rpx;
    height: 40rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}
</style>
