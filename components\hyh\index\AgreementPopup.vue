<template>
  <uni-popup
    background-color="#fff"
    ref="agreementPopup"
    type="bottom"
    border-radius="32rpx 32rpx 0 0"
  >
    <view class="agreement-popup">
      <view class="agreement-container">
        <view v-for="agreement in agreementList" :key="agreement.protocolId">
          <view class="agreement-content">
            <div v-html="agreement.content"></div>
          </view>
        </view>
      </view>
      <view class="agreement-agree-container">
        <view class="agreement-agree-btn" @click="handleAgreeAndContinue">同意并继续</view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'AgreementPopup',
  
  props: {
    agreementList: {
      type: Array,
      default: () => []
    }
  },
  
  methods: {
    open() {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },
    
    close() {
      this.$refs.agreementPopup.close()
    },
    
    handleAgreeAndContinue() {
      this.close()
      this.$emit('agree')
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05f2447ceb6b45caaf742b8366205959.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style> 