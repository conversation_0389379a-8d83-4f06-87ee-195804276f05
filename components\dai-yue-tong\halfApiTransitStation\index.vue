<template>
  <view class="transit-station-container">
    <view class="search-icon-container">
      <!-- 放大镜图标 -->
      <image
        class="search-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/970065097a4b4e51b37a496d4dcc29f1.png"
        mode="scaleToFill"
      />
    </view>

    <view class="review-title">您的申请正在审核中，请稍等...</view>

    <view class="matching-title">正在为您匹配第三方借款服务机构</view>

    <view class="progress-container">
      <view class="progress-list">
        <view class="progress-item">
          <uni-icons
            v-if="progress >= 1"
            class="progress-icon success"
            type="checkbox-filled"
            size="30"
          ></uni-icons>
          <uni-icons
            v-else
            class="progress-icon spinner"
            type="spinner-cycle"
            size="30"
          ></uni-icons>
          <view class="progress-text" :class="{ completed: progress >= 1 }">身份信息确认</view>
          <view class="progress-line" :class="{ completed: progress >= 1 }"></view>
        </view>
        <view class="progress-item">
          <uni-icons
            v-if="progress >= 2"
            class="progress-icon success"
            type="checkbox-filled"
            size="30"
          ></uni-icons>
          <uni-icons
            v-else
            class="progress-icon spinner"
            type="spinner-cycle"
            size="30"
          ></uni-icons>
          <view class="progress-text" :class="{ completed: progress >= 2 }">正在提交资料</view>
          <view class="progress-line" :class="{ completed: progress >= 2 }"></view>
        </view>
        <view class="progress-item">
          <uni-icons
            v-if="progress >= 3"
            class="progress-icon success"
            type="checkbox-filled"
            size="30"
          ></uni-icons>
          <uni-icons
            v-else
            class="progress-icon spinner"
            type="spinner-cycle"
            size="30"
          ></uni-icons>
          <view class="progress-text" :class="{ completed: progress >= 3 }">正在进行初审</view>
        </view>
      </view>
    </view>

    <view class="disclaimer-text">
      贷悦通将根据您的个人信息及借款需求信息，为您推荐第三方贷款服务机构，但未经您同意授权不会将您的身份信息共享或提供给第三方服务机构
    </view>
  </view>
</template>
<script>
export default {
  name: 'HalfApiTransitStation',
  props: {
    // 可以添加组件需要的props
    customTotalTime: {
      type: Number,
      default: 10000 // 默认10秒
    }
  },
  data() {
    return {
      progress: 0,
      timer: null,
      totalSteps: 3, // 总步骤数
      totalTime: this.customTotalTime, // 使用props中的值
      stepTimers: [] // 存储每个步骤的定时器
    }
  },
  computed: {
    stepTime() {
      // 每个步骤的时间
      return this.totalTime / this.totalSteps
    }
  },
  mounted() {
    this.startProgress()
  },
  beforeDestroy() {
    this.clearAllTimers()
  },
  methods: {
    startProgress() {
      // 清除所有现有定时器
      this.clearAllTimers()

      // 为每个步骤创建定时器
      for (let i = 1; i <= this.totalSteps; i++) {
        const timer = setTimeout(() => {
          this.progress = i
          // 当进度完成时触发事件
          if (i === this.totalSteps) {
            this.$emit('progress-complete')
          }
        }, i * this.stepTime)

        this.stepTimers.push(timer)
      }
    },

    // 更新总时间并重启进度
    updateTotalTime(newTime) {
      if (newTime > 0) {
        this.totalTime = newTime
        this.progress = 0
        this.startProgress()
      }
    },

    // 清除所有定时器
    clearAllTimers() {
      // 清除主定时器
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }

      // 清除步骤定时器
      this.stepTimers.forEach((timer) => {
        clearTimeout(timer)
      })
      this.stepTimers = []
    }
  }
}
</script>
<style lang="scss" scoped>
.transit-station-container {
  padding-top: 292rpx;
  min-height: 100vh;
  background: linear-gradient(
    179deg,
    rgba(184, 254, 255, 0.43) 0%,
    rgba(160, 241, 237, 0) 36%,
    rgba(64, 189, 183, 0) 100%
  );

  .search-icon-container {
    margin: 0 auto;
    background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/768793da4fe74530a64b2399fa4648c9.png);
    background-size: 100% 100%;
    width: 484rpx;
    height: 250rpx;
    position: relative;

    .search-icon {
      position: absolute;
      top: 40%;
      left: 60%;
      width: 84.21rpx;
      height: 84.22rpx;
      animation: search-icon-animation 2s linear infinite;
    }
  }

  .review-title {
    margin: 44rpx auto 28rpx;
    text-align: center;
    font-weight: 500;
    font-size: 36rpx;
    color: #1a1a1a;
    line-height: 52rpx;
  }

  .matching-title {
    font-weight: 400;
    font-size: 36rpx;
    color: #4d4d4d;
    line-height: 52rpx;
    text-align: center;
  }

  .progress-container {
    margin-top: 124rpx;
    margin-bottom: 294rpx;
    display: flex;
    justify-content: center;

    .progress-list {
      width: fit-content;
      position: relative;
    }

    .progress-item {
      margin-bottom: 50rpx;
      display: flex;
      align-items: center;
      gap: 36rpx;
      position: relative;

      &:last-child {
        margin-bottom: 0;
      }

      .progress-icon {
        font-size: 44rpx !important;
        color: #cccccc !important;

        &.success {
          color: #49c2c4 !important;
        }

        &.spinner {
          animation: spinner-rotate 1s linear infinite;
        }
      }

      .progress-text {
        font-weight: 400;
        font-size: 36rpx;
        color: #999999;

        &.completed {
          color: #1a1a1a;
        }
      }

      .progress-line {
        position: absolute;
        left: 22rpx;
        top: 44rpx;
        height: 50rpx;
        width: 2rpx;
        background-color: #cccccc;

        &.completed {
          background-color: #49c2c4;
        }
      }
    }
  }

  .disclaimer-text {
    margin: 0 auto;
    width: 630rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 34rpx;
    text-align: center;
  }
}

// 放大镜动画
@keyframes search-icon-animation {
  0% {
    transform: translate(-50%, -50%) rotate(0deg) translate(20rpx) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg) translate(20rpx) rotate(-360deg);
  }
}

// 旋转动画
@keyframes spinner-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
