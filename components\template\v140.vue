<template>
  <view class="page-container">
    <view class="phone-container">
      <view class="input-container">
        <image
          class="input-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/19d9d1fdb08045a1bc7183ca0ca86f88.png"
          mode="scaleToFill"
        />

        <view class="line"></view>

        <input
          class="input-text"
          type="text"
          placeholder="请输入手机号"
          maxlength="11"
          v-model="form.phone"
        />
      </view>

      <view class="confirm-btn" @click="clickConfirm">确认</view>
    </view>

    <image
      class="service-img"
      src="https://cdn.oss-unos.hmctec.cn/common/path/abf2fbd281c24af7a7fa17fe3096c9b2.png"
      mode="scaleToFill"
    />

    <view class="service-text">
      郑重声明：本平台只提供贷款咨询和推荐服务， 放款由银行或金融机构进行，
      所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全，
      请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。
    </view>
  </view>
</template>
<script>
import { applyOnline } from '@/apis/test.js'

export default {
  data() {
    return {
      form: {
        phone: ''
      }
    }
  },
  methods: {
    async clickConfirm() {
      if (!this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      const params = {
        linkId: this.vuex_linkId,
        linkPhone: this.form.phone
      }

      this.$u.vuex('vuex_phone', this.form.phone)

      uni.showLoading({
        title: '请稍后'
      })

      try {
        const res = await applyOnline(params)
        uni.hideLoading()
        if (res.data) {
          // 如果返回链接，进行重定向
          window.location.href = res.data
        } else {
          uni.showToast({
            title: '暂无产品',
            icon: 'none'
          })
        }
      } catch (err) {
        uni.hideLoading()
        uni.showToast({
          title: err.message || '网络异常，请稍后重试',
          icon: 'none'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  padding-top: 289rpx;
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/4306c646cf2c45ccadf67d7ee1f36d14.png);
  background-size: 100% auto;
  background-repeat: no-repeat;
  background-color: #ebf1fc;
}

.phone-container {
  margin: 0 auto 30rpx;
  padding: 94rpx 27rpx;
  width: 692rpx;
  height: 432rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;

  .input-container {
    display: flex;
    align-items: center;
    width: 637rpx;
    height: 98rpx;
    padding: 30rpx 22rpx;
    background: #f4f4f4;
    border-radius: 14rpx 14rpx 14rpx 14rpx;

    .input-icon {
      width: 38rpx;
      height: 38rpx;
    }

    .line {
      margin-left: 16rpx;
      margin-right: 22rpx;
      width: 1rpx;
      height: 46rpx;
      background-color: #d8d8d8;
    }

    .input-text {
      font-weight: 400;
      font-size: 32rpx;
    }
  }

  .confirm-btn {
    margin-top: 48rpx;
    width: 637rpx;
    height: 98rpx;
    background: #4868f9;
    border-radius: 14rpx 14rpx 14rpx 14rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 400;
    font-size: 41rpx;
    color: #ffffff;
    line-height: 59rpx;
  }
}

.service-img {
  display: block;
  margin: 0 auto;
  width: 692rpx;
  height: 372rpx;
}

.service-text {
  margin: 69rpx auto;
  width: 629rpx;
  font-size: 24rpx;
  color: #939393;
  line-height: 39rpx;
  text-align: center;
}
</style>
