<template>
  <view class="page-container">
    <AlertBar theme="orange" />

    <Header @click.native="navigateToIndex" />

    <AmountInput :value.sync="form.demandAmount" :sliderValue.sync="form.demandAmountSlider" />

    <BorrowingOptions
      :demandAmount="form.demandAmount"
      :initialMonthIndex="form.monthIndex"
      @month-changed="handleMonthChanged"
    />

    <PhoneContainer
      v-model="form.phone"
      :agreementList="agreementList"
      :agreeStatus.sync="isAgree"
      @open-agreement="openAgreement"
      @get-quota="handleGetQuota"
    />

    <Declaration />

    <AgreementPopup ref="agreementPopup" :agreementList="agreementList" @agree="handleAgree" />

    <CodePopup
      ref="codePopup"
      :phone="form.phone"
      :channelId="form.channelId"
      @send-code="handleSendCode"
      @submit="handleCodeSubmit"
    />

    <RemindPopup ref="remindPopup" />
  </view>
</template>
<script>
import { validateMobile } from '@/utils/validators'
import LockNativeBack from '@/utils/lock-native-back'
import { reportUV } from '@/apis/common'
import { saveLoan, sendSmsCode } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hyh.vue'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'
import AlertBar from '@/components/alert/AlertBar.vue'
import RemindPopup from '@/components/hyh/index/RemindPopup.vue'
import AmountInput from '@/components/hyh/index/AmountInput.vue'
import BorrowingOptions from '@/components/hyh/index/BorrowingOptions.vue'
import PhoneContainer from '@/components/hyh/index/PhoneContainer.vue'
import AgreementPopup from '@/components/hyh/index/AgreementPopup.vue'
import CodePopup from '@/components/hyh/index/CodePopup.vue'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v7',

  components: {
    Declaration,
    Header,
    AlertBar,
    RemindPopup,
    AmountInput,
    BorrowingOptions,
    PhoneContainer,
    AgreementPopup,
    CodePopup
  },

  data() {
    return {
      form: {
        demandAmount: 50000,
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        channelId: ''
      },

      isAgree: false,
      lockBack: null,
      agreementList: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
    }
    this.fetchAgreement()

    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
    async fetchAgreement() {
      // 注册页协议
      this.agreementList = await getAgreements('hyh_info_stream_registration_page_agreement')
    },

    handleMonthChanged(newMonthIndex) {
      this.form.monthIndex = newMonthIndex
    },

    openAgreement() {
      this.$refs.agreementPopup.open()
    },

    // 验证手机号格式
    validatePhoneFormat() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return false
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }

      return true
    },

    handleGetQuota() {
      // 验证手机号
      if (!this.validatePhoneFormat()) {
        return
      }

      this.$refs.codePopup.open()
    },

    handleAgree() {
      this.isAgree = true
      this.handleGetQuota()
    },

    async handleSendCode() {
      // 验证手机号
      if (!this.validatePhoneFormat()) {
        return
      }

      const res = await sendSmsCode({
        phone: this.form.phone,
        channelId: this.form.channelId
      })

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '发送失败',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      // 发送成功后，通知验证码组件开始倒计时
      this.$refs.codePopup.startCountdown()
    },

    handleCodeSubmit(code) {
      throttle(() => {
        this.login(code)
      })
    },

    async getVisitorId() {
      const fp = await fpPromise
      const { visitorId } = await fp.get()
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },

    async getLoginParams(code) {
      const params = {}

      params.phoneBlack = getBlackPhone()
      // params.h5UaUuid = await this.getVisitorId();
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = this.form.demandAmount
      params.phone = this.form.phone
      params.code = code
      params.channelId = this.form.channelId

      return params
    },

    async login(code) {
      const params = await this.getLoginParams(code)

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      // 登录成功后，记录手机号
      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)
      this.$u.vuex('vuex_consumerId', res.data)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: this.form.demandAmount,
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v7/auth/index?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/3d34ff5b56ae4f299c8ef6f00cd59fc2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}
</style>
