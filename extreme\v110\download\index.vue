<script>
import Header from '@/components/header/header-custom.vue'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: { Declaration, Header, Services }
}
</script>

<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <view class="page-content">
        <Header @click.native="navigateToIndex" />

        <view class="feature-image">
          <view class="icon-speaker"></view>
        </view>

        <Services />

        <Declaration />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
@import '@/theme/v110/export/download';
</style>
