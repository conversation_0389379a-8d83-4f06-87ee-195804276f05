<script>
export default {
  name: 'declaration-xmxr',

  methods: {
    bindOpenUrl(url) {
      window.location.href = url
    }
  }
}
</script>

<template>
  <view class="declaration-container">
    <view v-if="vuex_declaration && !vuex_templateSig" v-html="vuex_declaration"></view>
    <template v-else>
      <view>
        <view
          class="police label flex-row-cc"
          @click="
            bindOpenUrl(
              'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=51010702002566'
            )
          "
        >
          <image
            class="police-logo"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/h5/images/icon/police.png"
          ></image>
        </view>
        <view class="label">鲁ICP备2023043951号</view>
        <view class="label">小马信融(威海)信息科技有限公司</view>
        <view class="label">客服热线（工作日9：30-18：00）</view>
        <view class="label">028-83225733</view>
        <view class="label" style="padding-top: 30rpx"
          >郑重声明：平台只提供贷款咨询和推荐服务，放款由银行或金融机构进行，
          所有贷款申请在未成功贷款前绝不收取任何费用，为了保证您的资金安全，
          请不要相信任何要求您支付费用的信息、邮件、电话等不实信息。</view
        >
      </view>

      <view>
        <view class="label">本平台IRR综合年化利率7%~24%（IRR）;</view>
        <view class="label">贷款有风险，借款需谨慎;</view>
        <view class="label">请根据个人能力合理贷款，理性消费，避免逾期;</view>
        <view class="label">贷款额度，放款时间以实际审批结果为准;</view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.declaration-container {
  padding: 30rpx 60rpx;
  font-size: 26rpx;
  line-height: 1.5;
  text-align: center;
  color: #ccc;

  .police-logo {
    width: 30rpx;
    height: 30rpx;
    margin-right: 8rpx;
  }
}
</style>
