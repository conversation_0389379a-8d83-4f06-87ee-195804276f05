<script>
export default {
  name: 'header-jqgj2'
}
</script>

<template>
  <view class="header-container">
    <image
      class="logo"
      src="https://cdn.oss-unos.hmctec.cn/common/path/ef44e045922d4892ba172de816798c3c.png"
    />
    <image
      src="https://cdn.oss-unos.hmctec.cn/common/path/d65280cf2e96414983512abf9206e857.png"
      class="brand"
    />
    <view class="divider"></view>
    <view class="slogan">助力美好生活</view>
  </view>
</template>

<style scoped lang="scss">
.header-container {
  display: flex;
  align-items: center;

  .logo {
    margin-right: 15rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .brand {
    width: 135rpx;
    height: 32rpx;
  }

  .divider {
    margin: 0 6rpx;
    width: 2rpx;
    height: 27rpx;
    background-color: #333333;
  }

  .slogan {
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 35rpx;
    letter-spacing: 1px;
  }
}
</style>
