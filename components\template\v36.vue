<template>
  <view class="page-container">
    <AlertBar theme="orange" />

    <Header />

    <AmountInput :value.sync="form.demandAmount" :sliderValue.sync="form.demandAmountSlider" />

    <BorrowingOptions
      :demandAmount="form.demandAmount"
      :initialMonthIndex="form.monthIndex"
      @month-changed="handleMonthChanged"
    />

    <PhoneContainer
      v-model="form.phone"
      :agreementList="agreementList"
      :agreeStatus.sync="isAgree"
      @open-agreement="openAgreementPopup"
      @get-quota="handleGetQuotaFromPhoneContainer"
    />

    <Declaration />

    <AgreementPopup
      ref="agreementPopup"
      :agreementList="agreementList"
      @agree="handleAgreeFromPopup"
    />

    <CodePopup
      ref="codePopup"
      :phone="form.phone"
      :channelId="form.channelId"
      @send-code="handleSendCodeFromPopup"
      @submit="handleCodeSubmitFromPopup"
    />

    <RemindPopup ref="remindPopup" />
  </view>
</template>
<script>
import RemindPopup from '@/components/hyh/index/RemindPopup.vue'
import { validateMobile } from '@/utils/validators'
import AlertBar from '@/components/alert/AlertBar.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { reportUV } from '@/apis/common'
import { saveLoan, sendTestSmsCode as sendSmsCode } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'
import { getAgreements } from '@/utils/agreement'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-hyh.vue'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'

import AmountInput from '@/components/hyh/index/AmountInput.vue'
import BorrowingOptions from '@/components/hyh/index/BorrowingOptions.vue'
import PhoneContainer from '@/components/hyh/index/PhoneContainer.vue'
import AgreementPopup from '@/components/hyh/index/AgreementPopup.vue'
import CodePopup from '@/components/hyh/index/CodePopup.vue'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v36',

  components: {
    AlertBar,
    Declaration,
    Header,
    RemindPopup,
    AmountInput,
    BorrowingOptions,
    PhoneContainer,
    AgreementPopup,
    CodePopup
  },

  data() {
    return {
      form: {
        demandAmount: 50000,
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        channelId: '',
        code: ''
      },
      isAgree: false,
      lockBack: null,
      agreementList: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
    }
    this.fetchAgreement()
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
  },

  methods: {
    async fetchAgreement() {
      this.agreementList = await getAgreements('hyh_info_stream_registration_page_agreement')
    },

    handleMonthChanged(newMonthIndex) {
      this.form.monthIndex = newMonthIndex
    },

    openAgreementPopup() {
      this.$refs.agreementPopup.open()
    },

    // 验证手机号格式
    validatePhoneFormat() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return false
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }

      return true
    },

    handleGetQuotaFromPhoneContainer() {
      if (!this.validatePhoneFormat()) {
        return
      }

      if (!this.isAgree) {
        this.$refs.agreementPopup.open()
        return
      }
      this.$refs.codePopup.open()
    },

    handleAgreeFromPopup() {
      this.isAgree = true
      this.$refs.agreementPopup.close()
      if (this.validatePhoneFormat()) {
        this.$refs.codePopup.open()
      }
    },

    async handleSendCodeFromPopup() {
      if (!this.validatePhoneFormat()) {
        return
      }

      const res = await sendSmsCode({
        phone: this.form.phone,
        channelId: this.form.channelId
      })

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '发送失败',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      this.$refs.codePopup.startCountdown()
    },

    handleCodeSubmitFromPopup(code) {
      this.form.code = code
      throttle(() => {
        this.login(code)
      })
    },

    async getVisitorId() {
      const fp = await fpPromise
      const { visitorId } = await fp.get()
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },

    async getLoginParams(code) {
      const params = {}
      params.phoneBlack = getBlackPhone()
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = this.form.demandAmount
      params.phone = this.form.phone
      params.code = code
      params.channelId = this.form.channelId
      return params
    },

    async login(code) {
      const params = await this.getLoginParams(code)

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)
      this.$u.vuex('vuex_consumerId', res.data)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: this.form.demandAmount,
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v36/auth/index?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/3d34ff5b56ae4f299c8ef6f00cd59fc2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}

.declaration {
  padding: 0 60rpx;
}
</style>
