<template>
  <view class="page-container">
    <AlertBar theme="orange" />

    <!-- <Header @click.native="navigateToIndex" /> -->

    <view class="header">
      <view class="header-text">恭喜您咨询成功！</view>
      <view class="sub-header-text">根据您的资质，已为您匹配以下{{ productCount }}款产品</view>
    </view>

    <view class="main-product">
      <view class="main-product-info">
        <view class="product">
          <image class="product-logo" :src="mainProduct.logo"></image>
          <text class="product-name">{{ mainProduct.name }}</text>
        </view>
        <view class="product-users">
          <text class="number">1000</text>
          <text>人已放款</text>
        </view>
      </view>
      <view class="product-amount">
        <view class="amount-label">最高额度(元)</view>
        <view class="amount-value">{{ mainProduct.loanableFundsBig | toThousandFilter }}</view>
      </view>
      <view class="product-rate">
        最低年化利率
        {{ mainProduct.interestRateLittle | toPriceAbbreviations }}%
      </view>
      <view class="apply-button" @click="clickApply(mainProduct)">立即申请</view>
    </view>

    <view class="recommended-products">
      <view class="title">
        <text class="section-title">精选推荐</text>
        <text class="section-subtitle">申请产品越多，获取额度概率越高</text>
      </view>

      <view class="product-item" v-for="(item, index) in productList" :key="index">
        <view class="product">
          <image class="product-icon" :src="item.logo"></image>
          <text class="product-name">{{ item.name }}</text>
        </view>
        <view class="product-details">
          <view class="product-features">
            <view class="product-features-item">
              <text class="product-features-value highlight">{{
                item.loanableFundsBig | toThousandFilter
              }}</text>
              <text class="product-features-label">最高额度(元)</text>
            </view>
            <view class="product-features-item">
              <text class="product-features-value"
                >{{ item.interestRateLittle | toPriceAbbreviations }}%</text
              >
              <text class="product-features-label">最低年化利率</text>
            </view>
          </view>
          <view class="apply-button-small" @click="clickApply(item)">立即申请</view>
        </view>
      </view>
    </view>

    <Services />

    <Declaration />

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import AlertBar from '@/components/alert/AlertBar.vue'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct } from '@/apis/common-2'
import Header from '@/components/header/header-hyh.vue'
import Declaration from '@/components/footer/declaration-component/declaration-zxbc.vue'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import Services from '@/components/services/index.vue'
import { matchingOnlineProduct } from '@/apis/common'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'applyOther',

  components: {
    AlertBar,
    Declaration,
    Header,
    FullScreenMatchLoading,
    Services
  },

  data() {
    return {
      form: {},
      productList: [],
      mainProduct: {},
      productCount: 0
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getFlowData()
  },

  methods: {
    async getFlowData() {
      const flowData = getFlowData('overloan')
      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
        this.productCount = flowData.productList.length
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
        window.location.href = res.data

        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const customFetch = {
        wechat: matchingOnlineProduct,
        wechat_link: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}
.page-container {
  min-height: 100vh;
  position: relative;
  background-color: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 481rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/152637d1607c4b0dbe315e48566915cf.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;
  background-color: #fff7eb;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}

.header {
  margin-bottom: 20rpx;
  position: relative;

  .header-text {
    margin-bottom: 10rpx;
    font-family: UPFont-WuweiGB-Flash;
    font-weight: 400;
    font-size: 52rpx;
    color: #16283c;
    line-height: 52rpx;
    letter-spacing: 3px;
    text-align: center;
  }

  .sub-header-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 41rpx;
    text-align: center;
  }
}

.main-product {
  padding: 35rpx 0;
  margin: 0 30rpx;
  position: relative;
  background: linear-gradient(180deg, #1678ff 0%, #7db4ff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .main-product-info {
    display: flex;
    justify-content: space-between;

    .product {
      padding-left: 35rpx;
      display: flex;
      align-items: center;
      gap: 15rpx;

      .product-logo {
        width: 48rpx;
        height: 48rpx;
      }

      .product-name {
        font-weight: 400;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 46rpx;
      }
    }

    .product-users {
      display: flex;
      align-items: center;
      padding: 10rpx 7rpx 10rpx 10rpx;
      background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
      border-radius: 20rpx 0rpx 0rpx 20rpx;
      font-weight: 400;
      font-size: 20rpx;
      color: #5a352f;
      line-height: 28rpx;

      .number {
        font-size: 24rpx;
      }
    }
  }

  .product-amount {
    margin-top: 5rpx;

    .amount-label {
      font-weight: normal;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 34rpx;
      text-align: center;
    }

    .amount-value {
      font-family: DIN;
      font-weight: 700;
      font-size: 72rpx;
      color: #ffffff;
      line-height: 78rpx;
      text-align: center;
    }
  }

  .product-rate {
    font-weight: normal;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 34rpx;
    text-align: center;
  }

  .apply-button {
    margin: 20rpx 55rpx 0;
    background: linear-gradient(180deg, #eaf3ff 0%, #ffffff 100%);
    border-radius: 46rpx 46rpx 46rpx 46rpx;
    border: 1rpx solid #ffffff;
    font-weight: 400;
    font-size: 36rpx;
    color: $color-primary;
    line-height: 52rpx;
    padding: 12rpx;
    text-align: center;
  }
}

.recommended-products {
  margin: 30rpx;

  .title {
    margin-bottom: 25rpx;
    display: flex;
    gap: 16rpx;
    align-items: flex-end;

    .section-title {
      font-weight: 700;
      font-size: 36rpx;
      color: #3d3d3d;
      line-height: 48rpx;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
    }
  }

  .product-item {
    margin-bottom: 30rpx;
    padding: 35rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 1rpx solid #ffffff;

    .product {
      margin-bottom: 20rpx;
      display: flex;
      gap: 20rpx;

      .product-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .product-name {
        font-weight: 400;
        font-size: 32rpx;
        color: #171a1d;
        line-height: 45rpx;
      }
    }

    .product-details {
      display: flex;
      gap: 80rpx;

      .product-features {
        flex: 1;
        display: flex;
        justify-content: space-between;

        .product-features-item {
          display: flex;
          flex-direction: column;
          gap: 8rpx;
          align-items: center;

          .product-features-value {
            font-family: DIN;
            font-weight: 700;
            font-size: 38rpx;
            color: #171a1d;
            line-height: 41rpx;

            &.highlight {
              color: #f55c4d;
            }
          }

          .product-features-label {
            font-weight: normal;
            font-size: 24rpx;
            color: #919094;
            line-height: 34rpx;
          }
        }
      }

      .apply-button-small {
        padding: 12rpx 25rpx;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        border: 2rpx solid $color-primary;
        font-weight: 400;
        font-size: 36rpx;
        color: $color-primary;
        line-height: 50rpx;
      }
    }
  }
}
</style>
