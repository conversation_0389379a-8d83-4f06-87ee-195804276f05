<template>
  <view class="content">
    <view class="page-body">
      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/64900a390f644dccb33bb1f2eb2183de.png"
        mode="scaleToFill"
        class="feature-image"
      />

      <view class="apply-btn" @click="hanldeqw">
        立即领取 <span v-if="this.timers">({{ this.timer }}S)</span>
      </view>

      <view class="protocol-container">
        <view class="flex-row-cc flex-wrap color_6 f12">
          <view>本人已知晓</view>
          <view
            class="color-link"
            v-for="item in authProtocol"
            :key="item.protocolId"
            @click="openProtocol(item)"
          >
            《{{ item.name }}》
          </view>
          <view class="">的</view>
        </view>
        <view class="flex-row-cc flex-wrap color_6 f12"
          >所有内容，同意并授权平台推荐/匹配多个产品</view
        >
      </view>
    </view>

    <image
      src="https://cdn.oss-unos.hmctec.cn/common/path/07ce895eee7047bda36aa7800ffd6833.png"
      mode="scaleToFill"
      class="apply-step-image"
    />

    <view class="tips-container">
      <view class="tips-title">温馨提示</view>
      <view class="tips-content">贷款有风险，借款需谨慎</view>
      <view class="tips-content">请根据个人能力合理借贷，理性消费，避免逾期</view>
      <view class="tips-content">贷款额度、利率、放款时间以实际审批结果为准</view>
    </view>

    <v-drawer
      v-model="protocolVisible"
      @close="protocolVisible = false"
      direction="bottom"
      width="100vw"
      height="auto"
      backgroundImage="orange"
      radius="32rpx 32rpx 0 0"
      class="protocol-detail-drawer"
    >
      <view class="protocol-detail-drawer-container">
        <protocol-detail
          class="protocol-detail"
          :protocol-data="protocolObj"
          :replace-data="protocolInfo"
        ></protocol-detail>
      </view>
      <template #drawer-btn>
        <view class="confirm">
          <view class="confirm-button" @click="protocolVisible = false">同意并继续</view>
        </view>
      </template>
    </v-drawer>

    <WaitLoading ref="loading" />
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyOnlineProductV2, fetchOnlineProduct, getProtocolList } from '@/apis/common.js'
import drawer from '@/components/drawer/drawer.vue'
import ProtocolDetail from '../components/protocol/ProtocolDetail.vue'
import WaitLoading from '@/components/overlay/WaitLoading.vue'

export default {
  name: 'qw',
  components: {
    ProtocolDetail,
    'v-drawer': drawer,
    WaitLoading
  },
  data() {
    return {
      form: {},
      timer: 3,
      element: [],
      timers: null,
      authProtocol: [],
      protocolVisible: false,
      protocolObj: {},
      protocolInfo: {},
      isProductFetched: false // 新增: 标记产品是否已获取
    }
  },

  computed: {
    hasProduct() {
      return this.element.length > 0
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getProtocolList()
  },
  onShow() {
    this.main()
  },
  onUnload() {
    this.stopTimer()
  },
  methods: {
    stopTimer() {
      if (this.timers) {
        clearInterval(this.timers)
        this.timers = null
        this.timer = 3
      }
    },

    openProtocol(item) {
      this.protocolVisible = true
      this.protocolObj = item
      this.protocolInfo = { ...this.form, appUserId: this.form.consumerId }
    },

    handleCancel() {
      this.stopTimer()

      this.navigateToNextPage()
    },

    async main() {
      this.stopTimer()
      await this.matchQwProduct()
      if (!this.hasProduct) {
        this.navigateToNextPage()
        return
      }
      // this.timerAuto()
    },

    async getProtocolList() {
      const res = await getProtocolList({ protocolSig: 'xmxr_sms_wechat' })
      this.authProtocol = res.data || []
    },

    async matchQwProduct() {
      this.isProductFetched = false // 开始获取产品时设置为 false
      const res = await fetchOnlineProduct({
        ...this.form,
        matchType: 1
      })
      this.element = res.data || []
      this.isProductFetched = true // 获取完成后设置为 true
    },

    async applyProduct() {
      const product = this.element[0]
      if (!product) {
        return
      }
      const res = await applyOnlineProductV2({
        ...this.form,
        productId: product.id,
        consumerId: product.consumerId
      })

      if (res.code == 200 && res.data) {
        location.href = res.data
      } else {
        this.navigateToNextPage()
      }
    },

    async fetchOverLoanProduct() {
      const res = await fetchOnlineProduct({
        matchType: 2,
        channelId: this.form.channelId,
        smsId: this.form.smsId
      })
      return res.data || []
    },

    async navigateToNextPage() {
      const param = encodeURIComponent(encryptByDES(JSON.stringify(this.form)))

      // const overLoanProduct = await this.fetchOverLoanProduct();
      // if (overLoanProduct.length > 0) {
      //   uni.navigateTo({url: `/extreme/v1/applyOther/index?param=${param}`})
      //   return
      // }

      uni.navigateTo({
        url: `/extreme/v1/download/index?param=${param}&result=2`
      })
    },

    async hanldeqw() {
      this.stopTimer()

      if (!this.isProductFetched) {
        // 如果产品还未获取完成,显示加载中
        this.$refs.loading.open()
        while (!this.isProductFetched) {
          await new Promise((resolve) => setTimeout(resolve, 100)) // 等待100ms再检查
        }
      }

      if (!this.hasProduct) {
        this.navigateToNextPage()
        return
      }

      this.applyProduct()
    },

    async timerAuto() {
      if (this.timers) {
        clearInterval(this.timers)
      }
      this.timers = setInterval(async () => {
        this.timer--
        if (!this.hasProduct) {
          this.stopTimer()
          this.navigateToNextPage()
          return
        }
        if (this.timer <= 0) {
          this.stopTimer()
          this.applyProduct()
        }
      }, 1300)
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  background: url('https://cdn.oss-unos.hmctec.cn/common/path/c3dd1d91dcb24497a4ba71f48acb8b30.png');
  background-color: #f4f4f4;
  background-size: 100% 557rpx;
  background-repeat: no-repeat;
  padding-top: 520rpx;
  padding-bottom: 100rpx;
}

.page-body {
  padding: 35rpx 35rpx 75rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 0rpx 0rpx;
}

.apply-step-image {
  margin: 35rpx 25rpx;
  width: calc(100% - 50rpx);
  height: 323rpx;
}

.apply-btn {
  width: 100%;
  padding: 30rpx;
  background: linear-gradient(270deg, #ff564f 0%, #ffb157 100%);
  border-radius: 62rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: normal;
  font-size: 40rpx;
  color: #ffffff;
  line-height: 56rpx;
}

.feature-image {
  margin-bottom: 50rpx;
  width: 686rpx;
  height: 315rpx;
}

.tips-container {
  .tips-title {
    font-weight: 500;
    font-size: 24rpx;
    color: #666666;
    line-height: 35rpx;
    text-align: center;
  }

  .tips-content {
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 35rpx;
    text-align: center;
  }
}

::v-deep .protocol-detail-drawer {
  .drawer {
    padding: 40rpx !important;
  }
}

.protocol-detail-drawer-container {
  height: 500rpx;
  display: flex;
  flex-direction: column;

  .protocol-detail {
    flex: 1;
  }
}

.confirm {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -80rpx;
    left: 0;
    width: 100%;
    height: 80rpx;
    background: linear-gradient(360deg, #ffffff 0%, rgba(255, 255, 255, 0) 100%);
    pointer-events: none;
  }

  .confirm-button {
    background: #ff564f;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    padding: 24rpx;
    text-align: center;
    font-weight: normal;
    font-size: 36rpx;
    color: #ffffff;
  }
}

.color-link {
  color: #ff564f;
}

.protocol-container {
  margin-top: 24rpx;
  text-align: center;

  .flex-row-cc {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .f12 {
    font-size: 24rpx;
  }

  .color_6 {
    color: #666;
  }
}

.flex-wrap {
  flex-wrap: wrap;

  .color-link {
    color: #ff564f;
    margin: 0 4rpx;
  }
}
</style>
