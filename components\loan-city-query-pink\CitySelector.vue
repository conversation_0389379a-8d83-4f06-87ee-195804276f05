<template>
  <view class="city-container">
    <view class="city-label">
      <text class="label-text">常驻城市</text>
      <image
        class="city-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/46af164e07064b79998376fa99eda9a9.png"
        mode="scaleToFill"
      />
    </view>
    <view class="city-select">
      <picker
        range-key="name"
        mode="multiSelector"
        :value="cityValue"
        :range="areaData"
        @change="regionChange"
        @columnchange="regionColChange"
      >
        <view class="city-picker-wrapper">
          <input
            type="text"
            class="select-input"
            placeholder="请选择"
            :value="selectedCityName"
            disabled
          />
          <image
            class="select-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/417d1e8b1ef044ea8a914ae882492a2b.png"
            mode="scaleToFill"
          />
        </view>
      </picker>
    </view>
  </view>
</template>

<script>
import { fetchAreaData, getIpArea } from '@/apis/common-2'

export default {
  data() {
    return {
      selectedCityName: '',
      selectedCityCode: '',
      areaData: [],
      cityValue: [0, 0]
    }
  },
  mounted() {
    this.fetchAreaData()
    this.fetchAddressByIP()
  },
  methods: {
    async fetchAreaData() {
      try {
        const res = await fetchAreaData()
        const area = res[1].data
        this.areaData = [area, area[0].citys]
      } catch (error) {
        console.error('获取城市数据失败', error)
      }
    },
    async fetchAddressByIP() {
      try {
        const res = await getIpArea()
        if (res.data) {
          this.selectedCityName = res.data.cityName
          this.selectedCityCode = res.data.cityCode
          this.$emit('cityChanged', {
            cityName: this.selectedCityName,
            cityCode: this.selectedCityCode
          })
          // 定位IP城市后，需要尝试在picker中选中它
          this.trySelectIpCity(res.data.cityCode)
        }
      } catch (error) {
        console.error('获取IP地址城市失败', error)
      }
    },
    trySelectIpCity(cityCode) {
      if (!this.areaData.length || !this.areaData[0].length) return
      for (let i = 0; i < this.areaData[0].length; i++) {
        const province = this.areaData[0][i]
        if (province.citys) {
          for (let j = 0; j < province.citys.length; j++) {
            const city = province.citys[j]
            if (city.code === cityCode) {
              this.cityValue = [i, j]
              //  columnchange不会自动触发，需要手动更新第二列的数据
              this.areaData[1] = this.areaData[0][i].citys
              this.$forceUpdate() // 强制更新picker的显示
              return
            }
          }
        }
      }
    },
    regionColChange({ detail }) {
      const { column, value: provinceIndex } = detail
      if (column === 0) {
        const citys = this.areaData[0][provinceIndex].citys
        this.areaData.splice(1, 1, citys) // 使用splice确保响应性
        this.cityValue = [provinceIndex, 0]
      }
    },
    regionChange({ detail }) {
      const { value } = detail
      const [provinceIndex, cityIndex] = value
      this.selectedCityName = this.areaData[1][cityIndex].name
      this.selectedCityCode = this.areaData[1][cityIndex].code
      this.cityValue = [provinceIndex, cityIndex]
      this.$emit('cityChanged', {
        cityName: this.selectedCityName,
        cityCode: this.selectedCityCode
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.city-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10rpx;
  padding: 40rpx 10rpx;
  border-bottom: 2rpx solid #f6f6f6;

  .city-label {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .label-text {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    .city-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .city-select {
    display: flex;
    align-items: center;
    gap: 6rpx;

    .city-picker-wrapper {
      display: flex;
      align-items: center;
    }

    .select-input {
      font-weight: 400;
      font-size: 28rpx;
      text-align: right;
      pointer-events: none;
    }

    .select-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}
</style>
