<template>
  <view class="auth-container">
    <FullScreenMatchLoading ref="loading" />
  </view>
</template>
<script>
import { matchProduct, reportUV } from '@/apis/common'
import { encryptByDES } from '@/utils/encrypt'
import { nextFlowNode, setFlowNodes } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { register } from '@/apis/common-2'
import { isInWechatMiniProgram } from '@/utils/user-agent'
import { saveAppletOpenRecord } from '@/apis/common-3'

export default {
  name: 'template-v113',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {}
    }
  },

  async onLoad({ i, u }) {
    setFlowNodes(['wechat', 'overloan', 'wechat_official_account'])
    this.$u.vuex('vuex_invite', i)
    this.$u.vuex('vuex_uid', u)
    await this.register(u, i)
    this.navigateToNextFlow()
  },

  methods: {
    async register(uid, invite) {
      const res = await register({ uid, invite })
      if (res.code == 200) {
        this.form.consumerId = res.data.consumerId
        this.form.channelId = res.data.channelId

        this.$u.vuex('vuex_consumerId', this.form.consumerId)

        this.$u.vuex('vuex_channelId', res.data.channelId)

        await reportUV({
          channelId: this.form.channelId
        })

        if (isInWechatMiniProgram()) {
          await saveAppletOpenRecord({
            channelId: this.form.channelId,
            appUserId: this.form.consumerId
          })
        }
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v113/qw/index',
        overloan: '/extreme/v113/applyOther/index',
        end: '/extreme/v113/download/index',
        wechat_official_account: '/extreme/v113/wechatOfficialAccount/index'
      }

      const customFetch = {
        wechat: matchProduct,
        overloan: matchProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/3d34ff5b56ae4f299c8ef6f00cd59fc2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}
</style>
