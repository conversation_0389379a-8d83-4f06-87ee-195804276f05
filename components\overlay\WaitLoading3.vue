<template>
  <uni-popup ref="popup" type="center" :mask-click="false" mask-background-color="#F6F6F8">
    <view class="page-container">
      <view class="image-container">
        <image
          class="inner-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/0184bac34eca4d9bad30052f77da7b4e.png"
        />
        <image
          class="outer-image rotating"
          src="https://cdn.oss-unos.hmctec.cn/common/path/06ff03943b624228a9ca5f139e67c079.png"
        />
        <image
          class="outer-image2 rotating"
          src="https://cdn.oss-unos.hmctec.cn/common/path/8a98875319a94e1589c961bf04b258ee.png"
        />
      </view>
      <view class="text">正在匹配中，请稍后...</view>
      <!-- <view class="subtext">正在为您匹配最佳产品，请耐心等待...</view>
      <view class="progress">({{ progress }}/100)</view> -->
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'WaitPopup',
  data() {
    return {
      progress: 0,
      progressTimer: null,
      duration: 10 * 1000, // 默认 10 秒
      autoClose: false
    }
  },

  methods: {
    open(duration, options = {}) {
      this.duration = duration || this.duration
      this.$refs.popup.open()
      this.startProgressAnimation()
    },
    close() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
        this.progressTimer = null
      }
      this.$refs.popup.close()
    },
    startProgressAnimation() {
      this.progress = 0
      const interval = this.duration / 100 // 计算每次增加进度的时间间隔
      this.progressTimer = setInterval(() => {
        if (this.progress < 100) {
          this.progress++
        } else {
          clearInterval(this.progressTimer)
          this.progressTimer = null
          if (this.autoClose) {
            this.close()
          }
        }
      }, interval)
    }
  },

  beforeDestroy() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: transparent;
}

.image-container {
  position: relative;
  width: 330rpx;
  height: 330rpx;
}

.inner-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 70.93rpx;
  height: 81.65rpx;
  z-index: 2;
}

.outer-image {
  position: absolute;
  width: 330rpx;
  height: 330rpx;
}

.outer-image2 {
  position: absolute;
  top: calc(50% - 205rpx / 2);
  left: calc(50% - 205rpx / 2);
  width: 205rpx;
  height: 205rpx;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.text {
  margin-top: 10rpx;
  font-weight: 500;
  font-size: 36rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: center;
}

.subtext {
  margin-top: 10rpx;
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 42rpx;
  text-align: center;
}

.progress {
  font-weight: 400;
  font-size: 24rpx;
  color: #5edeff;
  line-height: 42rpx;
  text-align: center;
}
</style>
