<template>
  <view class="amount">
    <view class="title">您想最高借多少(元)</view>
    <view class="input-container">
      <input
        placeholder="请输入金额"
        :value="formattedAmount"
        @input="handleAmountInput"
        @blur="handleAmountBlur"
        @focus="handleAmountFocus"
        maxlength="6"
      />
      <view class="all-btn" @click="handleMaxAmount">全部借出</view>
    </view>
    <view class="slider-container">
      <slider
        :step="100 / 3"
        @change="handleSliderChange"
        :value="demandAmountSlider"
        backgroundColor="#F7F7F7"
        block-size="13"
      />
      <view class="slider-tick">
        <text>5万</text>
        <text>10万</text>
        <text>15万</text>
        <text>20万</text>
      </view>
    </view>
    <view class="annual-interest-rate">
      <view class="tag">限时优惠</view>
      <text>
        参考年化利率
        <text class="highlight">12%</text>
        ,1000元1天仅需
        <text class="highlight">0.3</text>
        元
      </text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'AmountInput',
  
  props: {
    value: {
      type: Number,
      default: 50000
    },
    sliderValue: {
      type: Number,
      default: 0
    }
  },
  
  data() {
    return {
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      demandAmount: this.value,
      demandAmountSlider: this.sliderValue,
      isFocused: false
    }
  },
  
  computed: {
    formattedAmount() {
      if (this.isFocused) {
        return this.demandAmount;
      } else {
        return this.formatNumber(this.demandAmount);
      }
    }
  },
  
  watch: {
    value(newVal) {
      this.demandAmount = newVal
    },
    sliderValue(newVal) {
      this.demandAmountSlider = newVal
    }
  },
  
  methods: {
    formatNumber(num) {
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    
    handleAmountInput(e) {
      // 移除输入值中的所有逗号
      const rawValue = e.detail.value.replace(/,/g, '');
      this.demandAmount = rawValue;
    },
    
    handleAmountFocus() {
      this.isFocused = true;
    },
    
    handleSliderChange({ detail }) {
      const sliderValue = detail.value

      // 找出最近的预设值
      const closestPreset = this.presetValues.reduce((prev, curr) =>
        Math.abs(curr - sliderValue) < Math.abs(prev - sliderValue) ? curr : prev
      )

      // 更新金额
      const amountIndex = this.presetValues.indexOf(closestPreset)
      const amount = this.presetAmounts[amountIndex]
      this.demandAmount = amount
      this.demandAmountSlider = closestPreset
      
      this.$emit('update:value', amount)
      this.$emit('update:sliderValue', closestPreset)
      this.$emit('change', amount)
    },

    handleMaxAmount() {
      this.demandAmount = 200000
      this.setSliderValue(200000)
      
      this.$emit('update:value', 200000)
      this.$emit('update:sliderValue', this.demandAmountSlider)
      this.$emit('change', 200000)
    },

    handleAmountBlur() {
      this.isFocused = false;
      
      const amount = parseInt(this.demandAmount)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.demandAmount = 50000
        this.setSliderValue(50000)
        
        this.$emit('update:value', 50000)
        this.$emit('update:sliderValue', this.demandAmountSlider)
        this.$emit('change', 50000)
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.demandAmount = 50000
        this.setSliderValue(50000)
        
        this.$emit('update:value', 50000)
        this.$emit('update:sliderValue', this.demandAmountSlider)
        this.$emit('change', 50000)
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.demandAmount = 200000
        this.setSliderValue(200000)
        
        this.$emit('update:value', 200000)
        this.$emit('update:sliderValue', this.demandAmountSlider)
        this.$emit('change', 200000)
        return
      }

      this.demandAmount = amount
      this.setSliderValue(amount)
      
      this.$emit('update:value', amount)
      this.$emit('update:sliderValue', this.demandAmountSlider)
      this.$emit('change', amount)
    },

    // 根据传入的金额，设置滑块的值
    setSliderValue(amount) {
      if (amount <= this.presetAmounts[0]) {
        this.demandAmountSlider = this.presetValues[0]
      } else if (amount >= this.presetAmounts[this.presetAmounts.length - 1]) {
        this.demandAmountSlider = this.presetValues[this.presetValues.length - 1]
      } else {
        for (let i = 1; i < this.presetAmounts.length; i++) {
          if (amount <= this.presetAmounts[i]) {
            const lowerAmount = this.presetAmounts[i - 1]
            const upperAmount = this.presetAmounts[i]
            const lowerValue = this.presetValues[i - 1]
            const upperValue = this.presetValues[i]

            const ratio = (amount - lowerAmount) / (upperAmount - lowerAmount)
            this.demandAmountSlider = lowerValue + ratio * (upperValue - lowerValue)
            break
          }
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.amount {
  position: relative;
  margin: 0 30rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/9e4d885c5d2e4c4b9d9ee3ba3688e1d0.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 45rpx 30rpx 30rpx;

  .title {
    font-weight: normal;
    font-size: 28rpx;
    color: #171a1d;
    line-height: 39rpx;
  }

  .input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    input {
      font-family: DIN;
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      line-height: 98rpx;
      flex: 1;
    }

    .all-btn {
      flex-shrink: 0;
      height: fit-content;
      padding: 17rpx 25rpx;
      background: #ff3154;
      box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.2);
      border-radius: 185rpx 185rpx 185rpx 185rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #eef9f3;
      line-height: 32rpx;
    }
  }

  .slider-container {
    ::v-deep slider {
      margin: 0;

      .uni-slider-track {
        background: linear-gradient(90deg, #ffeacd 0%, #ffca7b 100%) !important;
      }

      .uni-slider-handle-wrapper {
        height: 13rpx;
      }
    }

    .slider-tick {
      font-weight: 400;
      font-size: 24rpx;
      color: #747677;
      line-height: 35rpx;
      display: flex;
      justify-content: space-between;
    }
  }

  .annual-interest-rate {
    margin-top: 35rpx;
    display: flex;
    align-items: center;
    gap: 15rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 32rpx;

    .tag {
      padding: 8rpx 18rpx;
      background: linear-gradient(270deg, #ffcd85 0%, #ffab44 100%);
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 25rpx;
      flex-shrink: 0;
      border-radius: 14rpx;
    }

    .highlight {
      color: #ffc93b;
    }
  }
}
</style> 