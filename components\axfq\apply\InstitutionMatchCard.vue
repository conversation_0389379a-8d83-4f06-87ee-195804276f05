<template>
  <view class="institution-match" @click="$emit('card-click')">
    <view class="match-header">
      为您匹配<text class="institution-count">{{ productList.length }}</text>家机构，通过率预估
      <text class="rate-text">98%</text>
    </view>

    <view class="institution-list">
      <view v-for="(product, index) in productList" :key="index">
        <view class="institution-item" @click.stop="handleProductClick(product)">
          <image :src="product.logo" class="institution-logo" />
          <text class="institution-name">{{ product.productName }}</text>
          <view class="divider"></view>
          <text class="match-rate">{{ product.matchingDegree }}%匹配</text>
          <image class="arrow-icon" src="https://cdn.oss-unos.hmctec.cn/common/path/2979e2177b1b42bb895ee568aee164ad.png" />

          <!-- 复选框 -->
          <view class="product-checkbox">
            <image
              src="https://cdn.oss-unos.hmctec.cn/common/path/19f3b925dcfb42958fb6c588d9bfe752.png"
              class="product-checkbox-item"
              v-if="product.uiChecked"
              @click.stop="handleCheckboxChange(product, false)"
            />
            <image
              src="https://cdn.oss-unos.hmctec.cn/common/path/a04bce66fd5840b88ff1760355a656d5.png"
              class="product-checkbox-item"
              v-else
              @click.stop="handleCheckboxChange(product, true)"
            />
            <view class="speech-bubble" v-if="!product.uiChecked">
              <image
                class="icon-good"
                src="https://cdn.oss-unos.hmctec.cn/common/path/99a4c6f68344488eb6c0f7c925230101.png"
              />
              通过率+3%
            </view>
          </view>
        </view>

        <view class="item-line" v-if="index !== productList.length - 1"></view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'InstitutionMatchCard',

  props: {
    // 产品列表
    productList: {
      type: Array,
      default: () => []
    }
  },

  methods: {
    handleProductClick(product) {
      this.$emit('product-click', product)
    },

    handleCheckboxChange(product, checked) {
      this.$emit('checkbox-change', { product, checked })
    }
  }
}
</script>

<style scoped lang="scss">
$color-primary: #2737A8;

.institution-match {
  margin: 30rpx;
  padding: 30rpx;
  background: #ffffff;
  border-radius: 8rpx;

  .match-header {
    font-weight: 500;
    font-size: 26rpx;
    color: #333333;
    line-height: 38rpx;

    .institution-count {
      color: $color-primary;
      font-weight: 700;
    }

    .rate-text {
      color: #F00000;
    }
  }

  .institution-list {
    .institution-item {
      padding: 35rpx 0;
      display: flex;
      align-items: center;
      position: relative;

      .institution-logo {
        width: 72rpx;
        height: 72rpx;
      }

      .institution-name {
        margin-left: 16rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #3D3D3D;
        line-height: 34rpx;
      }

      .divider {
        margin: 0 8rpx;
        width: 2rpx;
        height: 31rpx;
        background-color: #D8D8D8;
      }

      .match-rate {
        margin-right: 8rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: $color-primary;
        line-height: 35rpx;
      }

      .arrow-icon {
        width: 16rpx;
        height: 16rpx;
      }

      .product-checkbox {
        position: absolute;
        right: 0;
        width: 30rpx;
        height: 30rpx;

        .speech-bubble {
          width: 160rpx;
          height: 50rpx;
          display: flex;
          align-items: center;
          gap: 5rpx;
          position: absolute;
          top: -50rpx;
          right: -30rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ffffff;
          line-height: 29rpx;
          background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/c8872af589644b15999e088c0121158e.png');
          background-size: 100% 100%;
          background-repeat: no-repeat;
          padding: 10rpx 15rpx 20rpx;

          .icon-good {
            width: 20rpx;
            height: 20rpx;
          }
        }

        .product-checkbox-item {
          width: 30rpx;
          height: 30rpx;
        }
      }
    }

    .item-line {
      height: 2rpx;
      background-color: #f6f6f6;
    }
  }
}
</style>
