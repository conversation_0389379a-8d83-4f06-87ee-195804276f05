<template>
  <view class="footer">
    <!-- 协议 -->
    <view class="agreement"
      >本人已知晓
      <!-- 高亮 -->
      <text
        v-for="(agreement, index) in agreementList"
        :key="agreement.protocolId"
        class="agreement-highlight"
        @click.stop="handleAgreementClick(index)"
        >《{{ agreement.name }}》</text
      >
      的所有内容，同意并授权平台推荐/匹配多个产品
    </view>

    <template v-if="applyUrl">
      <a class="contact-btn" :href="applyUrl" target="_blank">
        立即联系
        <text v-if="confirmCountdownNumber">（{{ confirmCountdownNumber }}S）</text>
      </a>
      <view class="cancel-btn" @click="handleCancel">取消</view>
    </template>
  </view>
</template>

<script>
export default {
  name: 'DytQwFooter',
  props: {
    agreementList: {
      type: Array,
      default: () => []
    },
    applyUrl: {
      type: String,
      default: ''
    },
    confirmCountdownNumber: {
      type: Number,
      default: 0
    }
  },
  methods: {
    handleContact() {
      this.$emit('contact')
    },
    handleCancel() {
      this.$emit('cancel')
    },
    handleAgreementClick(index) {
      this.$emit('agreement-click', index)
    }
  }
}
</script>

<style lang="scss" scoped>
.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  min-height: 414rpx;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.3);
  z-index: 10;

  .agreement {
    padding: 42rpx 0 20rpx;
    margin: 0 auto;
    width: 605rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #666666;
    line-height: 30rpx;
    text-align: center;

    .agreement-highlight {
      color: #41bdb8;
      margin: 0 4rpx;

      // 添加点击效果
      &:active {
        opacity: 0.8;
      }
    }
  }

  .contact-btn {
    margin: 0 auto 20rpx;
    width: 690rpx;
    height: 98rpx;
    background: linear-gradient(270deg, #ffb173 0%, #ff702a 100%);
    border-radius: 295rpx 295rpx 295rpx 295rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-decoration: none;
  }

  .cancel-btn {
    margin: 0 auto;
    width: 690rpx;
    height: 98rpx;
    border-radius: 295rpx 295rpx 295rpx 295rpx;
    border: 1rpx solid #999999;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 40rpx;
    color: #999999;
    line-height: 56rpx;
  }
}
</style>
