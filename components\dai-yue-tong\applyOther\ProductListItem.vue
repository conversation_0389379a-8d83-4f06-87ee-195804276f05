<template>
  <view class="product-list-item">
    <view class="product-card">
      <view class="product-header">
        <!-- 产品logo -->
        <image class="product-logo" :src="product.logo" mode="scaleToFill" />
        <view class="product-info">
          <view class="product-title">
            <view class="product-name">{{ product.name }}</view>
            <view class="product-tag">正规品牌</view>
          </view>
          <view class="product-rate"
            >年化利率 {{ product.interestRateLittle | toPriceAbbreviations }}%</view
          >
        </view>
      </view>
      <view class="product-amount">
        <view class="amount-value">{{ product.loanableFundsBig | toThousandFilter }}</view>
        <view class="amount-label">最高借款额度(元)</view>
      </view>
    </view>
    <view class="product-footer">
      <view class="installment-info">
        可分期数
        <text class="highlight-text">6-48期</text>
      </view>

      <view class="application-info"
        >已有
        <text class="highlight-text">1000</text>
        人申请
      </view>

      <view class="apply-button-secondary" @click="onApply">立即申请</view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'ProductListItem',
  props: {
    product: {
      type: Object,
      required: true,
      default: () => ({
        logo: '',
        name: '',
        loanableFundsBig: '',
        interestRateLittle: '',
        platformId: '',
        id: ''
      })
    }
  },
  methods: {
    onApply() {
      this.$emit('apply', this.product)
    }
  }
}
</script>

<style scoped lang="scss">
.product-list-item {
  margin-bottom: 24rpx;
  width: 686rpx;
  height: 246rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .product-card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    margin: 0 24rpx;
    border-bottom: 1rpx solid #f2f2f2;

    .product-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10rpx;

      .product-logo {
        width: 80rpx;
        height: 80rpx;
      }

      .product-info {
        .product-title {
          display: flex;
          align-items: center;
          gap: 12rpx;

          .product-name {
            font-weight: 700;
            font-size: 32rpx;
            color: #1a1a1a;
            line-height: 46rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 150rpx;
          }

          .product-tag {
            width: 104rpx;
            height: 32rpx;
            background: #fff7f2;
            border-radius: 4rpx 4rpx 4rpx 4rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 400;
            font-size: 24rpx;
            color: #ff9a2e;
            line-height: 28rpx;
          }
        }

        .product-rate {
          margin-top: 4rpx;
          font-weight: 400;
          font-size: 28rpx;
          color: #999999;
          line-height: 34rpx;
        }
      }
    }

    .product-amount {
      .amount-value {
        font-family: DIN, DIN;
        font-weight: 500;
        color: #1a1a1a;
        font-size: 48rpx;
        line-height: 58rpx;
      }

      .amount-label {
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 34rpx;
      }
    }
  }

  .product-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 106rpx;
    padding: 0 24rpx;

    .installment-info {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;

      .highlight-text {
        margin-left: 8rpx;
        color: #1a1a1a;
      }
    }

    .application-info {
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 40rpx;

      .highlight-text {
        color: #44bfbd;
      }
    }

    .apply-button-secondary {
      width: 152rpx;
      height: 60rpx;
      border-radius: 308rpx 308rpx 308rpx 308rpx;
      border: 2rpx solid #44bfbd;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 28rpx;
      color: #44bfbd;
      line-height: 64rpx;
    }
  }
}
</style>
