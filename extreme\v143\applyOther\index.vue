<template>
  <view class="page-container">
    <AlertBar theme="orange" />

    <view class="header">
      <view class="header-text">恭喜您咨询成功！</view>
      <view class="sub-header-text">根据您的资质，已为您匹配以下{{ productCount }}款产品</view>
    </view>

    <view class="main-product">
      <view class="main-product-info">
        <view class="product">
          <image class="product-logo" :src="mainProduct.logo"></image>
          <text class="product-name">{{ mainProduct.name }}</text>
        </view>
        <view class="product-users">
          <text class="number">1000</text>
          <text>人已放款</text>
        </view>
      </view>
      <view class="product-amount">
        <view class="amount-label">最高额度(元)</view>
        <view class="amount-value">{{ mainProduct.loanableFundsBig | toThousandFilter }}</view>
      </view>
      <view class="product-rate">
        最低年化利率
        {{ mainProduct.interestRateLittle | toPriceAbbreviations }}%
      </view>
      <view class="apply-button" @click="clickApply(mainProduct)">立即申请</view>
    </view>

    <view class="recommended-products">
      <view class="title">
        <text class="section-title">精选推荐</text>
        <text class="section-subtitle">申请产品越多，获取额度概率越高</text>
      </view>

      <view class="product-item" v-for="(item, index) in productList" :key="index">
        <view class="product">
          <image class="product-icon" :src="item.logo"></image>
          <text class="product-name">{{ item.name }}</text>
        </view>
        <view class="product-details">
          <view class="product-features">
            <view class="product-features-item">
              <text class="product-features-value highlight">{{
                item.loanableFundsBig | toThousandFilter
              }}</text>
              <text class="product-features-label">最高额度(元)</text>
            </view>
            <view class="product-features-item">
              <text class="product-features-value"
                >{{ item.interestRateLittle | toPriceAbbreviations }}%</text
              >
              <text class="product-features-label">最低年化利率</text>
            </view>
          </view>
          <view class="apply-button-small" @click="clickApply(item)">立即申请</view>
        </view>
      </view>
    </view>

    <Services />

    <FullScreenMatchLoading ref="loading" />

    <!-- 跳转弹窗 -->
    <uni-popup ref="jumpPopup" type="center" :is-mask-click="false">
      <view class="jump-popup-container">
        <view class="jump-popup-title">即将跳转到</view>
        <view class="jump-popup-confirm-btn" @click="confirmJump">立即跳转</view>
        <view class="jump-popup-countdown" v-if="countdown > 0">{{ countdown }}s后关闭</view>
      </view>
    </uni-popup>

    <!-- 底部协议组件 -->
    <!-- <AgreementFooter agreementKey="hyh-dc" @agree="handleAgreementAgree" /> -->
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import AlertBar from '@/components/alert/AlertBar.vue'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { outboundApplyProduct } from '@/apis/common'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getDcAutoFlag } from '@/apis/common'
import Services from '@/components/services/index.vue'
// import AgreementFooter from '@/components/hyh/applyOther/AgreementFooter.vue'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'applyOther',

  components: {
    AlertBar,
    FullScreenMatchLoading,
    Services
    // AgreementFooter
  },

  data() {
    return {
      form: {},
      productList: [],
      mainProduct: {},
      productCount: 0,
      countdown: 5,
      timer: null
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getFlowData()

    if (this.mainProduct.platformId && this.mainProduct.id) {
      this.getDcAutoFlag()
    }
  },

  mounted() {
    if (this.vuex_halfJumpUrl) {
      this.openJumpPopup()
    }
  },

  methods: {
    // handleAgreementAgree() {
    //   // 协议同意后的处理逻辑，如果需要的话
    // },

    async getDcAutoFlag() {
      const res = await getDcAutoFlag({
        platformId: this.mainProduct.platformId,
        productId: this.mainProduct.id
      })
      // data = 1 不自动 ，data = 2 自动
      if (res.data == 2) {
        this.applyProduct(this.mainProduct)
      }
    },

    async getFlowData() {
      const flowData = getFlowData('overloan')
      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
        this.productCount = flowData.productList.length
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await outboundApplyProduct({
        productId: product.id,
        consumerId: this.form.consumerId,
        channelId: this.form.channelId,
        uid: this.vuex_uid,
        applyType: 2
      })

      uni.hideLoading()

      if (res.data) {
        // 设置 url 到 vuex
        this.$u.vuex('vuex_overloanWebview.url', res.data)
        // 跳转到 overloan-webview 页面
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `/extreme/v143/overloanWebview/index?param=${urlParamString}`
        })
        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })
      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    openJumpPopup() {
      this.$refs.jumpPopup.open()
      this.startCountdown()
    },

    startCountdown() {
      this.countdown = 5
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        }
        if (this.countdown === 0) {
          clearInterval(this.timer)
          this.$refs.jumpPopup.close()
        }
      }, 1000)
    },

    confirmJump() {
      clearInterval(this.timer)
      this.$refs.jumpPopup.close()
      if (this.vuex_halfJumpUrl) {
        window.location.href = this.vuex_halfJumpUrl
      }
    }
  },

  beforeDestroy() {
    clearInterval(this.timer)
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  min-height: 100vh;
  position: relative;
  background-color: #f6f6f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 481rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/152637d1607c4b0dbe315e48566915cf.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;
  background-color: #fff7eb;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}

.header {
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  position: relative;

  .header-text {
    margin-bottom: 10rpx;
    font-family: UPFont-WuweiGB-Flash;
    font-weight: 400;
    font-size: 52rpx;
    color: #16283c;
    line-height: 52rpx;
    letter-spacing: 3px;
    text-align: center;
  }

  .sub-header-text {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 41rpx;
    text-align: center;
  }
}

.main-product {
  padding: 35rpx 0;
  margin: 0 30rpx;
  position: relative;
  background: linear-gradient(180deg, #1678ff 0%, #7db4ff 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .main-product-info {
    display: flex;
    justify-content: space-between;

    .product {
      padding-left: 35rpx;
      display: flex;
      align-items: center;
      gap: 15rpx;

      .product-logo {
        width: 48rpx;
        height: 48rpx;
      }

      .product-name {
        font-weight: 400;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 46rpx;
      }
    }

    .product-users {
      display: flex;
      align-items: center;
      padding: 10rpx 7rpx 10rpx 10rpx;
      background: linear-gradient(270deg, #ffcc83 0%, #ffab45 100%);
      border-radius: 20rpx 0rpx 0rpx 20rpx;
      font-weight: 400;
      font-size: 20rpx;
      color: #5a352f;
      line-height: 28rpx;

      .number {
        font-size: 24rpx;
      }
    }
  }

  .product-amount {
    margin-top: 5rpx;

    .amount-label {
      font-weight: normal;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 34rpx;
      text-align: center;
    }

    .amount-value {
      font-family: DIN;
      font-weight: 700;
      font-size: 72rpx;
      color: #ffffff;
      line-height: 78rpx;
      text-align: center;
    }
  }

  .product-rate {
    font-weight: normal;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 34rpx;
    text-align: center;
  }

  .apply-button {
    margin: 20rpx 55rpx 0;
    background: linear-gradient(180deg, #eaf3ff 0%, #ffffff 100%);
    border-radius: 46rpx 46rpx 46rpx 46rpx;
    border: 1rpx solid #ffffff;
    font-weight: 400;
    font-size: 36rpx;
    color: $color-primary;
    line-height: 52rpx;
    padding: 12rpx;
    text-align: center;
  }
}

.recommended-products {
  margin: 30rpx;

  .title {
    margin-bottom: 25rpx;
    display: flex;
    gap: 16rpx;
    align-items: flex-end;

    .section-title {
      font-weight: 700;
      font-size: 36rpx;
      color: #3d3d3d;
      line-height: 48rpx;
    }

    .section-subtitle {
      font-size: 24rpx;
      color: #999999;
      line-height: 34rpx;
    }
  }

  .product-item {
    margin-bottom: 30rpx;
    padding: 35rpx;
    background: #ffffff;
    border-radius: 16rpx 16rpx 16rpx 16rpx;
    border: 1rpx solid #ffffff;

    .product {
      margin-bottom: 20rpx;
      display: flex;
      gap: 20rpx;

      .product-icon {
        width: 48rpx;
        height: 48rpx;
      }

      .product-name {
        font-weight: 400;
        font-size: 32rpx;
        color: #171a1d;
        line-height: 45rpx;
      }
    }

    .product-details {
      display: flex;
      gap: 80rpx;

      .product-features {
        flex: 1;
        display: flex;
        justify-content: space-between;

        .product-features-item {
          display: flex;
          flex-direction: column;
          gap: 8rpx;
          align-items: center;

          .product-features-value {
            font-family: DIN;
            font-weight: 700;
            font-size: 38rpx;
            color: #171a1d;
            line-height: 41rpx;

            &.highlight {
              color: #f55c4d;
            }
          }

          .product-features-label {
            font-weight: normal;
            font-size: 24rpx;
            color: #919094;
            line-height: 34rpx;
          }
        }
      }

      .apply-button-small {
        padding: 12rpx 25rpx;
        border-radius: 46rpx 46rpx 46rpx 46rpx;
        border: 2rpx solid $color-primary;
        font-weight: 400;
        font-size: 36rpx;
        color: $color-primary;
        line-height: 50rpx;
      }
    }
  }
}

.jump-popup-container {
  position: relative;
  padding: 345rpx 70rpx 50rpx;
  width: 610rpx;
  background: linear-gradient(180deg, #ffc0c9 0%, #ffffff 50%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .jump-popup-countdown {
    position: absolute;
    top: 30rpx;
    right: 20rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #ffffff;
    line-height: 48rpx;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/5b30e383ab2a4be79d3865467dc5bac8.png');
    background-size: 550rpx 405rpx;
    background-repeat: no-repeat;
    background-position: center top;
  }

  .jump-popup-title {
    position: relative;
    margin-bottom: 50rpx;
    font-weight: 500;
    font-size: 38rpx;
    color: #3d3d3d;
    line-height: 48rpx;
    text-align: center;
  }

  .jump-popup-confirm-btn {
    position: relative;
    margin-bottom: 15rpx;
    padding: 23rpx;
    background: $color-primary;
    border-radius: 47rpx 47rpx 47rpx 47rpx;
    font-weight: 500;
    font-size: 38rpx;
    color: #ffffff;
    line-height: 48rpx;
    text-align: center;
  }
}
</style>
