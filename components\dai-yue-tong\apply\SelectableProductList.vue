<template>
  <view class="product-list">
    <selectable-product-list-item
      v-for="(item, index) in productList"
      :key="index"
      :product="item"
      :is-selected="selectedProducts.includes(item)"
      @toggle-select="toggleProductSelection"
    />
  </view>
</template>

<script>
import SelectableProductListItem from './SelectableProductListItem.vue'

export default {
  name: 'SelectableProductList',
  components: {
    SelectableProductListItem
  },
  props: {
    productList: {
      type: Array,
      default: () => []
    },
    defaultSelectedAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      selectedProducts: []
    }
  },
  created() {
    // 如果默认全选，初始化时选择所有产品
    if (this.defaultSelectedAll && this.productList.length > 0) {
      this.selectedProducts = [...this.productList]
      // 触发选择变更事件，通知父组件
      this.$emit('selection-change', this.selectedProducts)
    }
  },
  watch: {
    // 监听产品列表变化，如果默认全选且产品列表有变化，则重新全选
    productList: {
      handler(newVal) {
        if (
          this.defaultSelectedAll &&
          newVal.length > 0 &&
          this.selectedProducts.length !== newVal.length
        ) {
          this.selectedProducts = [...newVal]
          this.$emit('selection-change', this.selectedProducts)
        }
      },
      deep: true
    }
  },
  methods: {
    toggleProductSelection(product) {
      const index = this.selectedProducts.findIndex((p) => p.name === product.name)
      if (index === -1) {
        this.selectedProducts.push(product)
      } else {
        this.selectedProducts.splice(index, 1)
      }
      this.$emit('selection-change', this.selectedProducts)
    }
  }
}
</script>

<style scoped lang="scss">
.product-list {
  width: 686rpx;
  margin: 32rpx auto 0;
}
</style>
