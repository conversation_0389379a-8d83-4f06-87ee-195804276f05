<script>
export default {
  name: 'declaration-szhh',

  methods: {
  }
}
</script>

<template>
  <view class="declaration-container">
    <view v-if="vuex_declaration && !vuex_templateSig" v-html="vuex_declaration"></view>
    <template v-else>
      <view>
        <view>粤ICP备2023134169号</view>
        <view>深圳瀚华小额贷款有限公司</view>
        <view>综合年化利率7.2%-24%（单利）</view>
        <view>资金来源：深圳瀚华小额贷款有限公司及合作机构等</view>
        <view>经营地址:深圳市前海深港合作区前湾一路1号A栋201室</view>
        <view>贷款有风险，借款需谨慎，借款额度、放款时间以实际审批为准，公积金相关资料仅作为贷款服务验资证明放贷路径与公积金账户无关</view>
        <view>客服热线：400-005-1922</view>
        <view>服务时间：09:00~18:00(工作日)</view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.declaration-container {
  padding: 30rpx 60rpx;
  font-size: 28rpx;
  color: #999;
  text-align: center;

  .police-icon {
    width: 30rpx;
    height: 30rpx;
  }
}
</style> 