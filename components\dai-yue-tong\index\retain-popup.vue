<template>
  <uni-popup
    :is-mask-click="false"
    background-color="#fff"
    ref="retainPopup"
    type="center"
    mask-background-color="rgba(0, 0, 0, 0.7)"
    border-radius="56rpx 56rpx 56rpx 56rpx"
  >
    <view class="retain-popup">
      <view class="retain-popup__title">现在放弃您将失去</view>
      <view class="retain-popup__amount"
        >最高
        <!-- 高亮 -->
        <text class="retain-popup__amount-highlight">20万额度</text>
      </view>
      <view class="retain-popup__stats">
        <view class="retain-popup__stats-item">
          <!-- icon -->
          <image
            class="retain-popup__stats-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/d4ef3ef9d7ef4e0fbde9ac316e454930.png"
            mode="scaleToFill"
          />
          <view class="retain-popup__stats-text">
            今日放款率
            <!-- 高亮 -->
            <text class="retain-popup__stats-highlight">95%</text>
          </view>
        </view>
        <view class="retain-popup__stats-item">
          <!-- icon -->
          <image
            class="retain-popup__stats-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/d4ef3ef9d7ef4e0fbde9ac316e454930.png"
            mode="scaleToFill"
          />
          <view class="retain-popup__stats-text">
            今日申请最快
            <!-- 高亮 -->
            <text class="retain-popup__stats-highlight">当天到账</text>
          </view>
        </view>
      </view>

      <view class="retain-popup__subtitle">填写手机号 快速提现到账</view>
      <view class="retain-popup__submit-btn" @click="handleContinue">立即申请</view>
      <view class="retain-popup__cancel-btn" @click="handleExit">放弃额度</view>

      <!-- 关闭icon -->
      <image
        class="retain-popup__close-icon"
        src="https://cdn.oss-unos.hmctec.cn/common/path/5bf70001a2bf40dc8ec0f1dd75460eb7.png"
        mode="scaleToFill"
        @click="handleExit"
      />
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'RetainPopup',

  methods: {
    open() {
      this.$refs.retainPopup.open()
    },

    close() {
      this.$refs.retainPopup.close()
    },

    handleContinue() {
      this.$emit('continue')
      this.close()
    },

    handleExit() {
      this.$emit('exit')
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.retain-popup {
  position: relative;
  padding-top: 64rpx;
  padding-bottom: 44rpx;
  width: 600rpx;
  height: 684rpx;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/7a4333469fcb4891a4fa909b34e7362a.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;

  &__title {
    margin-bottom: 20rpx;
    font-size: 32rpx;
    font-weight: 500;
    color: #1a1a1a;
    line-height: 44.08rpx;
    padding: 0 44rpx;
  }

  &__amount {
    padding: 0 44rpx;
    margin-bottom: 50rpx;
    font-size: 56rpx;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 44.08rpx;

    &-highlight {
      color: #f96a39;
    }
  }

  &__stats {
    padding: 0 44rpx;
    &-item {
      display: flex;
      align-items: center;
      gap: 32rpx;
      margin-bottom: 26rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &-icon {
      width: 40rpx;
      height: 40rpx;
    }

    &-text {
      font-weight: 400;
      font-size: 36rpx;
      color: #1a1a1a;
      line-height: 50rpx;
    }

    &-highlight {
      color: #ff702a;
    }
  }

  &__subtitle {
    margin-top: 50rpx;
    margin-bottom: 20rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 34rpx;
    text-align: center;
  }

  &__submit-btn {
    margin: 0 auto 24rpx;
    width: 492rpx;
    height: 112rpx;
    background: linear-gradient(284deg, #ff702a 9%, #ffb072 100%);
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 64rpx;
  }

  &__cancel-btn {
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 40rpx;
    text-align: center;
  }

  &__close-icon {
    position: absolute;
    top: -100rpx;
    right: 0;
    width: 40rpx;
    height: 40rpx;
  }
}
</style>
