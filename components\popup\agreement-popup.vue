<template>
  <uni-popup
    background-color="#fff"
    ref="popup"
    type="bottom"
    border-radius="32rpx 32rpx 0 0"
    :is-mask-click="false"
  >
    <view class="agreement-popup">
      <!-- <view class="agreement-header">
        <text class="agreement-title">{{title}}</text>
        <uni-icons type="closeempty" size="20" color="#999" @click="$refs.popup.close()"></uni-icons>
      </view> -->

      <scroll-view class="agreement-content" scroll-y>
        <web-view :src="url" :fullscreen="false" />
      </scroll-view>

      <view class="agreement-footer">
        <button class="agree-btn" @click="handleAgree">同意并继续</button>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'agreement-popup',

  props: {
    title: {
      type: String,
      default: '用户协议'
    }
  },

  data() {
    return {
      url: '',
      confirm: () => {}
    }
  },

  methods: {
    open(options = {}) {
      this.url = options.url || ''
      this.confirm = options.confirm || (() => {})
      this.$refs.popup.open()
    },

    handleAgree() {
      this.confirm()
      this.$refs.popup.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/********************************.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 32rpx;

    .agreement-title {
      font-weight: normal;
      font-size: 40rpx;
      color: #3d3d3d;
      line-height: 56rpx;
    }
  }

  .agreement-content {
    height: 700rpx;
    padding: 24rpx 0;
    overflow: auto;
    font-size: 26rpx;
    color: #3d3d3d;
    line-height: 42rpx;

    ::v-deep iframe,
    web-view {
      width: 100% !important;
      height: 100% !important;
      border: none !important;
    }
  }

  .agreement-footer {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agree-btn {
      width: 100%;
      height: 88rpx;
      line-height: 88rpx;
      text-align: center;
      background: linear-gradient(90deg, #ee9a36 0%, #e95728 100%);
      border-radius: 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      border: none;

      &:active {
        opacity: 0.8;
      }
    }
  }
}
</style>
