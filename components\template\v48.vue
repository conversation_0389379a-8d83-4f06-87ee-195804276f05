<template>
  <view class="page-container">
    <view class="page-content">
      <view class="page-content-title">
        <view class="title-text">金利花</view>
        <view class="desc-text">新平台28天内还款0息费</view>
      </view>

      <view class="form">
        <view class="form-title"> 请输入身份信息完成身份认证 </view>
        <view class="form-item">
          <view class="form-item-label">
            姓
            <view style="width: 32rpx; display: inline-block"></view>
            名
          </view>
          <view class="form-item-input">
            <input
              type="text"
              placeholder="请输入姓名"
              v-model="form.name"
              @blur="nameBlur"
              maxlength="20"
              placeholder-style="overflow: visible;"
            />
          </view>
        </view>
        <view class="form-item">
          <view class="form-item-label">手机号</view>
          <view class="form-item-input">
            <input
              type="text"
              placeholder="请输入手机号"
              v-model="form.phone"
              @blur="phoneBlur"
              maxlength="11"
              placeholder-style="overflow: visible;"
            />
          </view>
        </view>

        <view class="channel-info"> 渠道号:NO.5519 </view>

        <view class="submit-btn" @click="handleSubmit">一键提现</view>

        <view class="agreement" @click="agreementChecked = !agreementChecked">
          <image
            v-if="agreementChecked"
            class="agree-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/592c8d09c817442ebb63cc750cfc173a.png"
          ></image>
          <image
            v-else
            class="agree-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/ca29794ca1564662ad1a6be4cd760903.png"
          ></image>

          <view class="agreement-text">
            我已阅读并同意
            <text
              class="name"
              v-for="(item, index) in agreementList"
              :key="index"
              @click.stop="clickAgreement(item)"
            >
              《{{ item.name }}》
            </text>
          </view>
        </view>
      </view>
    </view>
    <view class="user-scores">
      <view class="user-scores-title">
        <view class="title-text"
          >评分待
          <text class="highlight">提升</text>
        </view>
        <view class="desc-text">满进度后即可提现</view>
      </view>

      <template v-if="showUserScores">
        <view class="progress-bar">
          <view class="progress">
            <view class="progress-inner" :style="{ width: `${progressPercentage}%` }">
              <view class="progress-text">{{ Math.round(progressPercentage) }}%</view>
            </view>
          </view>
          <view class="progress-status">授信成功</view>
        </view>

        <view class="behavior-list">
          <view
            class="behavior-item"
            v-for="(item, index) in behaviorList"
            :key="index"
            :class="{ 'animate-in': currentAnimationIndex >= index }"
            @click="handleBehaviorClick(item)"
          >
            <view class="behavior-icon-container">
              <image class="behavior-icon" :src="item.icon" />
            </view>
            <view class="behavior-label-container">
              <view class="behavior-label">{{ item.label }}</view>
              <view class="behavior-desc">已增加{{ item.percentage }}%</view>
            </view>
            <view class="behavior-status">
              <view :class="item.status === 'success' ? 'success' : 'fail'">
                <view>{{ item.status === 'success' ? '已拥有' : '未通过' }}</view>
                <image
                  :src="
                    item.status === 'success'
                      ? 'https://cdn.oss-unos.hmctec.cn/common/path/9b6e1d161d5042caa96a6800ff9359f0.png'
                      : 'https://cdn.oss-unos.hmctec.cn/common/path/866a8643c80e4ec18a3b0d13a7677770.png'
                  "
                  class="status-icon"
                />
              </view>
            </view>
          </view>
        </view>
      </template>

      <template v-else>
        <view class="user-scores-empty">
          <image
            class="empty-image"
            src="https://cdn.oss-unos.hmctec.cn/common/path/4e554c7db2ea4535ab1479870eb8e77c.png"
          />
          <view class="empty-text"> 暂无数据 </view>
          <view class="empty-desc"> 请填写身份信息进行匹配~ </view>
        </view>
      </template>
    </view>

    <view class="apply-steps-title">三步申请</view>

    <view class="apply-steps">
      <view class="apply-step">
        <image
          class="apply-step-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/0b347ff306804ef497b4a546b5b642f1.png"
          mode="scaleToFill"
        />
        <view class="apply-step-label">填写身份信息</view>
      </view>
      <image
        class="apply-step-arrow"
        src="https://cdn.oss-unos.hmctec.cn/common/path/66abd422d9194977bb70836e78c533b5.png"
        mode="scaleToFill"
      />
      <view class="apply-step">
        <image
          class="apply-step-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/a812f313e6784d31b715e70203d1d01d.png"
          mode="scaleToFill"
        />
        <view class="apply-step-label">联系客户经理</view>
      </view>
      <image
        class="apply-step-arrow"
        src="https://cdn.oss-unos.hmctec.cn/common/path/66abd422d9194977bb70836e78c533b5.png"
        mode="scaleToFill"
      />
      <view class="apply-step">
        <image
          class="apply-step-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/db034b8c05ca4473a810d54887048b52.png"
          mode="scaleToFill"
        />
        <view class="apply-step-label">借款提现</view>
      </view>
    </view>

    <view class="reason-title">选择我们的理由</view>

    <view class="reason-list">
      <view class="reason-item">
        <image
          class="reason-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/3f8ed15627cd4647bea80d60799b866d.png"
          mode="aspectFit"
        />
        <view class="reason-content">
          <view class="reason-label">期限灵活</view>
          <view class="reason-desc">最长分期96期</view>
        </view>
      </view>
      <view class="reason-item">
        <image
          class="reason-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/b59e3a4fa5ee404ba5d677a0e226ed79.png"
          mode="aspectFit"
        />
        <view class="reason-content">
          <view class="reason-label">安全保障</view>
          <view class="reason-desc">信息多重加密</view>
        </view>
      </view>
      <view class="reason-item">
        <image
          class="reason-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/5b9b833e707447e89903d2c5c00dcb98.png"
          mode="aspectFit"
        />
        <view class="reason-content">
          <view class="reason-label">智能提额</view>
          <view class="reason-desc">即时额度评估</view>
        </view>
      </view>
      <view class="reason-item">
        <image
          class="reason-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/3f8ed15627cd4647bea80d60799b866d.png"
          mode="aspectFit"
        />
        <view class="reason-content">
          <view class="reason-label">贴心服务</view>
          <view class="reason-desc">便捷快速办理</view>
        </view>
      </view>
    </view>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="submitPopup"
      type="center"
      border-radius="24rpx"
    >
      <view class="submit-popup">
        <view class="submit-popup-title"> 选择额度 </view>

        <view class="contact-manager-tip">
          <image
            class="tip-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/e1a7a645d4904c75bb80073b1716002c.png"
            mode="aspectFit"
          />
          请联系您的客户经理
        </view>

        <view class="number-list">
          <view
            v-for="(item, index) in numberOptions"
            :key="index"
            class="number-item"
            :class="{ selected: selectedNumber === item }"
            @click="selectNumber(item)"
          >
            {{ item }}元
          </view>
        </view>

        <image
          class="tips-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/1efceeaf3bce49a2b64dbdedaf51b641.png"
          mode="scaleToFill"
        />

        <view class="submit-popup-desc" @click="clickContactManager"
          >请联系您的客户经理
          <text class="highlight">NO.5519</text>
          进行审核
        </view>

        <view class="submit-popup-btn" @click="handleConfirm">确定</view>

        <image
          class="close-button"
          src="https://cdn.oss-unos.hmctec.cn/common/path/cc6cb1496eab418da8a0690c23500d77.png"
          @click="closeSubmitPopup"
        />
      </view>
    </uni-popup>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="contactManagerPopup"
      type="bottom"
      border-radius="40rpx"
    >
      <view class="contact-manager-popup">
        <image
          class="close-btn"
          src="https://cdn.oss-unos.hmctec.cn/common/path/f6c868fc4bb148afa0206be3918b50cc.png"
          @click="closeContactManagerPopup"
        />

        <image
          class="tips-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/1efceeaf3bce49a2b64dbdedaf51b641.png"
          mode="scaleToFill"
        />

        <view class="submit-popup-desc" @click="clickContactManager"
          >请联系您的客户经理
          <text class="highlight">NO.5519</text>
          进行审核
        </view>

        <view class="contact-manager-btn" @click="clickContactManager"> 去联系 </view>
      </view>
    </uni-popup>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="matchPopup"
      type="center"
      border-radius="24rpx"
    >
      <view class="match-popup">
        <view class="match-status-loading">
          <image
            class="icon-circle"
            :class="{ rotating: isRotating }"
            src="https://cdn.oss-unos.hmctec.cn/common/path/cfedf4814cec475f8055ecef94571778.png"
            mode="scaleToFill"
          />
          <image
            class="icon-safe"
            src="https://cdn.oss-unos.hmctec.cn/common/path/b5137e62817143c19bc7491295b41f06.png"
            mode="scaleToFill"
          />
          <view class="match-countdown" v-if="matchCountdown > 0">{{ matchCountdown }}S</view>
        </view>

        <view class="match-status-desc"> 加载中，请稍等 </view>

        <view class="match-list">
          <view class="match-item" v-for="(item, index) in matchItems" :key="index">
            <view class="match-item-label">{{ item.label }}</view>
            <svg class="match-item-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
              <circle
                cx="50"
                cy="50"
                r="45"
                fill="none"
                stroke="#35D977"
                stroke-width="5"
                stroke-dasharray="283"
                stroke-dashoffset="283"
              >
                <animate
                  attributeName="stroke-dashoffset"
                  from="283"
                  to="0"
                  dur="0.6s"
                  fill="freeze"
                  :begin="`${index * 1}s`"
                />
              </circle>
              <path
                d="M30 50 L45 65 L70 40"
                fill="none"
                stroke="#35D977"
                stroke-width="5"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-dasharray="80"
                stroke-dashoffset="80"
              >
                <animate
                  attributeName="stroke-dashoffset"
                  from="80"
                  to="0"
                  dur="0.4s"
                  fill="freeze"
                  :begin="`${index * 1 + 0.6}s`"
                />
              </path>
            </svg>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup
      :is-mask-click="false"
      background-color="#F6F6F6"
      ref="agreementPopup"
      type="center"
      border-radius="24rpx"
    >
      <view class="agreement-popup">
        <view class="popup-body">
          <image
            class="icon-loading"
            src="https://cdn.oss-unos.hmctec.cn/common/path/5354deffb1bb4191b0979575f8d48a69.png"
          />
          <view class="loading-text">正在加载...</view>
        </view>

        <view class="popup-footer" @click="clickCancel">取消</view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
import test from '@/utils/test'
import { getPhoneApplyProgress } from '@/apis/common'

export default {
  name: 'template-v33',

  data() {
    return {
      agreementChecked: true,
      agreementList: [
        {
          name: '用户服务协议'
        }
      ],
      contactManagerLink: 'https://work.weixin.qq.com/kfid/kfc09394822e689bac2',
      numberOptions: [1000, 10000, 20000],
      selectedNumber: 20000,
      behaviorList: [
        {
          icon: 'https://cdn.oss-unos.hmctec.cn/common/path/85ca05d75f5149b6baec2b2176633e47.png',
          label: '网贷行为记录',
          percentage: 20,
          status: 'success'
        },
        {
          icon: 'https://cdn.oss-unos.hmctec.cn/common/path/85ca05d75f5149b6baec2b2176633e47.png',
          label: '身份核实',
          percentage: 20,
          status: 'success'
        },
        {
          icon: 'https://cdn.oss-unos.hmctec.cn/common/path/85ca05d75f5149b6baec2b2176633e47.png',
          label: '守信行为记录',
          percentage: 20,
          status: 'success'
        },
        {
          icon: 'https://cdn.oss-unos.hmctec.cn/common/path/85ca05d75f5149b6baec2b2176633e47.png',
          label: '金融行为记录',
          percentage: 40,
          status: 'fail'
        }
      ],
      form: {
        name: '',
        phone: ''
      },
      progressPercentage: 60,
      showUserScores: false,
      currentAnimationIndex: -1,
      matchCountdown: 3,
      isRotating: false,
      matchItems: [
        { label: '正在查询逾期情况' },
        { label: '正在查询互联网大数据' },
        { label: '正在查询履约情况' }
      ]
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  methods: {
    clickAgreement() {
      this.$refs.agreementPopup.open()
    },

    clickCancel() {
      this.$refs.agreementPopup.close()
    },

    selectNumber(number) {
      this.selectedNumber = number
    },

    validName() {
      if (!this.form.name) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        })
        return false
      }

      if (!test.chinese(this.form.name)) {
        uni.showToast({
          title: '请输入正确的姓名',
          icon: 'none'
        })
        return false
      }

      return true
    },

    validPhone() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return false
      }

      if (!test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return false
      }

      return true
    },

    nameBlur() {
      this.form.name = this.form.name ? this.form.name.trim() : ''
      return this.validName()
    },

    phoneBlur() {
      this.form.phone = this.form.phone ? this.form.phone.trim() : ''
      return this.validPhone()
    },

    validAgreement() {
      if (!this.agreementChecked) {
        uni.showToast({
          title: '请同意用户服务协议',
          icon: 'none'
        })
        return false
      }
      return true
    },

    handleSubmit() {
      if (this.validName() && this.validPhone() && this.validAgreement()) {
        this.showUserScores = false

        this.getPhoneApplyProgress()

        this.showMatchPopup()
      }
    },

    async getPhoneApplyProgress() {
      const res = await getPhoneApplyProgress({
        phone: this.form.phone,
        name: this.form.name
      })
      this.$u.vuex('vuex_phone', this.form.phone)

      this.progressPercentage = res.data.totalProgress

      this.behaviorList = res.data.itemProgressList.map((item) => {
        return {
          icon: 'https://cdn.oss-unos.hmctec.cn/common/path/85ca05d75f5149b6baec2b2176633e47.png',
          label: item.itemName,
          percentage: item.itemProgress,
          status: item.hasProgress ? 'success' : 'fail'
        }
      })
    },

    handleConfirm() {
      this.$refs.submitPopup.close()
      this.showUserScores = true
      this.$nextTick(() => {
        this.animateProgress()
        this.animateBehaviorItems()
      })
    },

    handleBehaviorClick(item) {
      if (item.status === 'fail') {
        this.$refs.contactManagerPopup.open()
      }
    },

    calculateProgressPercentage() {
      // return this.behaviorList
      //   .filter(item => item.status === 'success')
      //   .reduce((sum, item) => sum + item.percentage, 0);

      return this.progressPercentage
    },

    animateProgress() {
      const duration = 1500
      const start = 0
      const end = this.calculateProgressPercentage()
      let current = start

      const easeOutCubic = (t) => {
        return 1 - Math.pow(1 - t, 3)
      }

      const animate = (timestamp) => {
        if (!this.animationStartTime) {
          this.animationStartTime = timestamp
        }

        const elapsed = timestamp - this.animationStartTime
        const progress = Math.min(elapsed / duration, 1)

        current = start + (end - start) * easeOutCubic(progress)
        this.progressPercentage = Math.min(current, end)

        // 当进度达到40%时开始显示评分项
        if (this.progressPercentage >= 40 && this.currentAnimationIndex === -1) {
          this.animateBehaviorItems()
        }

        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          this.progressPercentage = end
          this.animationStartTime = null
          this.animationFinished = true
        }
      }

      this.animationStarted = true
      requestAnimationFrame(animate)
    },

    animateBehaviorItems() {
      this.currentAnimationIndex = -1
      const delay = 200
      this.behaviorList.forEach((item, index) => {
        setTimeout(() => {
          this.currentAnimationIndex = index
        }, index * delay)
      })
    },

    closeSubmitPopup() {
      this.handleConfirm()
    },

    closeContactManagerPopup() {
      this.$refs.contactManagerPopup.close()
    },

    showMatchPopup() {
      this.$refs.matchPopup.open()
      this.startMatchCountdown()
      this.isRotating = true
    },

    startMatchCountdown() {
      this.matchCountdown = 3
      const countdownInterval = setInterval(() => {
        this.matchCountdown--
        if (this.matchCountdown <= 0) {
          clearInterval(countdownInterval)
          setTimeout(() => {
            this.$refs.matchPopup.close()

            // 直接展示用户评分
            this.showUserScores = true
            this.$nextTick(() => {
              this.animateProgress()
              this.animateBehaviorItems()
            })
          }, 500)
        }
      }, 1000)
    },

    // 新增方法来停止旋转
    stopRotating() {
      this.isRotating = false
    },

    closeMatchPopup() {
      this.$refs.matchPopup.close()
      this.stopRotating()
    },

    clickContactManager() {
      window.location.href = this.contactManagerLink
    }
  }
}
</script>
<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #e25e4f 46%, rgba(255, 255, 255, 0) 97%);
  background-size: 100% 741rpx;
  background-repeat: no-repeat;
  padding-bottom: 100rpx;
}

.page-content {
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/72599919cad94e91bac0208976bc657a.png');
  background-repeat: no-repeat;
  background-size: 100% 499rpx;
  background-position: 0 -68rpx;

  .page-content-title {
    padding: 60rpx 35rpx 30rpx;

    .title-text {
      font-family:
        Alimama ShuHeiTi,
        Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 82rpx;
      color: #ffcd95;
      line-height: 95rpx;
      margin-bottom: 20rpx;
    }

    .desc-text {
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 26rpx;
    }
  }
}

.form {
  margin: 0 20rpx;
  background: #ffffff;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  padding: 30rpx 0 50rpx;

  .form-title {
    padding: 0 50rpx 40rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 54rpx;
  }

  .form-item {
    display: flex;
    align-items: center;
    margin: 0 50rpx;
    padding: 25rpx 0;
    border-bottom: 1rpx solid #ededed;
    gap: 40rpx;

    .form-item-label {
      font-weight: 400;
      font-size: 32rpx;
      color: #333333;
      line-height: 30rpx;
    }

    .form-item-input {
      input {
        font-weight: 400;
        font-size: 32rpx;
        color: #333333;
        line-height: 30rpx;
      }
    }
  }

  .channel-info {
    margin: 45rpx 0 25rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 20rpx;
    text-align: center;
  }

  .submit-btn {
    margin: 0 40rpx;
    padding: 18rpx;
    background: #e25e4f;
    border-radius: 285rpx;
    font-weight: 400;
    font-size: 36rpx;
    color: #ffffff;
    line-height: 52rpx;
    text-align: center;
  }
}

.submit-popup {
  position: relative;
  padding: 50rpx 35rpx 70rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/2deffd4f66e34542aba9aab9b1aa2832.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .submit-popup-title {
    font-weight: 500;
    font-size: 32rpx;
    color: #171a1d;
    line-height: 40rpx;
  }

  .contact-manager-tip {
    font-weight: 400;
    font-size: 24rpx;
    color: #e25e4f;
    line-height: 35rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15rpx;

    .tip-icon {
      width: 26rpx;
      height: 26rpx;
      margin-right: 3rpx;
    }
  }

  .number-list {
    margin-bottom: 68rpx;
    display: flex;
    justify-content: space-between;
    gap: 30rpx;

    .number-item {
      width: 176rpx;
      height: 64rpx;
      background: #ffffff;
      border-radius: 12rpx 12rpx 12rpx 12rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 34rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      border: 1rpx solid #ffffff;
      cursor: pointer;

      &.selected {
        background: #fff6f1;
        border-color: #e25e4f;
        color: #e25e4f;
        position: relative;
        overflow: hidden;

        &::after {
          content: '';
          position: absolute;
          top: 0px;
          left: 0px;
          right: 0px;
          bottom: 0px;
          background: linear-gradient(
            to bottom left,
            transparent calc(50% - 1rpx),
            #e25e4f,
            transparent calc(50% + 1rpx)
          );
          pointer-events: none;
        }
      }
    }
  }

  .tips-image {
    width: 385.68rpx;
    height: 53.18rpx;
    display: block;
    margin: 0 auto;
    margin-bottom: 35rpx;
  }

  .submit-popup-desc {
    margin-bottom: 89rpx;
    font-weight: 400;
    font-size: 34rpx;
    color: #666666;
    line-height: 49rpx;

    .highlight {
      color: #1c80ff;
      font-weight: 500;
      font-size: 36rpx;
      text-decoration: underline;
    }
  }

  .submit-popup-btn {
    margin: 0 auto;
    padding: 20rpx;
    width: calc(100% - 32rpx);
    background: #e25e4f;
    border-radius: 62rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .close-button {
    width: 72rpx;
    height: 72rpx;
    position: absolute;
    left: 50%;
    bottom: -132rpx; // 60rpx距离 + 72rpx按钮高度
    transform: translateX(-50%);
  }
}

.user-scores {
  margin: 35rpx 20rpx;
  background: linear-gradient(180deg, #fff1eb 0%, #ffffff 10%);
  border-radius: 8rpx;
  padding: 25rpx 0 50rpx;

  .user-scores-title {
    display: flex;
    align-items: flex-end;
    margin: 0 40rpx 30rpx;
    gap: 10rpx;

    .title-text {
      font-family:
        Alimama ShuHeiTi,
        Alimama ShuHeiTi;
      font-weight: 700;
      font-size: 42rpx;
      color: #353430;
      line-height: 45rpx;

      .highlight {
        color: #e25e4f;
      }
    }

    .desc-text {
      font-weight: 400;
      font-size: 32rpx;
      color: #666666;
      line-height: 40rpx;
    }
  }

  .progress-bar {
    background: #fff6f1;
    border-radius: 339rpx;
    margin: 0 40rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40rpx;

    .progress {
      flex: 1;
      border-radius: 339rpx;
      padding: 5rpx;
      display: flex;
      align-items: center;

      .progress-inner {
        background: linear-gradient(90deg, #e25e4f 0%, #f48e4b 100%);
        height: 34rpx;
        border-radius: 339rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding: 0 15rpx;
        min-width: fit-content;
        position: relative;
        overflow: hidden;
        transition: width 0.05s linear; // 使用更短的过渡时间，让动画更流畅
        box-shadow: 0 2rpx 10rpx rgba(226, 94, 79, 0.3);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          animation: shimmer 2s infinite;
        }

        &.animation-finished::before {
          display: none; // 动画结束后移除反光效果
        }

        .progress-text {
          font-weight: 400;
          font-size: 24rpx;
          color: #ffffff;
          position: relative;
          z-index: 1;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
        }
      }
    }

    .progress-status {
      padding: 0 13rpx;
      height: 40rpx;
      background: #ffc5c5;
      border-radius: 339rpx;
      border: 1rpx solid #ffffff;
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .behavior-list {
    margin: 10rpx 40rpx;
    perspective: 1000px;

    .behavior-item {
      display: flex;
      align-items: center;
      gap: 15rpx;
      padding: 30rpx;
      border-bottom: 1rpx solid #ededed;
      opacity: 0;
      transform: translateY(20px);
      transition:
        opacity 0.5s,
        transform 0.5s;

      &:last-child {
        border-bottom: none;
      }

      &.animate-in {
        opacity: 1;
        transform: translateY(0);
      }

      .behavior-icon-container {
        height: 70rpx;

        .behavior-icon {
          width: 40rpx;
          height: 40rpx;
        }
      }

      .behavior-label-container {
        flex: 1;

        .behavior-label {
          word-break: break-all;
          font-weight: 400;
          font-size: 36rpx;
          color: #333333;
        }

        .behavior-desc {
          font-weight: 400;
          font-size: 28rpx;
          color: #e53434;
        }
      }

      .behavior-status {
        .success,
        .fail {
          font-weight: 400;
          font-size: 36rpx;
          line-height: 45rpx;
          display: flex;
          align-items: center;
          gap: 10rpx;

          .status-icon {
            width: 56rpx;
            height: 34rpx;
          }
        }

        .success {
          color: #35d977;
        }

        .fail {
          color: #e25e4f;
        }
      }
    }
  }

  .user-scores-empty {
    display: flex;
    flex-direction: column;
    align-items: center;

    .empty-image {
      width: 154rpx;
      height: 154rpx;
    }

    .empty-text {
      margin-top: 8rpx;
      margin-bottom: 20rpx;
      font-weight: 400;
      font-size: 34rpx;
      color: #333333;
      line-height: 40rpx;
    }

    .empty-desc {
      font-weight: 400;
      font-size: 32rpx;
      color: #b9b9b9;
      line-height: 40rpx;
    }
  }
}

.apply-steps-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #520404;
  line-height: 40rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin: 28rpx 0;

  &::before {
    content: '';
    width: 125.21rpx;
    height: 21.21rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/fb1f7d793f8f460bbd43ca51c748f318.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &::after {
    content: '';
    width: 125.21rpx;
    height: 21.21rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05d677d07a98411ba9aed3d5703b9483.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.apply-steps {
  margin: 0 30rpx;
  padding: 25rpx 88rpx 65rpx;
  background: #ffffff;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 45rpx;

  .apply-step {
    position: relative;

    .apply-step-icon {
      width: 65rpx;
      height: 54rpx;
    }

    .apply-step-label {
      position: absolute;
      font-weight: 500;
      font-size: 28rpx;
      color: #333333;
      line-height: 40rpx;
      width: max-content;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  .apply-step-arrow {
    width: 62.77rpx;
    height: 40.51rpx;
  }
}

.reason-title {
  font-weight: 500;
  font-size: 32rpx;
  color: #520404;
  line-height: 40rpx;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin: 28rpx 0;

  &::before {
    content: '';
    width: 125.21rpx;
    height: 21.21rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/fb1f7d793f8f460bbd43ca51c748f318.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  &::after {
    content: '';
    width: 125.21rpx;
    height: 21.21rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05d677d07a98411ba9aed3d5703b9483.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
}

.reason-list {
  margin: 0 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 60rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 45rpx;
  justify-content: space-between;

  .reason-item {
    min-width: 240rpx;
    display: flex;
    gap: 18rpx;

    .reason-icon {
      width: 60rpx;
      height: 60rpx;
    }

    .reason-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 7rpx;

      .reason-title {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 46rpx;
      }

      .reason-desc {
        font-weight: 400;
        font-size: 20rpx;
        color: #666666;
        line-height: 29rpx;
      }
    }
  }
}

.fade-enter-active,
.fade-leave-active {
  transition:
    opacity 0.8s,
    transform 0.8s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
  transform: translateY(100%);
}

.fade-enter-to,
.fade-leave {
  opacity: 1;
  transform: translateY(0);
}

@keyframes shimmer {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(200%);
  }
}

@keyframes popIn {
  0% {
    opacity: 0;
    transform: scale(1.2);
  }

  70% {
    opacity: 1;
    transform: scale(0.95);
  }

  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.contact-manager-popup {
  position: relative;
  padding: 109rpx 30rpx 55rpx;
  background: linear-gradient(180deg, #f7d9af 0%, #ffffff 12%);
  border-radius: 40rpx 40rpx 0rpx 0rpx;

  .close-btn {
    position: absolute;
    top: 27rpx;
    right: 32rpx;
    width: 24rpx;
    height: 24rpx;
  }

  .tips-image {
    width: 385.68rpx;
    height: 53.18rpx;
    display: block;
    margin: 0 auto;
    margin-bottom: 38rpx;
  }

  .submit-popup-desc {
    font-weight: 400;
    font-size: 34rpx;
    color: #666666;
    line-height: 49rpx;
    text-align: center;
    margin-bottom: 80rpx;

    .highlight {
      color: #1c80ff;
      font-weight: 500;
      font-size: 36rpx;
      text-decoration: underline;
    }
  }

  .contact-manager-btn {
    padding: 20rpx;
    width: 100%;
    background: #e25e4f;
    border-radius: 62rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.match-popup {
  padding: 60rpx 50rpx 70rpx;
  width: 654rpx;

  .match-status-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 auto;
    width: 165rpx;
    height: 165rpx;

    .icon-circle {
      width: 100%;
      height: 100%;

      &::before {
        display: none;
      }

      &.rotating {
        animation: rotate 1.5s linear infinite;
      }
    }

    .icon-safe {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 82rpx;
      height: 82rpx;
    }

    .match-countdown {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-weight: 400;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 52rpx;
    }
  }

  .match-status-desc {
    text-align: center;
    margin: 20rpx 0 47rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #171a1d;
    line-height: 40rpx;
  }

  .match-list {
    .match-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 26rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .match-item-label {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 41rpx;
      }

      .match-item-icon {
        width: 36rpx;
        height: 36rpx;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(-360deg);
  }
}

.agreement {
  padding: 30rpx 50rpx 0;
  display: flex;
  gap: 16rpx;

  .agree-icon {
    width: 42rpx;
    height: 42rpx;
    flex-shrink: 0;
  }

  .agreement-text {
    font-weight: 400;
    font-size: 26rpx;
    color: #414141;
    line-height: 40rpx;

    .name {
      color: #e25e4f;
    }
  }
}

.agreement-popup {
  width: 654rpx;
  background: #f6f6f6;
  border-radius: 24rpx 24rpx 24rpx 24rpx;
  display: flex;
  flex-direction: column;

  .popup-body {
    padding: 200rpx 0 140rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 30rpx;

    .icon-loading {
      width: 88rpx;
      height: 88rpx;
      animation: rotate 1.5s linear infinite;
    }

    .loading-text {
      font-weight: 500;
      font-size: 32rpx;
      color: #666666;
      line-height: 40rpx;
    }
  }

  .popup-footer {
    width: 100%;
    padding: 16rpx;
    background: #ffffff;
    border-radius: 0rpx 0rpx 24rpx 24rpx;
    font-size: 40rpx;
    color: #666666;
    line-height: 56rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
