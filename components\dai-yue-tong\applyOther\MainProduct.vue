<template>
  <view class="main-product">
    <!-- 定位在左上角的图标 -->
    <image
      class="main-product-tag"
      src="https://cdn.oss-unos.hmctec.cn/common/path/bf0fc1a68e0b4f60b91eecbbbf425ce0.png"
      mode="scaleToFill"
    />

    <view class="main-product-content">
      <view class="product-name-wrapper">
        <view class="product-name">{{ product.name }}</view>
        <view class="product-feature">
          <!-- icon -->
          <image
            class="feature-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/29d6ed7af9b949c9b50248b7eef28a8d.png"
            mode="scaleToFill"
          />
          <text class="feature-text">安全合规</text>
        </view>
        <view class="product-feature">
          <!-- icon -->
          <image
            class="feature-icon"
            src="https://cdn.oss-unos.hmctec.cn/common/path/29d6ed7af9b949c9b50248b7eef28a8d.png"
            mode="scaleToFill"
          />
          <text class="feature-text">快速到账</text>
        </view>
      </view>
      <view class="loan-limit-label">最高可借额度（元）</view>
      <view class="loan-limit-wrapper">
        <view class="loan-amount">{{ product.loanableFundsBig | toThousandFilter }}</view>
        <view class="apply-button" @click="onApply">一键申请</view>
      </view>
      <view class="product-specs">
        <view class="spec-item">
          <view class="spec-label">年化利率</view>
          <view class="spec-value">{{ product.interestRateLittle | toPriceAbbreviations }}%</view>
        </view>
        <view class="spec-item">
          <view class="spec-label">借款时长</view>
          <view class="spec-value">3-60月</view>
        </view>
        <view class="spec-item">
          <view class="spec-label">放款率</view>
          <view class="spec-value">99%</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'MainProduct',
  props: {
    product: {
      type: Object,
      required: true,
      default: () => ({
        logo: '',
        name: '',
        loanableFundsBig: '',
        interestRateLittle: '',
        platformId: '',
        id: ''
      })
    }
  },
  methods: {
    onApply() {
      this.$emit('apply')
    }
  }
}
</script>

<style scoped lang="scss">
.main-product {
  position: relative;
  margin: 22rpx auto 0;
  width: 686rpx;
  height: 390rpx;
  background: linear-gradient(117deg, #faf6e6 0%, #faeed4 100%);

  .main-product-tag {
    position: absolute;
    left: 30rpx;
    top: -4rpx;
    width: 64rpx;
    height: 84rpx;
  }

  .main-product-content {
    .product-name-wrapper {
      display: flex;
      align-items: center;
      padding: 28rpx 0 20rpx 108rpx;

      .product-name {
        margin-right: 16rpx;
        font-weight: 500;
        font-size: 32rpx;
        color: #5a2b0b;
        line-height: 46rpx;
      }

      .product-feature {
        margin-right: 8rpx;
        width: 116rpx;
        height: 32rpx;
        background: #ffeac5;
        border-radius: 4rpx 4rpx 4rpx 4rpx;
        display: flex;
        align-items: center;
        gap: 4rpx;

        .feature-icon {
          width: 20rpx;
          height: 20rpx;
        }

        .feature-text {
          font-weight: 400;
          font-size: 24rpx;
          color: #a56a00;
          line-height: 28rpx;
        }
      }
    }

    .loan-limit-label {
      padding: 0 32rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #5a2b0b;
      line-height: 34rpx;
    }

    .loan-limit-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 32rpx 14rpx;

      .loan-amount {
        font-family: DIN, DIN;
        font-weight: 500;
        font-size: 96rpx;
        color: #5a2b0b;
        line-height: 116rpx;
      }

      .apply-button {
        width: 230rpx;
        height: 76rpx;
        background: #ff702a;
        border-radius: 582rpx 582rpx 582rpx 582rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 32rpx;
        color: #ffffff;
        line-height: 64rpx;
      }
    }

    .product-specs {
      width: 686rpx;
      height: 134rpx;
      background: linear-gradient(117deg, #fbf4e4 0%, #faeacb 100%);
      box-shadow: inset 0rpx 4rpx 12rpx 0rpx #f9ecd1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 72rpx;

      .spec-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .spec-label {
          font-weight: 400;
          font-size: 28rpx;
          color: #999999;
          line-height: 34rpx;
        }

        .spec-value {
          font-family: DIN, DIN;
          font-weight: 500;
          color: #1a1a1a;
          font-size: 36rpx;
          line-height: 44rpx;
        }
      }
    }
  }
}
</style>
