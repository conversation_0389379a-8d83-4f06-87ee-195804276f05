<script>
import Header from '@/components/header/header-hrh.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: {
    Header,
    Services
  }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <Header />
      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/fdd69eec32ac43d8bd82c73f28f11de3.png"
        class="feature-image"
      ></image>

      <Services />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  height: 100dvh;
  background: #f6f6f8;
  display: flex;
  flex-direction: column;

  .page-content {
    padding: 30rpx;
    flex: 1;
    overflow: hidden auto;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/80595aa32bef4eecbe9f7645660ea84e.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }
}

.feature-image {
  display: block;
  margin: 0 auto;
  width: 690rpx;
  height: 634rpx;
}
</style>
