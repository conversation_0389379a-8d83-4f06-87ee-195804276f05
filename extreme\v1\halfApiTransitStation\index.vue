<script>
import { fetchChannelAndTemplate, fetchOnlineProduct, reportUV } from '@/apis/common'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'

export default {
  name: 'halfApiTransitStation',

  data() {
    return {
      form: {
        consumerId: '',
        channelId: '',
        smsId: ''
      },
      fetchHalfApiProductLoading: true,
      fetchHalfApiProductProgress: 0,
      progressTimer: null
    }
  },

  onLoad({ param, i, s }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    if (s) {
      this.form.smsId = s
    }

    this.fetchChannelAndTemplate({ invite: i })
  },

  onUnload() {
    if (this.progressTimer) {
      clearInterval(this.progressTimer)
    }
  },

  methods: {
    async fetchChannelAndTemplate({ invite }) {
      try {
        this.updateProgress(6000)
        const res = await fetchChannelAndTemplate({
          invite
        })
        this.form.channelId = res.data.channelId
      } catch (e) {
        this.navigateToDownload()
      } finally {
        if (this.form.channelId) {
          reportUV({
            channelId: this.form.channelId
          })
          this.navigateToNextPage()
        } else {
          this.navigateToDownload()
        }
      }
    },

    async navigateToNextPage() {
      try {
        const weChatProduct = await this.fetchWeChatProduct()
        if (weChatProduct.length > 0) {
          this.navigateToWeChatProduct()
          return
        }

        // const overLoanProduct = await this.fetchOverLoanProduct();
        // if (overLoanProduct.length > 0) {
        //   this.navigateToOverLoanProduct()
        //   return
        // }

        this.navigateToDownload()
      } catch (e) {
        this.navigateToDownload()
      } finally {
        this.progressDone()
      }
    },

    async fetchOverLoanProduct() {
      const res = await fetchOnlineProduct({
        matchType: 2,
        channelId: this.form.channelId,
        smsId: this.form.smsId
      })
      return res.data || []
    },

    async fetchWeChatProduct() {
      const res = await fetchOnlineProduct({
        matchType: 1,
        channelId: this.form.channelId,
        smsId: this.form.smsId
      })
      return res.data || []
    },

    progressDone() {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }
      this.fetchHalfApiProductProgress = 100
    },

    updateProgress(duration) {
      if (this.progressTimer) {
        clearInterval(this.progressTimer)
      }

      const startTime = Date.now()
      const startProgress = this.fetchHalfApiProductProgress
      const targetProgress = 100

      this.progressTimer = setInterval(() => {
        const currentTime = Date.now()
        const elapsedTime = currentTime - startTime

        if (elapsedTime >= duration) {
          this.fetchHalfApiProductProgress = targetProgress
          clearInterval(this.progressTimer)
        } else {
          const progress = this.easeInOutQuad(
            elapsedTime,
            startProgress,
            targetProgress - startProgress,
            duration
          )
          this.fetchHalfApiProductProgress = Math.round(progress)
        }
      }, 50) // 每50毫秒更新一次进度
    },

    // easeInOutQuad 缓动函数
    easeInOutQuad(t, b, c, d) {
      t /= d / 2
      if (t < 1) return (c / 2) * t * t + b
      t--
      return (-c / 2) * (t * (t - 2) - 1) + b
    },

    navigateToOverLoanProduct() {
      const param = encodeURIComponent(encryptByDES(JSON.stringify(this.form)))
      uni.navigateTo({ url: `/extreme/v1/applyOther/index?param=${param}` })
    },

    navigateToWeChatProduct() {
      const param = encodeURIComponent(encryptByDES(JSON.stringify(this.form)))
      uni.navigateTo({
        url: `/extreme/v1/qw/index?param=${param}`
      })
    },

    navigateToDownload() {
      uni.navigateTo({
        url: `/extreme/v1/download/index?param=${encodeURIComponent(encryptByDES(JSON.stringify(this.form)))}&result=2`
      })
    }
  }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="fetchHalfApiProductLoading" v-if="fetchHalfApiProductLoading">
      <view class="fetchHalfApiProductLoading-content">
        <view class="icon-loading">
          <image
            class="out-img"
            src="https://cdn.oss-unos.hmctec.cn/common/path/c46718c0c9e14ecc8f384b38bc548cb9.png"
          />
          <image
            class="inner-img"
            src="https://cdn.oss-unos.hmctec.cn/common/path/2f939835ffa34f1195ad696cb36e41b2.png"
          />
        </view>

        <view class="tips-1">尊敬的用户，您已完成资料填写</view>
        <view class="tips-2">正在为您评估额度和匹配服务机构，请耐心等待</view>
      </view>
    </view>

    <view class="footer">
      <view class="text">正在加载 ({{ fetchHalfApiProductProgress }}%)</view>
      <view class="process">
        <view class="inner" :style="{ width: `${fetchHalfApiProductProgress}%` }"></view>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #e5ffff 0%, #ffffff 21%);
}

.fetchHalfApiProductLoading {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .fetchHalfApiProductLoading-content {
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon-loading {
      position: relative;
      width: 420rpx;
      height: 420rpx;
      display: flex;
      justify-content: center;
      align-items: center;

      .out-img {
        position: absolute;
        width: 100%;
        height: 100%;
        animation: rotate 1s linear infinite;
      }

      .inner-img {
        width: 200rpx;
        height: 200rpx;
      }
    }
  }
}

.tips-1 {
  margin-top: 20rpx;
  margin-bottom: 16rpx;
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  line-height: 46rpx;
  text-align: center;
}

.tips-2 {
  font-weight: 400;
  font-size: 24rpx;
  color: #333333;
  line-height: 35rpx;
  text-align: center;
}

.footer {
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom));
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .process {
    width: 300rpx;
    height: 18rpx;
    border-radius: 300rpx;
    //border: 1rpx solid #1ED1FF;
    display: flex;
    //padding: 4rpx;
    overflow: hidden;
    background-color: #f1f1f1;

    .inner {
      background-color: #1ed1ff;
      border-radius: 300rpx;
      transition: width 0.5s;
    }
  }

  .text {
    margin-bottom: 10rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #1ed1ff;
    line-height: 35rpx;
  }
}

// 旋转动画
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
