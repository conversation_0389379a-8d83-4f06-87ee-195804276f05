<template>
  <view class="countdown">
    <view class="countdown-label">限时领取</view>
    <view class="countdown-number">{{ formatTime(countdownTime).minutes }}</view>
    <view class="countdown-separator">:</view>
    <view class="countdown-number">{{ formatTime(countdownTime).seconds }}</view>
    <view class="countdown-label">后结束</view>
  </view>
</template>

<script>
export default {
  name: 'CountdownTimer',
  
  props: {
    // 初始倒计时时间（秒）
    initialTime: {
      type: Number,
      default: 300 // 5分钟
    }
  },

  data() {
    return {
      countdownTime: this.initialTime,
      countdownTimer: null,
      isCountdownActive: true
    }
  },

  mounted() {
    this.startCountdown()
  },

  beforeDestroy() {
    this.stopCountdown()
  },

  methods: {
    startCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
      }

      this.countdownTimer = setInterval(() => {
        if (this.countdownTime > 0) {
          this.countdownTime--
        } else {
          this.stopCountdown()
          this.$emit('countdown-end')
        }
      }, 1000)
    },

    stopCountdown() {
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }
      this.isCountdownActive = false
    },

    formatTime(seconds) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return {
        minutes: minutes.toString().padStart(2, '0'),
        seconds: remainingSeconds.toString().padStart(2, '0')
      }
    }
  }
}
</script>

<style scoped lang="scss">
.countdown {
  display: flex;
  justify-content: center;
  align-items: center;

  .countdown-label {
    font-weight: 400;
    font-size: 24rpx;
    color: #6a7a96;
    line-height: 35rpx;
    margin: 0 14rpx;
  }

  .countdown-number {
    width: 38rpx;
    height: 38rpx;
    background: #020202;
    border-radius: 8rpx;
    font-weight: normal;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 38rpx;
    text-align: center;
  }

  .countdown-separator {
    font-weight: 400;
    font-size: 24rpx;
    color: #020202;
    line-height: 35rpx;
    margin: 0 5rpx;
  }
}
</style>
