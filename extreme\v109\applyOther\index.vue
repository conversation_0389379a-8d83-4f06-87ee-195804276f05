<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <AlertBar theme="orange" />

      <Header />

      <view class="header">
        <view class="header-text">恭喜您咨询成功！</view>
        <view class="sub-header-text">根据您的资质，已为您匹配以下{{ productCount }}款产品</view>
      </view>

      <view class="main-product">
        <view class="main-product-info">
          <view class="product">
            <image class="product-logo" :src="mainProduct.logo"></image>
            <text class="product-name">{{ mainProduct.name }}</text>
          </view>
          <view class="product-users">
            <text class="number">1000</text>
            <text>人已放款</text>
          </view>
        </view>
        <view class="product-amount">
          <view class="amount-label">最高额度(元)</view>
          <view class="amount-value">{{ mainProduct.loanableFundsBig | toThousandFilter }}</view>
        </view>
        <view class="product-rate">
          最低年化利率
          {{ mainProduct.interestRateLittle | toPriceAbbreviations }}%
        </view>
        <view class="apply-button" @click="clickApply(mainProduct)">立即申请</view>
      </view>

      <view class="recommended-products">
        <view class="title">
          <text class="section-title">精选推荐</text>
          <text class="section-subtitle">申请产品越多，获取额度概率越高</text>
        </view>

        <view class="product-item" v-for="(item, index) in productList" :key="index">
          <view class="product">
            <image class="product-icon" :src="item.logo"></image>
            <text class="product-name">{{ item.name }}</text>
          </view>
          <view class="product-details">
            <view class="product-features">
              <view class="product-features-item">
                <text class="product-features-value highlight">{{
                  item.loanableFundsBig | toThousandFilter
                }}</text>
                <text class="product-features-label">最高额度(元)</text>
              </view>
              <view class="product-features-item">
                <text class="product-features-value"
                  >{{ item.interestRateLittle | toPriceAbbreviations }}%</text
                >
                <text class="product-features-label">最低年化利率</text>
              </view>
            </view>
            <view class="apply-button-small" @click="clickApply(item)">立即申请</view>
          </view>
        </view>
      </view>

      <Services />

      <Declaration />

      <FullScreenMatchLoading ref="loading" />
    </view>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import AlertBar from '@/components/alert/AlertBar.vue'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct, getOnlineLowProduct } from '@/apis/common-2'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getDcAutoFlag } from '@/apis/common'
import Services from '@/components/services/index.vue'
import Header from '@/components/header/header-custom.vue'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'

export default {
  name: 'applyOther',
  components: {
    AlertBar,
    FullScreenMatchLoading,
    Services,
    Header,
    Declaration
  },
  data() {
    return {
      form: {},
      productList: [],
      mainProduct: {},
      productCount: 0
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getFlowData()

    if (this.mainProduct.platformId && this.mainProduct.id) {
      this.getDcAutoFlag()
    }
  },

  methods: {
    async getDcAutoFlag() {
      const res = await getDcAutoFlag({
        platformId: this.mainProduct.platformId,
        productId: this.mainProduct.id
      })
      // data = 1 不自动 ，data = 2 自动
      if (res.data == 2) {
        this.applyProduct(this.mainProduct)
      }
    },

    async getFlowData() {
      const flowData = getFlowData('overloan')
      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
        this.productCount = flowData.productList.length
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
        window.location.href = res.data

        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v109/apply/index',
        wechat: '/extreme/v109/qw/index',
        overloan: '/extreme/v109/applyOther/index',
        end: '/extreme/v109/download/index'
      }

      const customFetch = {
        wechat: getOnlineLowProduct,
        overloan: getOnlineLowProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )
      if (nextFlow.flow === 'halfapi') {
        this.$refs.loading.close()
        window.location.href = nextFlow.url
        return
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/theme/v109/export/applyOther.scss';
</style>
