<template>
  <view class="page-container">
    <!-- 使用Banner组件 -->
    <dyt-qw-banner />

    <!-- 使用InfoSection组件 -->
    <dyt-qw-info-section />

    <!-- 使用Footer组件 -->
    <dyt-qw-footer
      :agreementList="agreementList"
      :applyUrl="applyUrl"
      :confirmCountdownNumber="confirmCountdownNumber"
      @cancel="handleCancel"
      @agreement-click="handleAgreementClick"
    />

    <!-- 协议弹窗 -->
    <agreement-popup
      ref="agreementPopup"
      :agreementList="agreementList"
      :selectedIndex="selectedAgreementIndex"
    />

    <!-- Loading弹窗 -->
    <loading-popup ref="loading" @progress-complete="handleProgressComplete" />

    <Declaration />
  </view>
</template>

<script>
import DytQwBanner from '@/components/dai-yue-tong/qw/Banner.vue'
import DytQwInfoSection from '@/components/dai-yue-tong/qw/InfoSection.vue'
import DytQwFooter from '@/components/dai-yue-tong/qw/Footer.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import {
  applyOnlineProduct,
  fetchOnlineProduct,
  fetchWechatAutoJump
} from '@/apis/common-2'
import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'
import AgreementPopup from '@/components/dai-yue-tong/index/AgreementPopup.vue'
import LoadingPopup from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import { getApplyOnlineProductLink } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'
import { getAgreements } from '@/utils/agreement'

export default {
  name: 'Qw',
  components: {
    DytQwBanner,
    DytQwInfoSection,
    DytQwFooter,
    AgreementPopup,
    LoadingPopup,
    Declaration
  },

  data() {
    return {
      form: {},
      agreementList: [],
      selectedAgreementIndex: -1,
      confirmCountdownNumber: 0,
      confirmCountdownTimer: null,
      productList: [],
      wechatAutoJump: '',
      applyUrl: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (isPageNested()) {
        savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    }
    this.fetchProtocol()
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      this.applyUrl = getApplyOnlineProductLink({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId,
        redirectUrl: encodeURIComponent(window.location.href)
      })
    } else {
      this.navigateToNextFlow()
    }
  },

  onUnload() {
    this.timerStop()
  },

  methods: {
    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })
      this.wechatAutoJump = res.data || 1
    },

    timerStart() {
      this.confirmCountdownNumber = 3
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
      }
      this.confirmCountdownTimer = setInterval(() => {
        this.confirmCountdownNumber--
        if (this.confirmCountdownNumber === 0) {
          this.clickConfirm()
        }
      }, 1000)
    },

    timerStop() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
        this.confirmCountdownTimer = null
        this.confirmCountdownNumber = 0
      }
    },

    async getFlowData() {
      const flowData = getFlowData('wechat')
      if (flowData) {
        this.productList = flowData.productList
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        offline: '/extreme/v130/apply/index',
        wechat: '/extreme/v130/qw/index',
        overloan: '/extreme/v130/applyOther/index',
        end: '/extreme/v130/download/index',
        halfapi: '/extreme/v130/webviewDown/index',
        wechat_official_account: '/extreme/v130/wechatOfficialAccount/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      this.$refs.loading.close()

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      clearFlowData('wechat')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },

    async fetchProtocol() {
      this.agreementList = await getAgreements('dyt-hls-ag2')
    },

    handleContact() {
      this.applyProduct()
    },

    handleCancel() {
      throttle(this.cancelHandler)
    },

    handleAgreementClick(index) {
      this.selectedAgreementIndex = index
      this.$refs.agreementPopup.open(index)
    },

    clickConfirm() {
      throttle(this.confirmHandler)
    },

    confirmHandler() {
      this.timerStop()
      this.applyProduct()
    },

    cancelHandler() {
      this.timerStop()
      this.navigateToNextFlow()
    },

    handleProgressComplete() {
      // 处理加载完成事件，如果需要
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  padding-top: 52rpx;
  padding-bottom: 666rpx;
  min-height: 100vh;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/c4b684c6fff9467caf389319901cebdc.png);
  background-size: 100% 1250rpx;
  background-repeat: no-repeat;
}
</style>
