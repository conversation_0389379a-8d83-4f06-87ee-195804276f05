<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content"></view>
    <view class="page-footer">
      <!-- 协议相关代码已注释 -->
      <!-- <view class="agreement" @click="clickAgreement">
        <view class="agreement-text">
          本人已知晓
          <text class="name" v-for="item in agreementList" :key="item.protocolId">
            《{{ item.name }}》
          </text>
          的所有内容，同意并授权平台推荐/匹配多个产品
        </view>
      </view> -->

      <view class="btn confirm-btn" @click="clickConfirm">
        立即领取
        <text v-if="confirmCountdownNumber">（{{ confirmCountdownNumber }}S）</text>
      </view>
      <view class="btn cancel-btn" @click="clickCancel">取消</view>
    </view>

    <!-- 协议弹窗已注释 -->
    <!-- <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <view class="agreement-container">
          <view v-for="agreement in agreementList" :key="agreement.protocolId">
            <view class="agreement-title">{{ agreement.title }}</view>
            <view class="agreement-content">
              <div v-html="agreement.content"></div>
            </view>
          </view>
        </view>
        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickConfirm">同意并继续</view>
        </view>
      </view>
    </uni-popup> -->

  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { fetchOnlineProduct, fetchWechatAutoJump, fetchWechatAutoTime } from '@/apis/common-2'
// import { getAgreements } from '@/utils/agreement' // 协议相关导入已注释
import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'

import { outboundApplyProduct } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'qwIndex',

  components: {},

  data() {
    return {
      form: {},
      // agreementList: [], // 协议列表已注释
      confirmCountdownNumber: 0,
      confirmCountdownTimer: null,
      productList: [],
      // 企微自动跳转: 1 为不自动跳转 2为自动跳转
      wechatAutoJump: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))

      if (isPageNested()) {
        savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    }
    // this.fetchProtocol() // 协议获取已注释
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    if (this.productList.length > 0) {
      await this.fetchWechatAutoJump()

      if (this.wechatAutoJump == 2) {
        // 获取倒计时时间
        await this.fetchWechatAutoTime()
        // 启动倒计时，倒计时结束后自动申请产品
        this.timerStart()
      }
    } else {
      this.navigateToNextFlow()
    }
  },

  onUnload() {
    this.timerStop()
  },

  methods: {
    async fetchWechatAutoJump() {
      const res = await fetchWechatAutoJump({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 1 不自动跳转 2自动跳转
      this.wechatAutoJump = res.data || 1
    },

    async fetchWechatAutoTime() {
      const res = await fetchWechatAutoTime({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      // 返回 data 0 立即跳转 5 5秒后跳转 10 10秒后跳转
      this.confirmCountdownNumber = res.data || 0
    },

    timerStart() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
      }

      // 如果倒计时时间为0，立即跳转
      if (this.confirmCountdownNumber === 0) {
        this.confirmHandler()
        return
      }

      this.confirmCountdownTimer = setInterval(() => {
        this.confirmCountdownNumber--
        if (this.confirmCountdownNumber === 0) {
          this.confirmHandler()
        }
      }, 1000)
    },

    timerStop() {
      if (this.confirmCountdownTimer) {
        clearInterval(this.confirmCountdownTimer)
        this.confirmCountdownTimer = null
        this.confirmCountdownNumber = 0
      }
    },

    async getFlowData() {
      const flowData = getFlowData('wechat')
      if (flowData) {
        this.productList = flowData.productList

        if (!this.form.consumerId) {
          this.form.consumerId = this.productList[0].consumerId
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
        // limit: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    async navigateToNextFlow() {
      uni.showLoading({
        title: '正在跳转...',
        mask: true
      })

      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      if (nextFlow.flow === 'end') {
        if (this.vuex_halfJumpUrl) {
          uni.hideLoading()
          window.location.href = this.vuex_halfJumpUrl
          return
        }
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.hideLoading()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await outboundApplyProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId,
        channelId: this.form.channelId,
        uid: this.vuex_uid,
        applyType: 1
      })

      uni.hideLoading()

      clearFlowData('wechat')
      this.productList = []

      if (res.data) {
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },

    // 协议相关方法已注释
    // async fetchProtocol() {
    //   // 个人信息共享授权书，知情告知书
    //   this.agreementList = await getAgreements(
    //     'hyh_info_stream_personal_information_sharing_authorization_letter,hyh_info_stream_informed_consent_document'
    //   )
    // },

    // clickAgreement() {
    //   this.$refs.agreementPopup.open()
    //   this.$forceUpdate()
    // },

    clickConfirm() {
      throttle(this.confirmHandler)
    },

    confirmHandler() {
      this.timerStop()
      this.applyProduct()
    },

    clickCancel() {
      throttle(this.cancelHandler)
    },

    cancelHandler() {
      this.timerStop()
      this.navigateToNextFlow()
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #ff3154;

.color-primary {
  color: $color-primary;
}

.page-container {
  height: 100vh;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/561fea415e7244219baa169557969e08.png');
  background-repeat: no-repeat;
  background-size: 100%;
  background-color: #159B8B;

  .page-content {
    flex: 1;
    padding-bottom: 600rpx;
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    // 协议样式已注释
    // .agreement {
    //   margin-bottom: 20rpx;
    //   padding: 0 30rpx;
    //   display: flex;
    //   gap: 5rpx;
    //   text-align: center;

    //   .agree-icon {
    //     width: 32rpx;
    //     height: 32rpx;
    //   }

    //   .agreement-text {
    //     font-weight: 400;
    //     font-size: 26rpx;
    //     color: #999999;
    //     line-height: 34rpx;

    //     .name {
    //       color: $color-primary;
    //     }
    //   }
    // }

    .btn {
      display: block;
      padding: 22rpx;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      font-size: 40rpx;
      line-height: 56rpx;
      text-align: center;
    }

    .confirm-btn {
      margin-bottom: 15rpx;
      border: 1rpx solid #2DB382;
      background: #2DB382;
      color: #ffffff;
    }

    .cancel-btn {
      margin-bottom: 20rpx;
      border: 1rpx solid #999999;
      color: #999999;
    }
  }
}



// 协议弹窗样式已注释
// .agreement-popup {
//   padding: 40rpx 50rpx 40rpx;
//   background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0a9a3a75797c401ab0170f69c0b923e7.png');
//   background-repeat: no-repeat;
//   background-size: 100%;

//   .agreement-container {
//     overflow: auto;
//     height: 700rpx;
//   }

//   .agreement-title {
//     margin-bottom: 20rpx;
//     font-weight: normal;
//     font-size: 40rpx;
//     color: #3d3d3d;
//     line-height: 56rpx;
//     text-align: center;
//   }

//   .agreement-content {
//     padding-bottom: 20rpx;
//     //font-size: 26rpx;
//     //color: #3D3D3D;
//     //line-height: 42rpx;
//   }

//   .agreement-agree-container {
//     position: relative;
//     padding: 20rpx 0 0;

//     &::before {
//       content: '';
//       position: absolute;
//       left: 0;
//       right: 0;
//       top: -40rpx;
//       height: 40rpx;
//       pointer-events: none;
//       background-image: linear-gradient(to bottom, transparent, #fff);
//     }

//     .agreement-agree-btn {
//       padding: 24rpx;
//       background: $color-primary;
//       border-radius: 30rpx 30rpx 30rpx 30rpx;
//       font-size: 36rpx;
//       color: #ffffff;
//       line-height: 50rpx;
//       text-align: center;
//     }
//   }
// }
</style>
