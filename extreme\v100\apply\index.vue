<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyFormProduct } from '@/apis/common-2'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import throttle from '@/utils/throttle'

export default {
  name: 'ApplyIndex',

  data() {
    return {
      form: {},
      productList: []
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
    this.getFlowData()
  },

  methods: {
    clickApply() {
      throttle(this.applyHandler)
    },

    applyHandler() {
      const selectedProducts = this.productList.filter((item) => item.uiChecked)
      if (selectedProducts.length === 0) {
        uni.showToast({
          title: '请选择产品',
          icon: 'none'
        })
        return
      }

      this.applyProduct()
    },

    getApplyParams() {
      const params = {
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      }

      params.applyProduct = this.productList.filter((item) => item.uiChecked)
      return params
    },

    async applyProduct() {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const params = this.getApplyParams()
      await applyFormProduct(params)

      uni.hideLoading()

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      const routeMap = {
        offline: '/extreme/v100/apply/index',
        wechat: '/extreme/v100/qw/index',
        overloan: '/extreme/v100/applyOther/index',
        end: '/extreme/v100/download/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        window.location.href = nextFlow.url
        return
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async getFlowData() {
      const flowData = getFlowData('offline')
      if (flowData) {
        flowData.productList.forEach((product) => {
          product.uiChecked = true
          // 随机生成匹配率（95～99）
          product.matchingDegree = Math.floor(Math.random() * 5 + 95)
        })
        this.productList = flowData.productList
        this.form.appUserId = flowData.appUserId
        this.form.channelId = flowData.channelId
      }
    }
  }
}
</script>

<template>
  <view>
    <view>线下产品</view>
    <view class="apply-btn" @click="clickApply"> 确认申请 </view>
  </view>
</template>

<style scoped lang="scss"></style>
