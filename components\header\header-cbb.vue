<script>
export default {
  name: 'header-cbb'
}
</script>

<template>
  <view class="header-container">
    <image
      class="logo"
      src="https://cdn.oss-unos.hmctec.cn/common/path/974f4279fda24d0f98c7325105baab33.png"
    />
    <view class="feature-container">
      <view class="feature-item">低门槛</view>
      <view class="gap-line"></view>
      <view class="feature-item">利息低</view>
      <view class="gap-line"></view>
      <view class="feature-item">放款快</view>
    </view>
  </view>
</template>

<style scoped lang="scss">
.header-container {
  width: fit-content;
  position: relative;
  padding: 30rpx;
  display: flex;
  gap: 15rpx;
  align-items: center;

  .logo {
    width: 96rpx;
    height: 39rpx;
  }

  .feature-container {
    display: flex;
    gap: 15rpx;
    align-items: center;

    .feature-item {
      font-weight: 400;
      font-size: 24rpx;
      color: #4a5159;
      line-height: 35rpx;
    }

    .gap-line {
      width: 1rpx;
      height: 19rpx;
      background: #4a5159;
    }
  }
}
</style>
