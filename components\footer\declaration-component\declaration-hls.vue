<script>
export default {
  name: 'declaration-hls',

  methods: {
    bindOpenUrl(url) {
      window.location.href = url
    }
  }
}
</script>

<template>
  <view class="declaration-container">
    <view v-if="vuex_declaration && !vuex_templateSig" v-html="vuex_declaration"></view>
    <template v-else>
      <view>
        <view
          @click="
            bindOpenUrl(
              'http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=51010702002566'
            )
          "
        >
          <image
            class="police-icon"
            src="https://cdn.oss-unos.hmctec.cn/app/statis/h5/images/icon/police.png"
          ></image>
        </view>

        <view
          >温馨提示：完成注册后，平台将不定期通过短信、电话等方式向您提供产品信息及贷款咨询服务</view
        >
        <view
          >郑重声明：所有贷款在未成功放款前，绝不收取任何费用。为了保障您的资金安全，请不要相信任何要求您支付费用的消息、邮件、电话等不实信息。</view
        >
        <view>蜀ICP备2024047093号-2</view>
        <view>四川禾濂晟科技有限公司</view>
        <view>客服电话 4008002289</view>
        <view>服务时间 09:00-18:00</view>
      </view>
    </template>
  </view>
</template>

<style scoped lang="scss">
.declaration-container {
  padding: 30rpx 60rpx;
  font-size: 20rpx;
  color: #ccc;
  line-height: 36rpx;
  text-align: center;

  .police-icon {
    width: 30rpx;
    height: 30rpx;
  }
}
</style>
