<template>
  <uni-popup
    :is-mask-click="false"
    background-color="#fff"
    ref="codePopup"
    type="center"
    border-radius="16rpx"
  >
    <view class="code-popup">
      <view class="code-popup-title">
        恭喜，已为您
        <text class="highlight">锁定借款名额</text>
      </view>
      <view class="code-input-container">
        <input
          maxlength="6"
          type="text"
          v-model="code"
          placeholder-style="color: #A2A3A5"
          placeholder="输入手机验证码"
        />
        <view class="gap-line"></view>
        <view class="get-code-btn">
          <template v-if="codeTimer">{{ countdown }}s</template>
          <template v-else>
            <text @click="handleGetCode">获取</text>
          </template>
        </view>
      </view>
      <view class="get-amount-btn" @click="handleGetAmount">获取额度</view>

      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/308fb51bf41943c2aec94bc29ef954ae.png"
        class="close-btn"
        @click="close"
      />
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'CodePopup',
  
  props: {
    phone: {
      type: String,
      required: true
    },
    channelId: {
      type: [String, Number],
      default: ''
    }
  },
  
  data() {
    return {
      code: '',
      countdown: 60,
      codeTimer: null
    }
  },
  
  beforeDestroy() {
    this.clearTimer()
  },
  
  methods: {
    open() {
      this.$refs.codePopup.open()
      // 打开验证码弹窗时，通知父组件发送验证码
      if (!this.codeTimer) {
        this.handleGetCode()
      }
    },
    
    close() {
      this.$refs.codePopup.close()
    },
    
    handleGetCode() {
      // 通知父组件发送验证码
      this.$emit('send-code')
    },
    
    // 父组件发送验证码成功后调用此方法开始倒计时
    startCountdown() {
      this.clearTimer()
      this.countdown = 60
      
      this.codeTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          this.clearTimer()
        }
      }, 1000)
    },
    
    clearTimer() {
      if (this.codeTimer) {
        clearInterval(this.codeTimer)
        this.codeTimer = null
      }
    },
    
    handleGetAmount() {
      if (!this.code) {
        uni.showToast({
          title: '请输入验证码',
          icon: 'none'
        })
        return
      }
      
      this.$emit('submit', this.code)
    },
    
    reset() {
      this.code = ''
      this.clearTimer()
      this.countdown = 60
    }
  }
}
</script>

<style lang="scss" scoped>
$color-primary: #1678ff;

.code-popup {
  position: relative;
  padding: 60rpx 50rpx 80rpx;

  .close-btn {
    position: absolute;
    top: 9rpx;
    right: 9rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .code-popup-title {
    margin-bottom: 50rpx;
    font-weight: 500;
    font-size: 38rpx;
    color: #333333;
    line-height: 55rpx;
    text-align: center;

    .highlight {
      color: #d4ad6a;
    }
  }

  .code-input-container {
    margin-bottom: 32rpx;
    height: 98rpx;
    display: flex;
    gap: 10rpx;
    align-items: center;
    font-size: 30rpx;
    line-height: 40rpx;
    padding: 30rpx 25rpx 30rpx 40rpx;
    background: #f5f5f5;
    border-radius: 49rpx 49rpx 49rpx 49rpx;

    input {
      flex: 1;
      font-size: 30rpx;
    }

    .gap-line {
      width: 1rpx;
      height: 40rpx;
      background-color: $color-primary;
    }

    .get-code-btn {
      min-width: 60rpx;
      font-weight: 400;
      font-size: 30rpx;
      color: $color-primary;
      line-height: 40rpx;
    }
  }

  .get-amount-btn {
    background: $color-primary;
    border-radius: 117rpx 117rpx 117rpx 117rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-align: center;
    padding: 20rpx;
  }
}
</style> 