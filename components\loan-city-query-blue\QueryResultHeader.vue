<template>
  <view class="result-header">
    <image
      class="result-icon"
      src="https://cdn.oss-unos.hmctec.cn/common/path/2998fb5559564a1b816ca5ead192a3b9.png"
      mode="scaleToFill"
    />
    <text class="result-text">查询成功</text>
  </view>
</template>

<script>
export default {};
</script>

<style lang="scss" scoped>
.result-header {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 55rpx 55rpx 15rpx;

  .result-icon {
    width: 30rpx;
    height: 30rpx;
  }

  .result-text {
    font-weight: 400;
    font-size: 32rpx;
    color: #666666;
    line-height: 46rpx;
  }
}
</style> 