<template>
  <view
    class="mask"
    :class="{ show: opt }"
    @tap="bindCloseMask"
    @tap.stop.prevent="bindDefault"
    @touchmove.stop.prevent="bindDefault"
  >
    <slot></slot>
  </view>
</template>

<script>
import TabMask from './mask.js'

export default {
  name: 'v-mask',
  data() {
    return {
      opt: false,
      plusMask: null
    }
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    maskCloseAble: {
      type: Boolean,
      default: true
    },
    opacity: {
      type: Number,
      default: 0.6
    },
    tabbarHeight: {
      type: Number,
      default: 0
    },
    navHeight: {
      type: Number,
      default: 45
    }
  },
  watch: {
    show: {
      deep: true,
      handler(opt) {
        this.opt = opt
        //#ifdef APP-PLUS
        if (opt) {
          this.plusMask.show(300)
        } else {
          this.plusMask.hide()
        }
        //#endif
      }
    }
  },
  created() {
    this.init()
  },
  methods: {
    /**
     *  @param { Function } init 初始化
     */
    init() {
      this.opt = this.show
      //#ifdef APP-PLUS
      this.plusMask = new TabMask({
        tabbarHeight: this.tabbarHeight,
        opacity: this.opacity,
        navHeight: this.navHeight,
        fn: this.bindCloseMask
      })

      this.plusMask.hide()
      //#endif
    },
    /**
     *  @param { Function } bindCloseMask 关闭弹窗
     */
    bindCloseMask() {
      if (!this.maskCloseAble) return
      this.maskCloseAble && this.$emit('close', false)
    },

    /**
     *  @param { Function } bindDefault 阻止冒泡 && 默认事件
     */
    bindDefault() {
      return false
    }
  },
  beforeDestroy() {
    // #ifdef APP-PLUS
    this.plusMask.hide()
    // #endif
  }
}
</script>

<style lang="scss" scoped></style>
