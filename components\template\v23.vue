<template>
  <view class="page-container">
    <AlertBar theme="orange" />

    <Header />

    <view class="amount">
      <view class="title">您想最高借多少(元)</view>
      <view class="input-container">
        <input
          placeholder="请输入金额"
          v-model="form.demandAmount"
          @blur="demandAmountBlur"
          @focus="demandAmountFocus"
        />
        <view class="all-btn" @click="clickMaxAmount">全部借出</view>
      </view>
      <view class="slider-container">
        <slider
          :step="100 / 3"
          @change="demandAmountSliderChange"
          :value="form.demandAmountSlider"
          backgroundColor="#F7F7F7"
          block-size="13"
        />
        <view class="slider-tick">
          <text>5万</text>
          <text>10万</text>
          <text>15万</text>
          <text>20万</text>
        </view>
      </view>
      <view class="annual-interest-rate">
        <view class="tag">限时优惠</view>
        <text>
          参考年化利率
          <text class="highlight">12%</text>
          ,1000元1天仅需
          <text class="highlight">0.3</text>
          元
        </text>
      </view>
    </view>

    <view class="borrowing-options">
      <view class="option term">
        <view class="label">最长借多久</view>
        <picker
          range-key="label"
          :range="monthRange"
          :value="form.monthIndex"
          @change="monthPickerChange"
        >
          <view class="value">
            <view>{{ monthRange[this.form.monthIndex].value }}个月</view>
            <view class="recommend" v-if="monthRange[this.form.monthIndex].value === 12">推荐</view>
            <uni-icons
              style="margin-left: 15rpx"
              color="#A2A3A5"
              type="right"
              size="16"
            ></uni-icons>
          </view>
        </picker>
      </view>
      <view class="option-line"></view>
      <view class="option repayment-method">
        <view class="label">如何还</view>
        <view class="value">
          <view class="repayment-amount">
            每月约应还
            <text class="amount-number">￥{{ monthlyPay }}</text>
          </view>
          <view class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</view>
        </view>
      </view>
      <view class="option-line"></view>
      <view class="option coupon">
        <view class="label">优惠券</view>
        <view class="value">
          <view class="coupon-container">
            <view class="coupon-text">
              新用户借款享
              <text class="days">30天</text>
              免息
            </view>
            <view class="coupon-tips"> 若提前还清或逾期，本券将失效 </view>
          </view>
          <uni-icons color="#A2A3A5" type="right" size="16"></uni-icons>
        </view>
      </view>
    </view>

    <view class="phone-container">
      <input
        type="tel"
        maxlength="11"
        class="phone-input"
        placeholder="输入手机号获取额度(已加密)"
        @blur="phoneBlur"
        v-model="form.phone"
        placeholder-style="color: #A2A3A5"
      />
      <view class="get-my-quota" @click="clickGetMyQuota">领取我的额度</view>
      <view class="agreement" @click="isAgree = !isAgree">
        <image
          v-if="isAgree"
          class="agree-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/674fd4e2b9c24182bd15929f85634629.png"
        ></image>
        <image
          v-else
          class="agree-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/f83966b496f348bba3ba7145ac281ab2.png"
        ></image>

        <view class="agreement-text">
          我已阅读并同意
          <text
            class="name"
            v-for="item in agreementList"
            :key="item.protocolId"
            @click="clickAgreement(item)"
          >
            《{{ item.name }}》
          </text>
        </view>
      </view>
    </view>

    <Declaration />

    <uni-popup
      background-color="#fff"
      ref="agreementPopup"
      type="bottom"
      border-radius="32rpx 32rpx 0 0"
    >
      <view class="agreement-popup">
        <view class="agreement-container">
          <view v-for="agreement in agreementList" :key="agreement.protocolId">
            <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
            <view class="agreement-content">
              <div v-html="agreement.content"></div>
            </view>
          </view>
        </view>
        <view class="agreement-agree-container">
          <view class="agreement-agree-btn" @click="clickAgreeAndContinue">同意并继续</view>
        </view>
      </view>
    </uni-popup>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="codePopup"
      type="center"
      border-radius="16rpx"
    >
      <view class="code-popup">
        <view class="code-popup-title">
          恭喜，已为您
          <text class="highlight">锁定借款名额</text>
        </view>
        <view class="code-input-container">
          <input
            maxlength="6"
            type="text"
            v-model="form.code"
            placeholder-style="color: #A2A3A5"
            placeholder="输入手机验证码"
          />
          <view class="gap-line"></view>
          <view class="get-code-btn">
            <template v-if="codeTimer">{{ codeCountdown }}s</template>
            <template v-else>
              <text @click="clickGetCode">获取</text>
            </template>
          </view>
        </view>
        <view class="get-amount-btn" @click="clickGetAmount">获取额度</view>

        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/308fb51bf41943c2aec94bc29ef954ae.png"
          class="close-btn"
          @click="$refs.codePopup.close()"
        />
      </view>
    </uni-popup>

    <uni-popup
      :is-mask-click="false"
      background-color="#fff"
      ref="remindPopup"
      type="center"
      border-radius="56rpx 56rpx 56rpx 56rpx"
    >
      <view class="remind-popup">
        <view class="remind-popup-title">确定要放弃以下限时特权吗？</view>
        <view class="remind-popup-desc">额度仅限今日领取</view>
        <view class="remind-popup-feature">
          <view class="remind-popup-feature-item">
            <image
              class="remind-popup-feature-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/d2196ac9fdb94ed28f61479de3fe821c.png"
            ></image>
            <view class="remind-popup-feature-text">
              已匹配到产品的
              <text class="highlight">申请机会</text>
            </view>
          </view>
          <view class="remind-popup-feature-item">
            <image
              class="remind-popup-feature-icon"
              src="https://cdn.oss-unos.hmctec.cn/common/path/d2196ac9fdb94ed28f61479de3fe821c.png"
            ></image>
            <view class="remind-popup-feature-text">
              初审已通过，确认额度
              <text class="highlight">立即领取</text>
            </view>
          </view>
        </view>
        <view class="remind-popup-tips"
          >信息加密储存仅用于资质申请，平台承诺不会泄露您的任何信息</view
        >
        <view class="remind-popup-confirm" @click="$refs.remindPopup.close()">继续申请</view>
        <view class="remind-popup-cancel" @click="$refs.remindPopup.close()">狠心离开</view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
import AlertBar from '@/components/alert/AlertBar.vue'
import { validateMobile } from '@/utils/validators'
import LockNativeBack from '@/utils/lock-native-back'
import { saveLoan } from '@/apis/common-2'
import { reportUV, sendSmsCode } from '@/apis/common-3'
import { encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-jxd.vue'
import { formatAmount, parseAmount } from '@/utils/amount'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-jxd.vue'
import { getAdReferrer } from '@/utils/ad-referrer'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v23',

  components: {
    AlertBar,
    Declaration,
    Header
  },

  data() {
    return {
      form: {
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        channelId: '',
        uvId: '' // 新增 uvId 字段
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      isAgree: false,
      codeTimer: null,
      codeCountdown: 0,
      lockBack: null,
      agreementList: []
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
      this.codeTimer = null
    }

    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  async mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      const query = this.$route.query
      const adReferrer = getAdReferrer(query)
      this.$u.vuex('vuex_adReferrer', adReferrer)
    }
    this.fetchAgreement()
    this.updateAmountUI()
    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()

    const res = await reportUV({
      channelId: this.channelId,
      adVid: this.vuex_adReferrer.value
    })
    if (res.code == 200) {
      this.form.uvId = res.data
    }
  },

  methods: {
    async fetchAgreement() {
      // 注册页协议
      this.agreementList = await getAgreements('jinxinding_registration_page_agreement')
    },

    clickGetMyQuota() {
      throttle(this.getMyQuotaHandler)
    },

    getMyQuotaHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (!this.isAgree) {
        this.$refs.agreementPopup.open()
        this.$forceUpdate()
        return
      }

      this.$refs.codePopup.open()
      // 打开验证码弹窗时，自动获取验证码
      if (!this.codeTimer) {
        this.clickGetCode()
      }
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    clickGetCode() {
      this.getCodeHandler()
    },

    async getCodeHandler() {
      const res = await sendSmsCode({
        phone: this.form.phone,
        channelId: this.form.channelId
      })

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '发送失败',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      this.codeCountdown = 60
      if (this.codeTimer) {
        clearInterval(this.codeTimer)
        this.codeTimer = null
      }
      this.codeTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.codeTimer)
          this.codeTimer = null
        }
      }, 1000)
    },

    demandAmountSliderChange({ detail }) {
      const sliderValue = detail.value

      // 找出最近的预设值
      const closestPreset = this.presetValues.reduce((prev, curr) =>
        Math.abs(curr - sliderValue) < Math.abs(prev - sliderValue) ? curr : prev
      )

      // 更新金额
      const amountIndex = this.presetValues.indexOf(closestPreset)
      const amount = this.presetAmounts[amountIndex]
      this.form.demandAmount = formatAmount(amount)
      this.updateAmountUI()
    },

    clickMaxAmount() {
      this.form.demandAmount = '200,000'
      this.updateAmountUI()
    },

    computedMonthPay() {
      let price = Number(parseAmount(this.form.demandAmount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.form.monthIndex].value

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    updateAmountUI() {
      this.setSliderValue(parseAmount(this.form.demandAmount))
      this.computedMonthPay()
    },

    demandAmountBlur() {
      const amount = parseInt(this.form.demandAmount)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(50000)
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(50000)
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(200000)
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = formatAmount(amount)
      this.updateAmountUI()
    },

    demandAmountFocus() {
      this.form.demandAmount = parseAmount(this.form.demandAmount)
    },

    // 根据传入的金额，设置滑块的值
    setSliderValue(amount) {
      if (amount <= this.presetAmounts[0]) {
        this.form.demandAmountSlider = this.presetValues[0]
      } else if (amount >= this.presetAmounts[this.presetAmounts.length - 1]) {
        this.form.demandAmountSlider = this.presetValues[this.presetValues.length - 1]
      } else {
        for (let i = 1; i < this.presetAmounts.length; i++) {
          if (amount <= this.presetAmounts[i]) {
            const lowerAmount = this.presetAmounts[i - 1]
            const upperAmount = this.presetAmounts[i]
            const lowerValue = this.presetValues[i - 1]
            const upperValue = this.presetValues[i]

            const ratio = (amount - lowerAmount) / (upperAmount - lowerAmount)
            this.form.demandAmountSlider = lowerValue + ratio * (upperValue - lowerValue)
            break
          }
        }
      }
    },

    monthPickerChange({ detail }) {
      this.form.monthIndex = detail.value
      this.updateAmountUI()
    },

    clickAgreeAndContinue() {
      this.$refs.agreementPopup.close()
      this.isAgree = true
      this.clickGetMyQuota()
    },

    clickGetAmount() {
      throttle(() => {
        if (!this.form.code) {
          uni.showToast({
            title: '请输入验证码',
            icon: 'none'
          })
          return
        }

        this.login()
      })
    },

    async getVisitorId() {
      const fp = await fpPromise
      const { visitorId } = await fp.get()
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },

    async getLoginParams() {
      const params = {}

      params.phoneBlack = getBlackPhone()
      params.h5UaUuid = this.form.uvId // 使用 form 中保存的 uvId
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = parseAmount(this.form.demandAmount)
      params.phone = this.form.phone
      params.code = this.form.code
      params.channelId = this.form.channelId

      return params
    },

    async login() {
      const params = await this.getLoginParams()

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      // 登录成功后，记录手机号
      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: parseAmount(this.form.demandAmount),
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      this.$u.vuex('vuex_consumerId', res.data)
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v23/auth/index?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
$color-primary: #1678ff;

.color-primary {
  color: $color-primary;
}

.page-container {
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  background: #f6f6f8;
  padding-bottom: calc(30rpx + env(safe-area-inset-bottom));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 438rpx;
    background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/3d34ff5b56ae4f299c8ef6f00cd59fc2.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    transform: scale(3);
  }
}

.alert {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 10rpx 30rpx;
  background-color: #fff7eb;

  .alert-icon {
    width: 51rpx;
    height: 29rpx;
  }

  .alert-notice {
    padding: 0;
    margin: 0;
  }
}

.platform {
  position: relative;
  padding: 30rpx;
  display: flex;
  align-items: flex-end;
  gap: 20rpx;

  .platform-name {
    color: #16283c;
    font-family: 'Alimama ShuHeiTi', serif;
    font-weight: bold;
    font-size: 40rpx;
  }

  .platform-feature {
    font-weight: 400;
    font-size: 24rpx;
    color: #4a5159;
    line-height: 35rpx;
    display: flex;
    align-items: center;
    gap: 6rpx;

    .platform-feature-line {
      width: 1rpx;
      height: 19rpx;
      background: #4a5159;
    }
  }
}

.amount {
  position: relative;
  margin: 0 30rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/9e4d885c5d2e4c4b9d9ee3ba3688e1d0.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 45rpx 30rpx 30rpx;

  .title {
    font-weight: normal;
    font-size: 28rpx;
    color: #171a1d;
    line-height: 39rpx;
  }

  .input-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    input {
      font-family: DIN;
      font-weight: 700;
      font-size: 80rpx;
      color: #333333;
      line-height: 98rpx;
      flex: 1;
    }

    .all-btn {
      flex-shrink: 0;
      height: fit-content;
      padding: 17rpx 25rpx;
      background: $color-primary;
      box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(0, 0, 0, 0.2);
      border-radius: 185rpx 185rpx 185rpx 185rpx;
      font-weight: 400;
      font-size: 24rpx;
      color: #eef9f3;
      line-height: 32rpx;
    }
  }

  .slider-container {
    ::v-deep slider {
      margin: 0;

      .uni-slider-track {
        background: linear-gradient(90deg, #c4ddff 0%, #1678ff 100%) !important;
      }

      .uni-slider-handle-wrapper {
        height: 13rpx;
      }
    }

    .slider-tick {
      font-weight: 400;
      font-size: 24rpx;
      color: #747677;
      line-height: 35rpx;
      display: flex;
      justify-content: space-between;
    }
  }

  .annual-interest-rate {
    margin-top: 35rpx;
    display: flex;
    align-items: center;
    gap: 15rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #333333;
    line-height: 32rpx;

    .tag {
      padding: 8rpx 18rpx;
      background: linear-gradient(270deg, #ffcd85 0%, #ffab44 100%);
      font-weight: 400;
      font-size: 24rpx;
      color: #ffffff;
      line-height: 25rpx;
      flex-shrink: 0;
      border-radius: 14rpx;
    }

    .highlight {
      color: #ffc93b;
    }
  }
}

.borrowing-options {
  padding: 15rpx 0;
  margin: 40rpx 30rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .option-line {
    margin-left: 60rpx;
    height: 2rpx;
    background-color: #f6f6f6;
  }

  .option {
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;

    .label {
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    &.term .value {
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 41rpx;
      display: flex;
      align-items: center;

      .recommend {
        margin-left: 3rpx;
        padding: 7rpx 10rpx;
        background: #ff3636;
        border-radius: 203rpx 203rpx 203rpx 203rpx;
        font-weight: 400;
        font-size: 20rpx;
        color: #eef9f3;
        line-height: 18rpx;
      }
    }

    &.repayment-method .value {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;

      .amount-number {
        color: #333333;
      }

      .repayment-reminder {
        font-weight: 400;
        font-size: 20rpx;
        color: #999999;
        line-height: 29rpx;
      }
    }

    &.coupon .value {
      gap: 15rpx;
      display: flex;
      align-items: center;

      .coupon-container {
        font-weight: 400;
        font-size: 28rpx;
        color: #333333;
        line-height: 32rpx;
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .days {
          color: #edbf7c;
        }

        .coupon-tips {
          padding: 8rpx 11rpx;
          background: #fff0f5;
          border-radius: 8rpx 8rpx 8rpx 8rpx;
          font-weight: 400;
          font-size: 20rpx;
          color: #ff5e7c;
          line-height: 21rpx;
        }
      }
    }
  }
}

.phone-container {
  margin: 0 30rpx;
  padding: 40rpx 20rpx 36rpx;
  background: #ffffff;
  border-radius: 16rpx 16rpx 16rpx 16rpx;

  .phone-input {
    height: 96rpx;
    padding: 30rpx 40rpx;
    background: #f6f6f8;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
    font-size: 28rpx;
  }

  .get-my-quota {
    margin: 20rpx 0;
    padding: 22rpx;
    text-align: center;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    background: $color-primary;
    border-radius: 30rpx 30rpx 30rpx 30rpx;
  }

  .agreement {
    display: flex;
    gap: 5rpx;

    .agree-icon {
      width: 32rpx;
      height: 32rpx;
      flex-shrink: 0;
    }

    .agreement-text {
      font-weight: 400;
      font-size: 26rpx;
      color: #999999;
      line-height: 32rpx;

      .name {
        color: $color-primary;
      }
    }
  }
}

.declaration {
  padding: 0 60rpx;
}

.agreement-popup {
  padding: 40rpx 50rpx 80rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/05f2447ceb6b45caaf742b8366205959.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: $color-primary;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}

.code-popup {
  position: relative;
  padding: 60rpx 50rpx 80rpx;

  .close-btn {
    position: absolute;
    top: 9rpx;
    right: 9rpx;
    width: 48rpx;
    height: 48rpx;
  }

  .code-popup-title {
    margin-bottom: 50rpx;
    font-weight: 500;
    font-size: 38rpx;
    color: #333333;
    line-height: 55rpx;
    text-align: center;

    .highlight {
      color: #d4ad6a;
    }
  }

  .code-input-container {
    margin-bottom: 32rpx;
    height: 98rpx;
    display: flex;
    gap: 10rpx;
    align-items: center;
    font-size: 30rpx;
    line-height: 40rpx;
    padding: 30rpx 25rpx 30rpx 40rpx;
    background: #f5f5f5;
    border-radius: 49rpx 49rpx 49rpx 49rpx;

    input {
      flex: 1;
      font-size: 30rpx;
    }

    .gap-line {
      width: 1rpx;
      height: 40rpx;
      background-color: $color-primary;
    }

    .get-code-btn {
      min-width: 60rpx;
      font-weight: 400;
      font-size: 30rpx;
      color: $color-primary;
      line-height: 40rpx;
    }
  }

  .get-amount-btn {
    background: $color-primary;
    border-radius: 117rpx 117rpx 117rpx 117rpx;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 56rpx;
    text-align: center;
    padding: 20rpx;
  }
}

.remind-popup {
  width: 650rpx;
  padding: 88rpx 66rpx 72rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/47517249d74c409daf1ccfee58dfb4eb.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .remind-popup-title {
    font-weight: normal;
    font-size: 36rpx;
    color: #3d3d3d;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-desc {
    margin-top: 14rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 42rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .remind-popup-feature {
    margin-top: 50rpx;
    display: flex;
    flex-direction: column;
    gap: 40rpx;

    .remind-popup-feature-item {
      display: flex;
      align-items: center;
      gap: 5rpx;

      .remind-popup-feature-icon {
        width: 36rpx;
        height: 30rpx;
      }

      .remind-popup-feature-text {
        font-weight: 400;
        font-size: 28rpx;
        color: #3d3d3d;
        line-height: 37rpx;
        letter-spacing: 2px;

        .highlight {
          color: $color-primary;
        }
      }
    }
  }

  .remind-popup-tips {
    margin-top: 43rpx;
    background: #fff7eb;
    font-weight: 400;
    font-size: 24rpx;
    color: #864508;
    line-height: 32rpx;
    padding: 16rpx 22rpx;
  }

  .remind-popup-confirm {
    margin-top: 48rpx;
    background: $color-primary;
    border-radius: 38rpx 38rpx 38rpx 38rpx;
    font-weight: normal;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 45rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
    padding: 15rpx;
  }

  .remind-popup-cancel {
    margin-top: 22rpx;
    font-weight: normal;
    font-size: 26rpx;
    color: #919094;
    line-height: 36rpx;
    text-align: center;
  }
}
</style>
