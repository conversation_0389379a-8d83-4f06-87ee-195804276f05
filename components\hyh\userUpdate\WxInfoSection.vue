<template>
  <view class="wx-container">
    <image
      class="wx-img"
      src="https://cdn.oss-unos.hmctec.cn/common/path/6f537f73605a453f97340039c71ad92f.png"
      mode="aspectFit"
    />
    <view class="wx-text">
      <view class="wx-text-title">您可以在微信领取额度啦</view>
      <view class="wx-text-desc">开启后将跳转微信咨询客服</view>
    </view>
    <view class="wx-switch" @click="handleSwitchClick">
      <switch color="#28C445" />
    </view>
  </view>
</template>

<script>
export default {
  name: 'WxInfoSection',
  methods: {
    handleSwitchClick() {
      // 这里仅作为示例，实际的资质更新请求由父组件的 clickUpdateAptitude 触发
      // 如果 WxInfoSection 自身也需要某种形式的确认或触发资质更新，可以在此 emit 事件
      // 根据原代码，wx-switch 的 click 事件直接绑定的是父组件的 clickUpdateAptitude
      // 因此这里保持简单，如果父组件需要从这里接收特定事件，可以添加 $emit
      this.$emit('update-aptitude-request');
    }
  }
}
</script>

<style scoped lang="scss">
.wx-container {
  margin: 0 auto;
  padding: 50rpx 25rpx;
  width: 692rpx;
  background: #ffffff;
  border-radius: 20rpx 20rpx 20rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;

  .wx-img {
    width: 92rpx;
    height: 92rpx;
  }
  .wx-text {
    flex: 1;
    .wx-text-title {
      font-weight: 700;
      font-size: 32rpx;
      color: #333333;
      line-height: 48rpx;
    }
    .wx-text-desc {
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      line-height: 39rpx;
    }
  }
  .wx-switch {
    width: 101rpx;
    height: 62rpx;
    // 注意：原代码中 switch 是 uniapp 内置组件，wx-switch view 只是容器
    // 这里只是复制了样式，实际交互由 <switch /> 组件控制
    // 如果需要自定义开关样式和行为，则需要更复杂的实现
    display: flex; // 让 switch 组件在容器内居中
    align-items: center;
    justify-content: center;
  }
}
</style>
