<template>
  <view class="page-container">
    <view>企微产品</view>
    <view class="btn confirm-btn" @click="clickConfirm"> 立即领取 </view>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { applyOnlineProduct, fetchOnlineProduct } from '@/apis/common-2'
import { addNowDayApplyProductId, getNowDayAlreadyApplyProductId } from '@/apis/test'
import { clearFlowData, getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'

export default {
  name: 'qwIndex',

  data() {
    return {
      form: {},
      productList: []
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      await this.fetchProduct()
    }

    // 在使用产品前过滤掉已申请过的产品
    await this.filterAlreadyAppliedProducts()

    if (this.productList.length > 0) {
      this.clickConfirm()
    } else {
      this.navigateToNextFlow()
    }
  },

  methods: {
    async getFlowData() {
      const flowData = getFlowData('wechat')
      if (flowData) {
        this.productList = flowData.productList
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await fetchOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
        // limit: 1
      })

      if (res.data) {
        this.productList = res.data
      }
    },

    // 过滤已申请过的产品
    async filterAlreadyAppliedProducts() {
      // 获取已申请过的产品ID列表
      const alreadyApplyRes = await getNowDayAlreadyApplyProductId({
        identity: this.form.identity
      })

      if (alreadyApplyRes.data && alreadyApplyRes.data.length > 0) {
        // 过滤掉已申请过的产品
        // 注意：返回格式改为 ['platformId:productId']
        this.productList = this.productList.filter((product) => {
          // 将产品id和platformType组合成key进行比较
          const productKey = `${product.platformId}:${product.id}`

          // 只有当平台类型和产品ID都匹配时才视为重复
          return !alreadyApplyRes.data.includes(productKey)
        })
      }
    },

    async navigateToNextFlow() {
      const routeMap = {
        offline: '/extreme/v100/apply/index',
        wechat: '/extreme/v100/qw/index',
        overloan: '/extreme/v100/applyOther/index',
        end: '/extreme/v100/download/index'
      }
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        window.location.href = nextFlow.url
        return
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async applyProduct() {
      if (this.productList.length === 0) {
        this.navigateToNextFlow()
        return
      }

      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })

      const res = await applyOnlineProduct({
        productId: this.productList[0].id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      // 保存当前产品ID，因为下面会清空productList
      const currentProductId = this.productList[0].id
      // 保存当前产品的platformType
      const currentPlatformType = this.productList[0].platformId

      clearFlowData('wechat')
      this.productList = []

      if (res.data) {
        // 申请产品成功后，记录已申请的产品ID
        await addNowDayApplyProductId({
          productId: currentProductId,
          identity: this.form.identity,
          platformType: currentPlatformType
        })
        window.location.href = res.data
      } else {
        this.navigateToNextFlow()
      }
    },

    clickConfirm() {
      this.applyProduct()
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f6f6f8;
  // display: flex;
  // flex-direction: column;

  .page-content {
    height: 100vh;
    overflow: hidden auto;
    background: linear-gradient(180deg, #30c497 0%, #effbf9 48%);
    padding-bottom: 600rpx;
  }

  .page-footer {
    padding: 40rpx 30rpx calc(40rpx + env(safe-area-inset-bottom));
    background: #ffffff;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;

    .agreement {
      margin-bottom: 20rpx;
      padding: 0 30rpx;
      display: flex;
      gap: 5rpx;
      text-align: center;

      .agree-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .agreement-text {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 34rpx;

        .name {
          color: #ff3154;
        }
      }
    }

    .btn {
      padding: 22rpx;
      border-radius: 62rpx 62rpx 62rpx 62rpx;
      font-size: 40rpx;
      line-height: 56rpx;
      text-align: center;
    }

    .confirm-btn {
      margin-bottom: 15rpx;
      border: 1rpx solid #62d5ab;
      background: #62d5ab;
      color: #ffffff;
    }

    .cancel-btn {
      margin-bottom: 20rpx;
      border: 1rpx solid #999999;
      color: #999999;
    }

    .confirm-tips {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 8rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #666666;
      line-height: 34rpx;

      .icon-wechat {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.page-title {
  padding-top: 40rpx;
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    display: flex;
    align-items: center;
    color: #fff;
    line-height: 100rpx;
    font-size: 72rpx;
    font-weight: 400;
  }

  .icon-wechat-2 {
    margin-right: 20rpx;
    width: 89rpx;
    height: 73rpx;
  }
}

.card {
  margin-bottom: 20rpx;
  display: flex;
  justify-content: center;

  image {
    width: 633rpx;
    height: 298rpx;
  }
}

.agreement-popup {
  padding: 40rpx 50rpx 40rpx;
  background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/0a9a3a75797c401ab0170f69c0b923e7.png');
  background-repeat: no-repeat;
  background-size: 100%;

  .agreement-container {
    overflow: auto;
    height: 700rpx;
  }

  .agreement-title {
    margin-bottom: 20rpx;
    font-weight: normal;
    font-size: 40rpx;
    color: #3d3d3d;
    line-height: 56rpx;
    text-align: center;
  }

  .agreement-content {
    padding-bottom: 20rpx;
    //font-size: 26rpx;
    //color: #3D3D3D;
    //line-height: 42rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 20rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -40rpx;
      height: 40rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: #ff3154;
      border-radius: 30rpx 30rpx 30rpx 30rpx;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }
  }
}
</style>
