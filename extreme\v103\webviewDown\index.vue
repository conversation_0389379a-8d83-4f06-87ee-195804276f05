<template>
  <view :class="vuex_theme">
    <view class="container" v-show="vuex_theme">
      <!--		<view class="cover" v-if="show">当前为第三方网页，请注意核实信息谨防上当受骗</view>-->
      <web-view :src="url" class="wb-view"></web-view>
    </view>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'

export default {
  name: 'webviewDown',

  data() {
    return {
      show: true,
      url: '',
      form: {}
    }
  },

  onLoad({ param, src }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
      this.url = decodeURIComponent(src)
    }

    setTimeout(() => {
      this.show = false
    }, 3000)
  },

  methods: {
    navigateToEnd() {
      const path = '/extreme/v103/download/index'
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss">
@import '@/theme/v103/export/webviewDown.scss';
</style>
