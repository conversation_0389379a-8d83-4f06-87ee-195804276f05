<template>
  <uni-popup
    background-color="#fff"
    ref="agreementPopup"
    type="bottom"
    border-radius="32rpx 32rpx 0 0"
  >
    <view class="agreement-popup">
      <view class="agreement-header">
        <view class="agreement-title" v-if="selectedAgreement">{{ selectedAgreement.name || selectedAgreement.protocolName }}</view>
      </view>
      <view class="agreement-container">
        <web-view v-if="selectedAgreement && selectedAgreement.protocolUrl" 
          :src="selectedAgreement.protocolUrl" 
          :fullscreen="false"
          class="agreement-web-view">
        </web-view>
        <view v-else-if="selectedAgreement" class="agreement-content">
          <div v-html="selectedAgreement.content"></div>
        </view>
        <view
          v-else-if="agreementList.length > 0"
          v-for="agreement in agreementList"
          :key="agreement.protocolId || agreement.id"
        >
          <view class="agreement-content">
            <div v-html="agreement.content"></div>
          </view>
        </view>
        <view v-else class="agreement-empty"> 暂无协议内容 </view>
      </view>
      <view class="agreement-agree-container">
        <view class="agreement-agree-btn" @click="onAgree">同意并继续</view>
        <view class="agreement-agree-tips">阅读并同意协议才能继续借款</view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'AgreementPopup',
  props: {
    agreementList: {
      type: Array,
      default: () => []
    },
    selectedIndex: {
      type: Number,
      default: -1
    }
  },
  data() {
    return {
      currentIndex: -1
    }
  },
  computed: {
    selectedAgreement() {
      if (this.currentIndex >= 0 && this.currentIndex < this.agreementList.length) {
        return this.agreementList[this.currentIndex]
      }
      return null
    }
  },
  methods: {
    open(index = -1) {
      this.currentIndex = index >= 0 ? index : this.selectedIndex
      this.$refs.agreementPopup.open()
    },
    close() {
      this.$refs.agreementPopup.close()
    },
    onAgree() {
      this.$emit('agree')
      this.close()
    }
  }
}
</script>

<style lang="scss" scoped>
.agreement-popup {
  padding: 40rpx 30rpx 40rpx;
  background: linear-gradient(180deg, #d8faf8 0%, #ffffff 8%, #ffffff 100%);
  border-radius: 20rpx 20rpx 0 0;

  .agreement-header {
    position: relative;
    margin-bottom: 30rpx;

    .agreement-title {
      font-weight: 500;
      font-size: 36rpx;
      color: #1a1a1a;
      line-height: 50rpx;
      text-align: center;
      padding-bottom: 10rpx;
    }
  }

  .agreement-container {
    overflow: auto;
    height: 680rpx;
    padding: 24rpx;
    background: #ffffff;
    border-radius: 16rpx;
    border: 1rpx solid rgba(63, 193, 188, 0.3);
    position: relative;
  }

  .agreement-web-view {
    width: 100%;
    height: 100%;

    ::v-deep iframe {
      width: 100% !important;
      height: 100% !important;
      border: none;
    }
  }

  .agreement-content {
    padding-bottom: 20rpx;
    font-size: 28rpx;
    color: #4d4d4d;
    line-height: 40rpx;
  }

  .agreement-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200rpx;
    color: #999;
    font-size: 28rpx;
  }

  .agreement-agree-container {
    position: relative;
    padding: 30rpx 0 0;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      top: -30rpx;
      height: 30rpx;
      pointer-events: none;
      background-image: linear-gradient(to bottom, transparent, #fff);
    }

    .agreement-agree-btn {
      padding: 24rpx;
      background: linear-gradient(100deg, #ffb173 22%, #ff702a 100%);
      border-radius: 462rpx 462rpx 462rpx 462rpx;
      font-weight: 500;
      font-size: 36rpx;
      color: #ffffff;
      line-height: 50rpx;
      text-align: center;
    }

    .agreement-agree-tips {
      margin-top: 16rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 34rpx;
      text-align: center;
    }
  }
}
</style>
