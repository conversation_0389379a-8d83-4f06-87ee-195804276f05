<template>
  <view class="page-container">
    <UserUpdateHeader />

    <AptitudeInfoCard
      v-if="needFillAptitude.includes('sesameId')"
      title="芝麻分"
      :currentValue="form.sesameScore"
      :options="sesameScoreOptions"
      :isExpanded="expandCard === 'sesameScore'"
      identifier="sesameScore"
      :certificationStatusText="form.sesameScore ? '已认证' : '未认证'"
      @item-click="handleAptitudeItemClick"
      @expand-request="handleExpandRequest"
    />

    <AptitudeInfoCard
      v-if="needFillAptitude.includes('isOverdue')"
      title="信用额度"
      :currentValue="form.isOverdue"
      :options="overdueOptions"
      :isExpanded="expandCard === 'overdue'"
      identifier="overdue"
      :certificationStatusText="
        form.isOverdue === true || form.isOverdue === false ? '已认证' : '未认证'
      "
      @item-click="handleAptitudeItemClick"
      @expand-request="handleExpandRequest"
    />

    <WxInfoSection @update-aptitude-request="clickUpdateAptitude" />

    <UserUpdateFooter estimatedAmount="220,000" @submit-application="clickUpdateAptitude" />

    <FullScreenMatchLoading ref="loading" />
  </view>
</template>
<script>
import { nextFlowNode } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { getNeedFillAptitude, updateUserAptitude } from '@/apis/common'
import throttle from '@/utils/throttle'

import UserUpdateHeader from '@/components/hyh/userUpdate/UserUpdateHeader.vue'
import AptitudeInfoCard from '@/components/hyh/userUpdate/AptitudeInfoCard.vue'
import WxInfoSection from '@/components/hyh/userUpdate/WxInfoSection.vue'
import UserUpdateFooter from '@/components/hyh/userUpdate/UserUpdateFooter.vue'
import routeMap from '../packages/routeMap.js'

export default {
  onLoad({ param }) {
    if (param) {
      Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getNeedFillAptitude()
  },

  components: {
    FullScreenMatchLoading,
    UserUpdateHeader,
    AptitudeInfoCard,
    WxInfoSection,
    UserUpdateFooter
  },

  data() {
    return {
      form: {
        sesameScore: '',
        isOverdue: '',
        age: '' // 保留age字段，如果未来有年龄卡片
      },
      sesameScoreOptions: [
        { value: 110, label: '600分以下' },
        { value: 112, label: '600-650分' },
        { value: 113, label: '650-700分' },
        { value: 115, label: '700分以上' }
      ],
      overdueOptions: [
        { value: false, label: '无逾期' },
        { value: true, label: '有逾期' }
      ],
      expandCard: 'sesameScore', // 默认展开第一个需要填写的卡片
      needFillAptitude: []
    }
  },

  methods: {
    handleExpandRequest(identifier) {
      this.expandCard = identifier
    },

    handleAptitudeItemClick(item, identifier) {
      if (identifier === 'sesameScore') {
        this.form.sesameScore = item.value
        // 检查 'isOverdue' 是否是下一个需要填写的资质
        if (this.needFillAptitude.includes('isOverdue')) {
          this.expandCard = 'overdue'
        }
        // 如果还有其他资质，可以在这里添加更多逻辑来决定下一个展开的卡片
      } else if (identifier === 'overdue') {
        this.form.isOverdue = item.value
        // 如果点击的是最后一个卡片或者没有更多卡片，可以让 expandCard 保持不变或设置为 null
      }
      // 可以为其他类型的资质信息添加更多 else if 分支
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })

      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }

      if (nextFlow.flow === 'end') {
        if (this.vuex_halfJumpUrl) {
          window.location.href = this.vuex_halfJumpUrl
          return
        }
      }

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    async getNeedFillAptitude() {
      const res = await getNeedFillAptitude({
        channelId: this.form.channelId,
        uid: this.vuex_uid
      })
      this.needFillAptitude = res.data

      if (!this.needFillAptitude || this.needFillAptitude.length === 0) {
        this.navigateToNextFlow()
      }
    },

    validateForm() {
      if (this.needFillAptitude.includes('sesameId') && !this.form.sesameScore) {
        uni.showToast({
          title: '请填写芝麻分',
          icon: 'none'
        })
        return false
      }

      if (this.needFillAptitude.includes('isOverdue') && typeof this.form.isOverdue !== 'boolean') {
        uni.showToast({
          title: '请填写信用额度',
          icon: 'none'
        })
        return false
      }

      if (this.needFillAptitude.includes('age') && !this.form.age) {
        uni.showToast({
          title: '请填写年龄',
          icon: 'none'
        })
        return false
      }

      return true
    },

    createUpdateAptitudeParams() {
      const params = {
        uid: this.vuex_uid,
        sesameId: this.form.sesameScore,
        isOverdueId: this.form.isOverdue,
        age: this.form.age
      }

      if (!this.needFillAptitude.includes('sesameId')) {
        delete params.sesameId
      }

      if (!this.needFillAptitude.includes('isOverdue')) {
        delete params.isOverdueId
      }

      if (!this.needFillAptitude.includes('age')) {
        delete params.age
      }

      return params
    },

    clickUpdateAptitude() {
      throttle(async () => {
        if (!this.validateForm()) {
          return
        }

        uni.showLoading({
          title: '提交中...',
          mask: true
        })

        const params = this.createUpdateAptitudeParams()
        await updateUserAptitude(params)

        uni.hideLoading()

        this.navigateToNextFlow()
      }, 1000)
    }
  }
}
</script>
<style scoped lang="scss">
.page-container {
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/cd4f0a1927c1424680dc9deb206dcb9e.png);
  background-color: #f6f8fa;
  background-size: 100% auto;
  background-repeat: no-repeat;
  min-height: 100vh;
  // 子组件的特定样式已移至各自的文件中
  // 只保留页面容器本身的样式
}
</style>
