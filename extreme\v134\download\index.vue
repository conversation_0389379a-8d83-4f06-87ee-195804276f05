<script>
import Header from '@/components/header/header-jqgj.vue'
import Services from '@/components/services/index.vue'

export default {
  name: 'Download',
  components: { Header, Services }
}
</script>

<template>
  <view class="page-container" :style="{ height: '100vh' }">
    <view class="page-content">
      <Header />

      <image
        src="https://cdn.oss-unos.hmctec.cn/common/path/3b95a0be6d3f45439db8b580992af57d.png"
        class="feature-image"
      ></image>

      <Services />
    </view>
  </view>
</template>

<style scoped lang="scss">
.page-container {
  height: 100vh;
  height: 100dvh;
  background: #f6f6f8;
  display: flex;
  flex-direction: column;

  .page-content {
    padding: 30rpx;
    flex: 1;
    overflow: hidden auto;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 438rpx;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/8f99413b228c4ac5a7bd093592d05f6f.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transform: scale(3);
    }
  }
}

.header {
  position: relative;
  margin-bottom: 30rpx;
  display: flex;
  gap: 10rpx;
  align-items: center;

  .logo {
    width: 62rpx;
    height: 62rpx;
  }

  .name {
    margin-bottom: 4rpx;
    font-family:
      Alimama ShuHeiTi,
      Alimama ShuHeiTi;
    font-weight: 700;
    font-size: 28rpx;
    color: #16283c;
    line-height: 34rpx;
  }

  .desc {
    font-weight: 400;
    font-size: 18rpx;
    color: #16283c;
    line-height: 26rpx;
    letter-spacing: 1px;
  }
}

.feature-image {
  width: 100%;
  height: 634rpx;
}
</style>
