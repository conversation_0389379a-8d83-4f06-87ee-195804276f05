<template>
  <view :class="vuex_theme">
    <view class="page-container" v-show="vuex_theme">
      <AlertBar theme="orange" />

      <Header @click.native="navigateToIndex" />

      <view class="amount">
        <view class="title">您想最高借多少(元)</view>
        <view class="input-container">
          <input
            placeholder="请输入金额"
            v-model="form.demandAmount"
            @blur="demandAmountBlur"
            @focus="demandAmountFocus"
          />
          <view class="all-btn" @click="clickMaxAmount">全部借出</view>
        </view>
        <view class="slider-container">
          <slider
            :step="100 / 3"
            @change="demandAmountSliderChange"
            :value="form.demandAmountSlider"
            backgroundColor="#F7F7F7"
            block-size="13"
          />
          <view class="slider-tick">
            <text>5万</text>
            <text>10万</text>
            <text>15万</text>
            <text>20万</text>
          </view>
        </view>
        <view class="annual-interest-rate">
          <view class="tag">限时优惠</view>
          <text>
            参考年化利率
            <text class="highlight">12%</text>
            ,1000元1天仅需
            <text class="highlight">0.3</text>
            元
          </text>
        </view>
      </view>

      <view class="borrowing-options">
        <view class="option term">
          <view class="label">最长借多久</view>
          <picker
            range-key="label"
            :range="monthRange"
            :value="form.monthIndex"
            @change="monthPickerChange"
          >
            <view class="value">
              <view>{{ monthRange[this.form.monthIndex].value }}个月</view>
              <view class="recommend" v-if="monthRange[this.form.monthIndex].value === 12"
                >推荐</view
              >
              <uni-icons
                style="margin-left: 15rpx"
                color="#A2A3A5"
                type="right"
                size="16"
              ></uni-icons>
            </view>
          </picker>
        </view>
        <view class="option-line"></view>
        <view class="option repayment-method">
          <view class="label">如何还</view>
          <view class="value">
            <view class="repayment-amount">
              每月约应还
              <text class="amount-number">￥{{ monthlyPay }}</text>
            </view>
            <view class="repayment-reminder">实际贷款利息及放款金额以最终审批为准</view>
          </view>
        </view>
        <view class="option-line"></view>
        <view class="option coupon">
          <view class="label">优惠券</view>
          <view class="value">
            <view class="coupon-container">
              <view class="coupon-text">
                新用户借款享
                <text class="days">30天</text>
                免息
              </view>
              <view class="coupon-tips"> 若提前还清或逾期，本券将失效 </view>
            </view>
            <uni-icons color="#A2A3A5" type="right" size="16"></uni-icons>
          </view>
        </view>
      </view>

      <view class="phone-container">
        <template v-if="jumeiFlashValidateInit">
          <view class="get-my-quota" id="j-get-code" @click="openJumeiFlashValidate">一键验证</view>
        </template>
        <template v-else>
          <input
            type="tel"
            maxlength="11"
            class="phone-input"
            placeholder="输入手机号获取额度(已加密)"
            @blur="phoneBlur"
            v-model="form.phone"
            placeholder-style="color: #A2A3A5"
          />
          <view class="get-my-quota" @click="clickGetMyQuota">领取我的额度</view>
        </template>
        <view class="agreement" @click="isAgree = !isAgree">
          <view :class="['agree-icon', isAgree ? 'agree-icon-selected' : '']"></view>

          <view class="agreement-text">
            我已阅读并同意
            <text
              class="name"
              v-for="item in agreementList"
              :key="item.protocolId"
              @click="clickAgreement(item)"
            >
              《{{ item.name }}》
            </text>
          </view>
        </view>
      </view>

      <Declaration />

      <uni-popup
        background-color="#fff"
        ref="agreementPopup"
        type="bottom"
        border-radius="32rpx 32rpx 0 0"
      >
        <view class="agreement-popup">
          <view class="agreement-container">
            <view v-for="agreement in agreementList" :key="agreement.protocolId">
              <!--            <view class="agreement-title">{{ agreement.title }}</view>-->
              <view class="agreement-content">
                <div v-html="agreement.content"></div>
              </view>
            </view>
          </view>
          <view class="agreement-agree-container">
            <view class="agreement-agree-btn" @click="clickAgreeAndContinue">同意并继续</view>
          </view>
        </view>
      </uni-popup>

      <uni-popup
        :is-mask-click="false"
        background-color="#fff"
        ref="codePopup"
        type="center"
        border-radius="16rpx"
      >
        <view class="code-popup">
          <view class="code-popup-title">
            恭喜，已为您
            <text class="highlight">锁定借款名额</text>
          </view>
          <view class="code-input-container">
            <input
              maxlength="6"
              type="text"
              v-model="form.code"
              placeholder-style="color: #A2A3A5"
              placeholder="输入手机验证码"
            />
            <view class="gap-line"></view>
            <view class="get-code-btn">
              <template v-if="codeTimer">{{ codeCountdown }}s</template>
              <template v-else>
                <text @click="clickGetCode">获取</text>
              </template>
            </view>
          </view>
          <view class="get-amount-btn" @click="clickGetAmount">获取额度</view>

          <view class="close-btn" @click="$refs.codePopup.close()"></view>
        </view>
      </uni-popup>

      <uni-popup
        :is-mask-click="false"
        background-color="#fff"
        ref="remindPopup"
        type="center"
        border-radius="56rpx 56rpx 56rpx 56rpx"
      >
        <view class="remind-popup">
          <view class="remind-popup-title">确定要放弃以下限时特权吗？</view>
          <view class="remind-popup-desc">额度仅限今日领取</view>
          <view class="remind-popup-feature">
            <view class="remind-popup-feature-item">
              <view class="remind-popup-feature-icon"></view>
              <view class="remind-popup-feature-text">
                已匹配到产品的
                <text class="highlight">申请机会</text>
              </view>
            </view>
            <view class="remind-popup-feature-item">
              <view class="remind-popup-feature-icon"></view>
              <view class="remind-popup-feature-text">
                初审已通过，确认额度
                <text class="highlight">立即领取</text>
              </view>
            </view>
          </view>
          <view class="remind-popup-tips"
            >信息加密储存仅用于资质申请，平台承诺不会泄露您的任何信息</view
          >
          <view class="remind-popup-confirm" @click="$refs.remindPopup.close()">继续申请</view>
          <view class="remind-popup-cancel" @click="$refs.remindPopup.close()">狠心离开</view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>
<script>
import { validateMobile } from '@/utils/validators'
import AlertBar from '@/components/alert/AlertBar.vue'
import LockNativeBack from '@/utils/lock-native-back'
import { getTemplateConfig, reportUV } from '@/apis/common'
import { oneClick, saveLoan, sendSmsCode } from '@/apis/common-2'
import { encryptByDES } from '@/utils/encrypt'
import FingerprintJS from '@fingerprintjs/fingerprintjs'
import throttle from '@/utils/throttle'
import Header from '@/components/header/header-custom.vue'
import { formatAmount, parseAmount } from '@/utils/amount'
import { getBlackPhone, setBlackPhone } from '@/utils/black-phone'
import Declaration from '@/components/footer/declaration-component/declaration-custom.vue'
import { getAgreements } from '@/utils/agreement'

const fpPromise = FingerprintJS.load()

export default {
  name: 'template-v126',

  components: {
    AlertBar,
    Declaration,
    Header
  },

  data() {
    return {
      form: {
        demandAmount: '50,000',
        demandAmountSlider: 0,
        monthIndex: 2,
        phone: '',
        channelId: ''
      },
      presetValues: [0, 33.33, 66.66, 100], // 对应 5万、10万、15万、20万
      presetAmounts: [50000, 100000, 150000, 200000],
      monthRange: [
        {
          label: '3个月',
          value: 3
        },
        {
          label: '6个月',
          value: 6
        },
        {
          label: '12个月',
          value: 12
        },
        {
          label: '36个月',
          value: 36
        }
      ],
      monthlyPay: '',
      isAgree: false,
      codeTimer: null,
      codeCountdown: 0,
      lockBack: null,
      agreementList: [],
      jumeiFlashValidate: null,
      jumeiFlashValidateInit: false,
      jumeiFlashValidateToken: ''
    }
  },

  props: {
    channelId: {
      type: [String, Number],
      default: ''
    }
  },

  beforeDestroy() {
    if (this.codeTimer) {
      clearInterval(this.codeTimer)
      this.codeTimer = null
    }

    if (this.lockBack) {
      this.lockBack.unLock()
      this.lockBack = null
    }
  },

  mounted() {
    if (this.channelId) {
      this.form.channelId = this.channelId
      reportUV({ channelId: this.channelId })
      this.getTemplateConfig()
    }
    this.fetchAgreement()
    this.updateAmountUI()
    // 初始化 LockNativeBack 实例
    this.lockBack = new LockNativeBack({
      onPopState: () => {
        // 当浏览器历史发生变化时，你可以在这里执行自定义的逻辑
        this.$refs.remindPopup.open()
      }
    })
    this.lockBack.lock()
    this.jumeiFlashValidate = new JumeiFlashValidate('123')
    this.jumeiFlashValidate.init((res) => {
      if (res.code == '000000') {
        this.jumeiFlashValidateInit = true
      } else {
        this.jumeiFlashValidateInit = false
      }
    })
  },

  methods: {
    async getTemplateConfig() {
      const res = await getTemplateConfig({
        channelId: this.form.channelId
      })

      if (res.code == 200) {
        // TODO
        // this.$u.vuex('vuex_theme', res.data.theme)
        this.$u.vuex('vuex_theme', 'fortune-blue')
        this.$u.vuex('vuex_templateConfig', res.data)
      } else {
        this.$u.vuex('vuex_theme', '')
        this.$u.vuex('vuex_templateConfig', {})
      }
    },

    openJumeiFlashValidate() {
      this.jumeiFlashValidate.open(function (res) {
        // 成功
        if (res.code == '200000') {
          console.log(res.token)
        }
      })
    },

    async fetchAgreement() {
      // 注册页协议
      this.agreementList = await getAgreements(this.vuex_templateConfig.agreementKeyIndex)
    },

    clickGetMyQuota() {
      throttle(this.getMyQuotaHandler)
    },

    getMyQuotaHandler() {
      if (!this.form.phone) {
        uni.showToast({
          title: '请输入手机号',
          icon: 'none'
        })
        return
      }

      if (!validateMobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
        return
      }

      if (!this.isAgree) {
        this.$refs.agreementPopup.open()
        this.$forceUpdate()
        return
      }

      this.$refs.codePopup.open()
      // 打开验证码弹窗时，自动获取验证码
      if (!this.codeTimer) {
        this.clickGetCode()
      }
    },

    clickAgreement(agreement) {
      this.$refs.agreementPopup.open()
      this.$forceUpdate()
    },

    phoneBlur() {
      if (this.form.phone && !this.$u.test.mobile(this.form.phone)) {
        uni.showToast({
          title: '请输入正确的手机号',
          icon: 'none'
        })
      }
    },

    clickGetCode() {
      this.getCodeHandler()
    },

    async getCodeHandler() {
      const res = await sendSmsCode({
        phone: this.form.phone,
        channelId: this.form.channelId
      })

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '发送失败',
          icon: 'none'
        })
        return
      }

      uni.showToast({
        title: '发送成功',
        icon: 'none'
      })

      this.codeCountdown = 60
      if (this.codeTimer) {
        clearInterval(this.codeTimer)
        this.codeTimer = null
      }
      this.codeTimer = setInterval(() => {
        this.codeCountdown--
        if (this.codeCountdown <= 0) {
          clearInterval(this.codeTimer)
          this.codeTimer = null
        }
      }, 1000)
    },

    demandAmountSliderChange({ detail }) {
      const sliderValue = detail.value

      // 找出最近的预设值
      const closestPreset = this.presetValues.reduce((prev, curr) =>
        Math.abs(curr - sliderValue) < Math.abs(prev - sliderValue) ? curr : prev
      )

      // 更新金额
      const amountIndex = this.presetValues.indexOf(closestPreset)
      const amount = this.presetAmounts[amountIndex]
      this.form.demandAmount = formatAmount(amount)
      this.updateAmountUI()
    },

    clickMaxAmount() {
      this.form.demandAmount = '200,000'
      this.updateAmountUI()
    },

    computedMonthPay() {
      let price = Number(parseAmount(this.form.demandAmount))
      let mLatte = (price * 12) / 100 / 12
      const month = this.monthRange[this.form.monthIndex].value

      this.monthlyPay = ((price + mLatte * month) / month).toFixed(2)
    },

    updateAmountUI() {
      this.setSliderValue(parseAmount(this.form.demandAmount))
      this.computedMonthPay()
    },

    demandAmountBlur() {
      const amount = parseInt(this.form.demandAmount)
      const isNotNumber = isNaN(amount)
      // 只允许输入 1万的整数倍
      const isNotValid = amount % 10000 !== 0

      if (isNotNumber || isNotValid) {
        uni.showToast({
          title: '请输入万的整数倍',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(50000)
        this.updateAmountUI()
        return
      }

      // 允许的范围是 50000～200000
      if (amount < 50000) {
        uni.showToast({
          title: '最低借款金额为5万',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(50000)
        this.updateAmountUI()
        return
      }

      if (amount > 200000) {
        uni.showToast({
          title: '最高借款金额为20万',
          icon: 'none'
        })
        this.form.demandAmount = formatAmount(200000)
        this.updateAmountUI()
        return
      }

      this.form.demandAmount = formatAmount(amount)
      this.updateAmountUI()
    },

    demandAmountFocus() {
      this.form.demandAmount = parseAmount(this.form.demandAmount)
    },

    // 根据传入的金额，设置滑块的值
    setSliderValue(amount) {
      if (amount <= this.presetAmounts[0]) {
        this.form.demandAmountSlider = this.presetValues[0]
      } else if (amount >= this.presetAmounts[this.presetAmounts.length - 1]) {
        this.form.demandAmountSlider = this.presetValues[this.presetValues.length - 1]
      } else {
        for (let i = 1; i < this.presetAmounts.length; i++) {
          if (amount <= this.presetAmounts[i]) {
            const lowerAmount = this.presetAmounts[i - 1]
            const upperAmount = this.presetAmounts[i]
            const lowerValue = this.presetValues[i - 1]
            const upperValue = this.presetValues[i]

            const ratio = (amount - lowerAmount) / (upperAmount - lowerAmount)
            this.form.demandAmountSlider = lowerValue + ratio * (upperValue - lowerValue)
            break
          }
        }
      }
    },

    monthPickerChange({ detail }) {
      this.form.monthIndex = detail.value
      this.updateAmountUI()
    },

    clickAgreeAndContinue() {
      this.$refs.agreementPopup.close()
      this.isAgree = true
      this.clickGetMyQuota()
    },

    clickGetAmount() {
      throttle(() => {
        if (!this.form.code) {
          uni.showToast({
            title: '请输入验证码',
            icon: 'none'
          })
          return
        }

        if (this.jumeiFlashValidateToken) {
          this.oneClick()
        } else {
          this.login()
        }
      })
    },

    async oneClick() {
      const params = this.getLoginParams()
      const res = await oneClick(params)
      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '一键验证失败',
          icon: 'none'
        })

        this.jumeiFlashValidateInit = false
        this.jumeiFlashValidateToken = ''

        return
      }

      // 登录成功后逻辑，与login方法一致但排除存储黑名单手机号
      const urlParam = {
        consumerId: res.data,
        demandAmount: parseAmount(this.form.demandAmount),
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      this.$u.vuex('vuex_consumerId', res.data)
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v126/auth/index?param=${urlParamString}`
      })
    },

    async getVisitorId() {
      const fp = await fpPromise
      const { visitorId } = await fp.get()
      if (!uni.getStorageSync('browser')) {
        uni.setStorageSync('browser', visitorId)
      }
      return visitorId
    },

    async getLoginParams() {
      const params = {}

      params.phoneBlack = getBlackPhone()
      // params.h5UaUuid = await this.getVisitorId();
      params.deviceType = uni.getSystemInfoSync().platform
      params.demandAmount = parseAmount(this.form.demandAmount)

      if (this.jumeiFlashValidateToken) {
        params.token = this.jumeiFlashValidateToken
      } else {
        params.phone = this.form.phone
        params.code = this.form.code
      }

      params.channelId = this.form.channelId

      return params
    },

    async login() {
      const params = await this.getLoginParams()

      const res = await saveLoan(params)

      if (res.code != 200) {
        uni.showToast({
          title: res.msg || '操作失败',
          icon: 'none'
        })
        return
      }

      // 登录成功后，记录手机号
      setBlackPhone(params.phone)
      this.$u.vuex('vuex_phone', params.phone)

      const urlParam = {
        consumerId: res.data,
        phone: this.form.phone,
        demandAmount: parseAmount(this.form.demandAmount),
        channelId: this.form.channelId,
        monthIndex: this.form.monthIndex
      }
      this.$u.vuex('vuex_consumerId', res.data)
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `/extreme/v126/auth/index?param=${urlParamString}`
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '@/theme/v126/export/index';
</style>
