<template>
  <view class="page-container">
    <view class="content">
      <view class="title">您的专属领取码</view>

      <view class="code">{{ exclusiveCode }}</view>
    </view>

    <view class="step-container">
      <view class="step-item">
        <image class="step-item-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/90418c122373494cb04b84084adbf019.png" mode="scaleToFill" />
        <text class="step-item-text">复制链接到浏览器打开</text>
      </view>
      <view class="step-item">
        <image class="step-item-icon"
          src="https://cdn.oss-unos.hmctec.cn/common/path/51770604591f48f18de597c886496ffb.png" mode="scaleToFill" />
        <text class="step-item-text">
          确认身份信息，领取您的
          <text class="step-item-text-bold">专属额度</text>
        </text>
      </view>
    </view>

    <view class="link-container">
      <view class="link-desc">复制链接到【浏览器】打开领取额度</view>
      <view class="link-url">
        <image src="https://cdn.oss-unos.hmctec.cn/common/path/2cd3cdc681324c04becd794779f2fa02.png" class="url-icon" />
        <text class="url-text" selectable="true">{{ shortLink || '链接加载中...' }}</text>

        <image
          src="https://cdn.oss-unos.hmctec.cn/common/path/47674047622e479abb6806155059ec5a.png"
          mode="scaleToFill"
          class="copy-tips"
        />
      </view>
      <view class="copy-btn" @click="copyShortLinkHandler">复制链接</view>
    </view>

    <FullScreenMatchLoading ref="loading" />

    <!-- 复制成功弹窗 -->
    <uni-popup
      ref="copySuccessPopup"
      type="center"
      :is-mask-click="false"
      background-color="transparent"
    >
      <view class="copy-success-popup">
        <image
          class="popup-main-image"
          src="https://cdn.oss-unos.hmctec.cn/common/path/db835c5700a84f0e95f4f49d43e56db8.png"
          mode="scaleToFill"
        />
        <image
          class="popup-close-btn"
          src="https://cdn.oss-unos.hmctec.cn/common/path/603a5777cf044cabb384d00a16fbfa15.png"
          mode="scaleToFill"
          @click="closeCopySuccessPopup"
        />
      </view>
    </uni-popup>
  </view>
</template>

<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { matchingOnlineProduct } from '@/apis/common'
import { isPageNested } from '@/utils/iframe'
import { savePageNestingOpenRecord } from '@/apis/common-3'
import { generateShortLink, copyShortLink } from '@/apis/common-2'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'wechatLinkIndex',
  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      productList: [],
      shortLink: '',
      exclusiveCode: ''
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    // 生成随机四位数专属领取码
    this.generateExclusiveCode()
  },

  async onShow() {
    this.getFlowData()

    if (this.productList.length === 0) {
      try {
        await this.fetchProduct()
      } catch (error) {
        console.error('获取产品数据失败:', error)
        // 如果获取产品失败，直接进入下一步流程
        this.navigateToNextFlow()
        return
      }
    }

    if (this.productList.length > 0) {
      if (isPageNested()) {
        await savePageNestingOpenRecord({
          appUserId: this.form.consumerId,
          channelId: this.form.channelId,
          deviceType: uni.getSystemInfoSync().osName,
          browser: uni.getSystemInfoSync().browserName
        })
      }
    } else {
      // 如果没有产品数据，自动进入下一步流程
      this.navigateToNextFlow()
    }

    // 获取短链接
    await this.fetchShortLink()
  },

  methods: {
    // 生成随机四位数专属领取码
    generateExclusiveCode() {
      // 生成1000-9999之间的随机四位数
      this.exclusiveCode = Math.floor(Math.random() * 9000) + 1000
    },

    async getFlowData() {
      const flowData = getFlowData('wechat_link')
      if (flowData) {
        this.productList = flowData.productList

        if (!this.form.consumerId) {
          this.form.consumerId = this.productList[0].consumerId
        }

        this.$u.vuex('vuex_consumerId', this.form.consumerId)
      } else {
        this.productList = []
      }
    },

    async fetchProduct() {
      const res = await matchingOnlineProduct({
        channelId: this.form.channelId,
        consumerId: this.form.consumerId,
        matchType: 1
      })

      if (res.data) {
        this.productList = res.data
      } else {
        this.productList = []
      }
    },

    async fetchShortLink() {
      try {
        const res = await generateShortLink({
          consumerId: this.form.consumerId,
          channelId: this.form.channelId
        })

        if (res.code === 200 && res.data) {
          this.shortLink = res.data
        } else {
          console.error('获取短链接失败:', res.msg || '获取短链接失败')
        }
      } catch (error) {
        console.error('获取短链接接口调用失败:', error)
      }
    },

    copyShortLinkHandler() {
      if (!this.shortLink) {
        uni.showToast({
          title: '链接获取中，请稍后',
          icon: 'none'
        })
        return
      }

      // 复制到剪贴板
      uni.setClipboardData({
        data: this.shortLink,
        showToast: false,
        success: () => {
          // 显示复制成功弹窗
          this.$refs.copySuccessPopup.open()

          // 埋点上报
          this.reportCopyShortLink()
        },
        fail: (error) => {
          console.error('复制链接失败:', error)
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          })
        }
      })
    },

    async reportCopyShortLink() {
      try {
        await copyShortLink({
          consumerId: this.form.consumerId,
          channelId: this.form.channelId
        })
      } catch (error) {
        console.error('复制短链接埋点上报失败:', error)
      }
    },

    // 关闭复制成功弹窗
    closeCopySuccessPopup() {
      this.$refs.copySuccessPopup.close()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()

      const customFetch = {
        wechat: matchingOnlineProduct,
        wechat_link: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch
      )

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #F3F5F9;
}

.content {
  padding-top: 400rpx;
  background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/84bfb7268a28427195c202d8bba44e00.png);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  height: 760rpx;

  .title {
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 48rpx;
    text-align: center;
  }

  .code {
    margin: 50rpx auto 0;
    width: 520rpx;
    height: 110rpx;
    background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/ee83a763053644e9a0eeab74678426f4.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    font-family: DIN, DIN;
    font-weight: 500;
    font-size: 90rpx;
    color: #333333;
    letter-spacing: 32px;
    text-align: center;
  }
}

.step-container {
  margin: 0 auto 40rpx;
  width: fit-content;

  .step-item {

    margin-bottom: 25rpx;
    display: flex;
    align-items: center;
    gap: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .step-item-icon {
      width: 52rpx;
      height: 48rpx;
    }

    .step-item-text {

      font-weight: 500;
      font-size: 34rpx;
      color: #333333;
      line-height: 51rpx;

      .step-item-text-bold {
        color: #FF8015;
      }
    }
  }
}

.link-container {
  margin: 0 30rpx 50rpx;
  background: linear-gradient(180deg, #DEF6F1 0%, #FFFFFF 42%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  padding: 40rpx 30rpx 60rpx;

  .link-desc {
    margin-top: 25rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #3D3D3D;
    line-height: 54rpx;
  }

  .link-url {
    position: relative;
    display: flex;
    align-items: center;
    margin-top: 100rpx;
    gap: 10rpx;
    height: 72rpx;
    padding: 15rpx 20rpx;
    background: #F4F6F3;
    border-radius: 16rpx 16rpx 16rpx 16rpx;

    .copy-tips {
      position: absolute;
      left: 0;
      top: -100%;
      width: 332rpx;
      height: 70rpx;
    }

    .url-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 10rpx;
    }

    .url-text {
      font-size: 28rpx;
      color: #757780;
      line-height: 39rpx;
      min-width: 100rpx;
      max-width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }


  .copy-btn {
    margin: 40rpx auto ;
    width: 614rpx;
    height: 100rpx;
    background: #01CC83;
    border-radius: 507rpx 507rpx 507rpx 507rpx;
    font-size: 40rpx;
    color: #FFFFFF;
    line-height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

/* 复制成功弹窗样式 */
.copy-success-popup {
  position: relative;
  width: 567rpx;
  height: 543rpx;

  .popup-main-image {
    width: 567rpx;
    height: 543rpx;
  }

  .popup-close-btn {
    position: absolute;
    top: 40rpx;
    right: 30rpx;
    width: 30rpx;
    height: 30rpx;
  }
}
</style>
