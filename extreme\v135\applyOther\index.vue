<template>
  <view class="page-container">
    <header-section />
    <main-product :product="mainProduct" @apply="handleMainProductApply" />
    <notification-bar :message="notificationMessage" />
    <product-list :productList="productList" @apply="handleProductApply" />
    <Loading ref="loading" />

    <Declaration />
  </view>
</template>

<script>
import HeaderSection from '@/components/dai-yue-tong/applyOther/HeaderSection.vue'
import MainProduct from '@/components/dai-yue-tong/applyOther/MainProduct.vue'
import NotificationBar from '@/components/dai-yue-tong/applyOther/NotificationBar.vue'
import ProductList from '@/components/dai-yue-tong/applyOther/ProductList.vue'
import Loading from '@/components/dai-yue-tong/halfApiTransitStation/loading.vue'
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct } from '@/apis/common-2'
import { matchingOnlineProduct } from '@/apis/common'
import Declaration from '@/components/footer/declaration-component/declaration-hls2.vue'

export default {
  name: 'applyOther',

  components: {
    HeaderSection,
    MainProduct,
    NotificationBar,
    ProductList,
    Loading,
    Declaration
  },

  data() {
    return {
      form: {},
      notificationMessage: '183****4565已申请四款产品，成功获得额度',
      productList: [],
      mainProduct: {}
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getFlowData()
  },

  methods: {
    async getFlowData() {
      const flowData = getFlowData('overloan')
      if (flowData) {
        this.mainProduct = flowData.productList[0]
        this.productList = flowData.productList.slice(1)
      }
    },

    handleMainProductApply() {
      throttle(() => {
        this.applyProduct(this.mainProduct)
      })
    },

    handleProductApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
        window.location.href = res.data

        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const routeMap = {
        wechat: '/extreme/v135/qw/index',
        overloan: '/extreme/v135/applyOther/index',
        end: '/extreme/v135/download/index',
        wechat_official_account: '/extreme/v135/wechatOfficialAccount/index'
      }

      const customFetch = {
        wechat: matchingOnlineProduct,
        overloan: matchingOnlineProduct
      }

      const customParams = {
        wechat: {
          matchType: 1
        },
        overloan: {
          matchType: 2
        }
      }

      const nextFlow = await nextFlowNode(
        {
          templateVersion: this.form.templateVersion,
          channelId: this.form.channelId,
          consumerId: this.form.consumerId
        },
        customFetch,
        customParams
      )

      this.$refs.loading.close()

      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page-container {
  padding-top: 80rpx;
  min-height: 100vh;
  background: linear-gradient(180deg, rgba(184, 254, 255, 0.43) 0%, #f3f8f9 36%, #f3f8f9 100%);
}
</style>
