<template>
  <view class="product-list">
    <product-list-item
      v-for="(item, index) in productList"
      :key="index"
      :product="item"
      @apply="onProductApply"
    />
  </view>
</template>

<script>
import ProductListItem from './ProductListItem.vue'

export default {
  name: 'ProductList',
  components: {
    ProductListItem
  },
  props: {
    productList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    onProductApply(product) {
      this.$emit('apply', product)
    }
  }
}
</script>

<style scoped lang="scss">
.product-list {
  width: 686rpx;
  margin: 32rpx auto 0;
}
</style>
