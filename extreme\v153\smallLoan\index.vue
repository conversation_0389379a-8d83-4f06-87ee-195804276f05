<template>
  <view class="page-container">
    <!-- 顶部成功提示区域 -->
    <view class="success-header">
      <view class="success-title">恭喜您，申请成功！</view>
      <view class="success-subtitle">机构即将致电审核，未接电话可能会审核失败</view>
    </view>

    <!-- 产品推荐和列表容器 -->
    <view class="product-container">
      <!-- 产品推荐说明 -->
      <view class="recommendation-tip">
        <image
          class="tip-icon-left"
          src="https://cdn.oss-unos.hmctec.cn/common/path/057eaae8eb9244be9a7d5abdd0e23503.png"
        ></image>
        <text class="tip-text">由于您的资质优秀，继续为您匹配更优产品</text>
        <image
          class="tip-icon-right"
          src="https://cdn.oss-unos.hmctec.cn/common/path/e6c82df6837440689b7c9fd9f4f18547.png"
        ></image>
      </view>

      <!-- 产品列表 -->
      <view class="product-list">
        <!-- 产品卡片 -->
        <view class="product-card" v-for="(item, index) in productList" :key="index">
          <!-- 产品头部 -->
          <view class="product-header">
            <image class="product-logo" :src="item.logo" />
            <view class="product-name">{{ item.name }}</view>
          </view>

          <!-- 产品标签 -->
          <view class="product-tags">
            <text class="tag">额度高</text>
            <text class="tag">放款快</text>
            <text class="tag">利息低</text>
          </view>

          <!-- 产品信息 -->
          <view class="product-info">
            <view class="info-item">
              <view class="info-label">最高额度(元)</view>
              <view class="info-value amount-value">{{ item.loanableFundsBig | toThousandFilter }}</view>
            </view>
            <view class="info-item">
              <view class="info-label">最低年化利率</view>
              <view class="info-value rate-value">{{ item.interestRateLittle | toPriceAbbreviations }}%</view>
            </view>
          </view>

          <!-- 申请按钮 -->
          <view class="apply-button" @click="clickApply(item)">
            <text class="apply-text">立即申请</text>
          </view>

          <!-- 底部状态栏 -->
          <view class="product-status">
            <view class="status-item">
              <image
                class="status-icon"
                src="https://cdn.oss-unos.hmctec.cn/common/path/3aed53f3f4364b408329ed46c7bf884c.png"
              />
              <text class="status-text">额度热抢中</text>
            </view>
            <view class="countdown">
              <text class="countdown-label">限时领取</text>
              <view class="countdown-timer">
                <text class="time-number">{{ formatTime(countdownMinutes) }}</text>
                <text class="time-separator">:</text>
                <text class="time-number">{{ formatTime(countdownSeconds) }}</text>
              </view>
              <text class="countdown-label">后结束</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <FullScreenMatchLoading ref="loading" />

    <!-- 跳转弹窗 -->
    <uni-popup ref="jumpPopup" type="center" :is-mask-click="false">
      <view class="jump-popup-container">
        <view class="jump-popup-title">即将跳转到</view>
        <view class="jump-popup-confirm-btn" @click="confirmJump">立即跳转</view>
        <view class="jump-popup-countdown" v-if="countdown > 0">{{ countdown }}s后关闭</view>
      </view>
    </uni-popup>
  </view>
</template>
  
<script>
import { decryptByDES, encryptByDES } from '@/utils/encrypt'
import throttle from '@/utils/throttle'
import { getFlowData, nextFlowNode } from '@/utils/processFlowFunctions'
import { applyOnlineProduct } from '@/apis/common-2'
import FullScreenMatchLoading from '@/components/overlay/FullScreenMatchLoading.vue'
import { getDcAutoFlag } from '@/apis/common'
import routeMap from '../packages/routeMap.js'

export default {
  name: 'smallLoan',

  components: {
    FullScreenMatchLoading
  },

  data() {
    return {
      form: {},
      productList: [],
      countdown: 5,
      timer: null,
      countdownMinutes: 5,
      countdownSeconds: 0,
      productCountdownTimer: null
    }
  },

  onLoad({ param }) {
    if (param) {
      this.form = Object.assign(this.form, decryptByDES(decodeURIComponent(param)))
    }

    this.getFlowData()

    if (this.productList.length > 0 && this.productList[0].platformId && this.productList[0].id) {
      this.getDcAutoFlag()
    }

    // 启动5分钟倒计时
    this.startProductCountdown()
  },

  mounted() {
    if (this.vuex_halfJumpUrl) {
      this.openJumpPopup()
    }
  },

  methods: {
    async getDcAutoFlag() {
      const res = await getDcAutoFlag({
        platformId: this.productList[0].platformId,
        productId: this.productList[0].id
      })
      // data = 1 不自动 ，data = 2 自动
      if (res.data == 2) {
        this.applyProduct(this.productList[0])
      }
    },

    async getFlowData() {
      const flowData = getFlowData('small_loan')
      if (flowData) {
        this.productList = flowData.productList || []
      }
    },

    clickApply(product) {
      throttle(() => {
        this.applyProduct(product)
      })
    },

    async applyProduct(product) {
      uni.showLoading({
        title: '正在申请产品',
        mask: true
      })
      const res = await applyOnlineProduct({
        productId: product.id,
        consumerId: this.form.consumerId
      })

      uni.hideLoading()

      if (res.data) {
        // 设置 url 到 vuex
        this.$u.vuex('vuex_overloanWebview.url', res.data)
        // 跳转到 overloan-webview 页面
        const urlParam = this.form
        const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
        uni.navigateTo({
          url: `/extreme/v153/overloanWebview/index?param=${urlParamString}`
        })
        return
      }

      this.navigateToNextFlow()
    },

    async navigateToNextFlow() {
      this.$refs.loading.open()
      const nextFlow = await nextFlowNode({
        templateVersion: this.form.templateVersion,
        channelId: this.form.channelId,
        consumerId: this.form.consumerId
      })
      if (nextFlow.flow === 'halfapi') {
        this.$u.vuex('vuex_webviewDown.url', nextFlow.url)
      }
      const path = routeMap[nextFlow.flow]
      const urlParam = this.form
      const urlParamString = encodeURIComponent(encryptByDES(JSON.stringify(urlParam)))
      this.$refs.loading.close()
      uni.navigateTo({
        url: `${path}?param=${urlParamString}`
      })
    },

    openJumpPopup() {
      this.$refs.jumpPopup.open()
      this.startCountdown()
    },

    startCountdown() {
      this.countdown = 5
      if (this.timer) {
        clearInterval(this.timer)
      }
      this.timer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        }
        if (this.countdown === 0) {
          clearInterval(this.timer)
          this.$refs.jumpPopup.close()
        }
      }, 1000)
    },

    confirmJump() {
      clearInterval(this.timer)
      this.$refs.jumpPopup.close()
      if (this.vuex_halfJumpUrl) {
        window.location.href = this.vuex_halfJumpUrl
      }
    },

    // 启动产品倒计时（5分钟）
    startProductCountdown() {
      this.countdownMinutes = 5
      this.countdownSeconds = 0

      if (this.productCountdownTimer) {
        clearInterval(this.productCountdownTimer)
      }

      this.productCountdownTimer = setInterval(() => {
        if (this.countdownSeconds > 0) {
          this.countdownSeconds--
        } else if (this.countdownMinutes > 0) {
          this.countdownMinutes--
          this.countdownSeconds = 59
        } else {
          // 倒计时结束，停止计时
          clearInterval(this.productCountdownTimer)
          this.productCountdownTimer = null
        }
      }, 1000)
    },

    // 格式化时间显示（补零）
    formatTime(time) {
      return time.toString().padStart(2, '0')
    }
  },

  beforeDestroy() {
    clearInterval(this.timer)
    if (this.productCountdownTimer) {
      clearInterval(this.productCountdownTimer)
    }
  }
}
</script>

  <style lang="scss" scoped>
  /* 页面容器 */
  .page-container {
    // 页面整体容器样式
    min-height: 100vh;
    background-color: #f2f2f2;
  }

  /* 顶部成功提示区域 */
  .success-header {
    // 成功提示区域背景和布局
    background-image: url(https://cdn.oss-unos.hmctec.cn/common/path/dae63a27e2804d3298b8c38c19ef5143.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 380rpx;
    padding: 50rpx 30rpx;
  }

  .success-title {
    // 主标题样式
    color: #fff;
    font-weight: 500;
    font-size: 58rpx;
    line-height: 84rpx;
  }

  .success-subtitle {
    // 副标题样式
    color: #fff;
    font-weight: 400;
    font-size: 24rpx;
    line-height: 35rpx;
  }

  /* 产品推荐和列表容器 */
  .product-container {
    background: #f2f2f2;
    border-radius: 30rpx 30rpx 0rpx 0rpx;
    margin-top: -150rpx;
  }

  /* 产品推荐说明 */
  .recommendation-tip {
    // 推荐提示容器样式
    padding: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12rpx;
  }

  .tip-icon-left {
    // 提示图标样式
    width: 35rpx;
    height: 9rpx;
  }

  .tip-text {
    // 提示文字样式

    font-weight: normal;
    font-size: 24rpx;
    color: #3d3d3d;
    line-height: 34rpx;
  }

  .tip-icon-right {
    width: 35rpx;
    height: 9rpx;
  }

  /* 产品列表 */
  .product-list {
    // 产品列表容器样式
    padding: 0 30rpx;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }

  /* 产品卡片 */
  .product-card {
    // 产品卡片容器样式
    padding: 35rpx 20rpx;
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
  }

  /* 产品头部 */
  .product-header {
    // 产品头部布局
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20rpx;
  }

  .product-logo {
    // 产品logo容器
    width: 48rpx;
    height: 48rpx;
  }

  .product-name {
    // 产品名称样式

    font-weight: 500;
    font-size: 36rpx;
    color: #171a1d;
    line-height: 54rpx;
  }

  /* 产品标签 */
  .product-tags {
    // 标签容器布局
    margin-top: 18rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
  }

  .tag {
    // 标签基础样式
    padding: 5rpx 15rpx;
    background: #FFF0E4;
    border-radius: 4rpx 4rpx 4rpx 4rpx;
    font-weight: normal;
    font-size: 20rpx;
    color: #d98e4d;
    line-height: 28rpx;
  }

  /* 产品信息 */
  .product-info {
    // 产品信息容器布局
    margin-top: 30rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 150rpx;
  }

  .info-item {
    // 信息项布局
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15rpx;
  }

  .info-label {
    // 信息标签样式

    font-weight: normal;
    font-size: 24rpx;
    color: #919094;
    line-height: 34rpx;
  }

  .info-value {
    // 信息值样式

    font-family: D-DIN, D-DIN;
    font-weight: 700;
    font-size: 58rpx;

    line-height: 63rpx;
  }

  .rate-value {
    // 利率值特殊样式
    color: #171a1d;
  }

  .amount-value {
    // 金额值特殊样式
    color: #1678FF ;
  }

  /* 申请按钮 */
  .apply-button {
    // 申请按钮样式
    margin-top: 40rpx;
    padding: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #1678FF;
    border-radius: 723rpx 723rpx 723rpx 723rpx;
  }

  .apply-text {
    // 按钮文字样式

    font-weight: 400;
    font-size: 40rpx;
    color: #ffffff;
    line-height: 58rpx;
  }

  /* 产品状态栏 */
  .product-status {
    // 状态栏布局
    margin-top: 37rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 10rpx;
    padding: 20rpx;
    background-color: #f2f4f5;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
  }

  .status-item {
    // 状态项布局
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
  }

  .status-icon {
    // 状态图标样式
    width: 29rpx;
    height: 29rpx;
  }

  .status-text {
    // 状态文字样式

    font-weight: 400;
    font-size: 24rpx;
    color: #6a7a96;
    line-height: 35rpx;
  }

  /* 倒计时 */
  .countdown {
    // 倒计时容器布局
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 15rpx;
  }

  .countdown-label {
    // 倒计时标签样式

    font-weight: 400;
    font-size: 24rpx;
    color: #6a7a96;
    line-height: 35rpx;
  }

  .countdown-timer {
    // 倒计时数字容器
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10rpx;
  }

  .time-number {
    // 时间数字样式
    width: 38rpx;
    height: 38rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #020202;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    font-weight: normal;
    font-size: 24rpx;
    color: #ffffff;
    line-height: 34rpx;
  }

  .time-separator {
    // 时间分隔符样式

    font-weight: 400;
    font-size: 24rpx;
    color: #020202;
    line-height: 35rpx;
  }

  /* 跳转弹窗样式 */
  .jump-popup-container {
    position: relative;
    padding: 345rpx 70rpx 50rpx;
    width: 610rpx;
    background: linear-gradient(180deg, #ffc0c9 0%, #ffffff 50%);
    border-radius: 16rpx 16rpx 16rpx 16rpx;

    .jump-popup-countdown {
      position: absolute;
      top: 30rpx;
      right: 20rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #ffffff;
      line-height: 48rpx;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-image: url('https://cdn.oss-unos.hmctec.cn/common/path/5b30e383ab2a4be79d3865467dc5bac8.png');
      background-size: 550rpx 405rpx;
      background-repeat: no-repeat;
      background-position: center top;
    }

    .jump-popup-title {
      position: relative;
      margin-bottom: 50rpx;
      font-weight: 500;
      font-size: 38rpx;
      color: #3d3d3d;
      line-height: 48rpx;
      text-align: center;
    }

    .jump-popup-confirm-btn {
      position: relative;
      margin-bottom: 15rpx;
      padding: 23rpx;
      background: #1678FF;
      border-radius: 47rpx 47rpx 47rpx 47rpx;
      font-weight: 500;
      font-size: 38rpx;
      color: #ffffff;
      line-height: 48rpx;
      text-align: center;
    }
  }
  </style>
